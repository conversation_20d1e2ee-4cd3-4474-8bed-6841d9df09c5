<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Question Paper Creator - ${bookTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/assets/wpmain/aiContent.css">
    <style>
        .qp-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }
        
        .qp-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #333;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .qp-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .qp-step {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #28a745;
        }
        
        .qp-step.hidden {
            display: none;
        }
        
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .step-number {
            background: #28a745;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .step-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            color: #333;
            background-color: #fff;
            min-height: 48px;
            line-height: 1.5;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        select.form-control {
            font-size: 1rem;
            color: #333;
            background-color: #fff;
            min-height: 48px;
            line-height: 1.5;
            padding: 12px 16px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }

        select.form-control option {
            color: #333;
            background-color: #fff;
            font-size: 1rem;
            padding: 8px 12px;
            line-height: 1.5;
        }
        
        .chapters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .chapter-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .chapter-item:hover {
            background: #e9ecef;
        }
        
        .chapter-item.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .chapter-checkbox {
            margin-right: 12px;
            transform: scale(1.2);
        }
        
        .chapter-name {
            font-weight: 500;
            color: #333;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-block;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: #007bff !important;
            color: white !important;
            border: 1px solid #007bff;
        }

        .btn-primary:hover {
            background: #0056b3 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            border: 1px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-success {
            background: #28a745 !important;
            color: white !important;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #1e7e34 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .select-all-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .sections-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .section-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        
        .section-header {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .section-fields {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 2fr 1fr;
            gap: 15px;
            align-items: end;
        }
        
        @media (max-width: 768px) {
            .section-fields {
                grid-template-columns: 1fr;
            }
            
            .chapters-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="qp-container">
        <!-- Header -->
        <div class="qp-header">
            <h1><i class="fas fa-file-alt"></i> Question Paper Creator</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Create comprehensive question papers for ${bookTitle}</p>
        </div>

        <!-- Step 1: Chapter Selection -->
        <div class="qp-step" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <h2 class="step-title">Chapter Selection & Basic Information</h2>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="paperName">Question Paper Name *</label>
                <input type="text" id="paperName" class="form-control" placeholder="Enter question paper name">
                <div class="error-message" id="paperNameError">Question paper name is required</div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="paperHeader">Question Paper Header *</label>
                <input type="text" id="paperHeader" class="form-control" placeholder="Enter question paper header">
                <div class="error-message" id="paperHeaderError">Question paper header is required</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Select Chapters *</label>
                <div class="select-all-controls">
                    <button type="button" class="btn btn-secondary" onclick="selectAllChapters()">Select All</button>
                    <button type="button" class="btn btn-secondary" onclick="unselectAllChapters()">Unselect All</button>
                </div>
                <div class="chapters-grid" id="chaptersGrid">
                    <g:each in="${chaptersList}" var="chapter">
                        <div class="chapter-item" onclick="toggleChapter(${chapter.id})">
                            <input type="checkbox" class="chapter-checkbox" id="chapter_${chapter.id}" value="${chapter.id}">
                            <span class="chapter-name">${chapter.name}</span>
                        </div>
                    </g:each>
                </div>
                <div class="error-message" id="chaptersError">At least one chapter must be selected</div>
            </div>
            
            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="proceedToStep2()">Next: Configure Questions</button>
            </div>
        </div>

        <!-- Step 2: Question Configuration -->
        <div class="qp-step hidden" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <h2 class="step-title">Question Configuration</h2>
            </div>
            
            <div class="loading-spinner" id="loadingQuestionTypes">
                <div class="spinner"></div>
                <p>Loading available question types...</p>
            </div>
            
            <div class="sections-container" id="sectionsContainer">
                <!-- Sections will be populated by JavaScript -->
            </div>
            
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="backToStep1()">Back</button>
                <button type="button" class="btn btn-success" onclick="generateQuestionPaper()">Generate Question Paper</button>
            </div>
        </div>
        
        <!-- Loading Spinner for Generation -->
        <div class="loading-spinner" id="generatingPaper">
            <div class="spinner"></div>
            <p>Generating question paper...</p>
        </div>
    </div>

    <script>
        var bookId = "${bookId}";
        var availableQuestionCounts = {};
        var questionTypeOrder = [
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence",
            "Mixed Objectives"
        ];

        function toggleChapter(chapterId) {
            var checkbox = document.getElementById("chapter_" + chapterId);
            var item = checkbox.closest(".chapter-item");

            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                item.classList.add("selected");
            } else {
                item.classList.remove("selected");
            }
        }

        function selectAllChapters() {
            var checkboxes = document.querySelectorAll(".chapter-checkbox");
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = true;
                checkbox.closest(".chapter-item").classList.add("selected");
            });
        }

        function unselectAllChapters() {
            var checkboxes = document.querySelectorAll(".chapter-checkbox");
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = false;
                checkbox.closest(".chapter-item").classList.remove("selected");
            });
        }

        function validateStep1() {
            var isValid = true;

            // Validate paper name
            var paperName = document.getElementById("paperName").value.trim();
            if (!paperName) {
                document.getElementById("paperNameError").style.display = "block";
                isValid = false;
            } else {
                document.getElementById("paperNameError").style.display = "none";
            }

            // Validate paper header
            var paperHeader = document.getElementById("paperHeader").value.trim();
            if (!paperHeader) {
                document.getElementById("paperHeaderError").style.display = "block";
                isValid = false;
            } else {
                document.getElementById("paperHeaderError").style.display = "none";
            }

            // Validate chapter selection
            var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
            if (selectedChapters.length === 0) {
                document.getElementById("chaptersError").style.display = "block";
                isValid = false;
            } else {
                document.getElementById("chaptersError").style.display = "none";
            }

            return isValid;
        }

        function proceedToStep2() {
            if (!validateStep1()) {
                return;
            }

            // Get selected chapter IDs
            var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
            var chapterIds = Array.from(selectedChapters).map(function(cb) {
                return cb.value;
            });

            // Show loading
            document.getElementById("step1").classList.add("hidden");
            document.getElementById("step2").classList.remove("hidden");
            document.getElementById("loadingQuestionTypes").style.display = "block";

            // Fetch available question counts
            fetch("/wpmain/getAvailableQuestionCounts?chapterIds=" + chapterIds.join(","))
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    if (data.success) {
                        availableQuestionCounts = data.counts;
                        createSectionForms();
                    } else {
                        alert("Error loading question types: " + data.message);
                        backToStep1();
                    }
                })
                .catch(function(error) {
                    console.error("Error:", error);
                    alert("Error loading question types");
                    backToStep1();
                })
                .finally(function() {
                    document.getElementById("loadingQuestionTypes").style.display = "none";
                });
        }

        function backToStep1() {
            document.getElementById("step2").classList.add("hidden");
            document.getElementById("step1").classList.remove("hidden");
        }

        function createSectionForms() {
            var container = document.getElementById("sectionsContainer");
            container.innerHTML = "";

            for (var i = 1; i <= 10; i++) {
                var sectionHtml = "<div class=\"section-card\">" +
                    "<div class=\"section-header\">Section " + i + "</div>" +
                    "<div class=\"section-fields\">" +
                        "<div class=\"form-group\">" +
                            "<label class=\"form-label\">Section Header</label>" +
                            "<input type=\"text\" id=\"section" + i + "Header\" class=\"form-control\" placeholder=\"e.g., Section A\">" +
                        "</div>" +
                        "<div class=\"form-group\">" +
                            "<label class=\"form-label\">Marks per Question</label>" +
                            "<input type=\"number\" id=\"section" + i + "Marks\" class=\"form-control\" min=\"1\" placeholder=\"e.g., 2\">" +
                        "</div>" +
                        "<div class=\"form-group\">" +
                            "<label class=\"form-label\">Total Questions</label>" +
                            "<input type=\"number\" id=\"section" + i + "Questions\" class=\"form-control\" min=\"1\" placeholder=\"e.g., 5\">" +
                        "</div>" +
                        "<div class=\"form-group\">" +
                            "<label class=\"form-label\">Question Type</label>" +
                            "<select id=\"section" + i + "Type\" class=\"form-control\">" +
                                "<option value=\"\">Select Type</option>" +
                                getQuestionTypeOptions() +
                            "</select>" +
                        "</div>" +
                        "<div class=\"form-group\">" +
                            "<label class=\"form-label\">Difficulty Level</label>" +
                            "<select id=\"section" + i + "Difficulty\" class=\"form-control\">" +
                                "<option value=\"All\">All</option>" +
                                "<option value=\"Easy\">Easy</option>" +
                                "<option value=\"Medium\">Medium</option>" +
                                "<option value=\"Difficult\">Difficult</option>" +
                            "</select>" +
                        "</div>" +
                    "</div>" +
                "</div>";

                container.innerHTML += sectionHtml;
            }
        }

        function getQuestionTypeOptions() {
            var options = "";
            questionTypeOrder.forEach(function(type) {
                var count = availableQuestionCounts[type] || 0;
                if (count > 0) {
                    options += "<option value=\"" + type + "\">" + type + " (" + count + " available)</option>";
                }
            });
            return options;
        }

        function validateStep2() {
            var isValid = false;

            for (var i = 1; i <= 10; i++) {
                var header = document.getElementById("section" + i + "Header").value.trim();
                var marks = document.getElementById("section" + i + "Marks").value;
                var questions = document.getElementById("section" + i + "Questions").value;
                var type = document.getElementById("section" + i + "Type").value;

                if (header && marks && questions && type) {
                    isValid = true;
                    break;
                }
            }

            if (!isValid) {
                alert("At least one section must be completely filled");
            }

            return isValid;
        }

        function generateQuestionPaper() {
            if (!validateStep2()) {
                return;
            }

            // Show loading
            document.getElementById("step2").classList.add("hidden");
            document.getElementById("generatingPaper").style.display = "block";

            // Prepare form data
            var formData = new FormData();
            formData.append("paperName", document.getElementById("paperName").value);
            formData.append("paperHeader", document.getElementById("paperHeader").value);
            formData.append("bookId", bookId);

            // Get selected chapter IDs
            var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
            var chapterIds = Array.from(selectedChapters).map(function(cb) {
                return cb.value;
            });
            formData.append("chapterIds", chapterIds.join(","));

            // Add section data
            for (var i = 1; i <= 10; i++) {
                var header = document.getElementById("section" + i + "Header").value.trim();
                var marks = document.getElementById("section" + i + "Marks").value;
                var questions = document.getElementById("section" + i + "Questions").value;
                var type = document.getElementById("section" + i + "Type").value;
                var difficulty = document.getElementById("section" + i + "Difficulty").value;

                if (header && marks && questions && type) {
                    formData.append("section" + i + "Header", header);
                    formData.append("section" + i + "Marks", marks);
                    formData.append("section" + i + "Questions", questions);
                    formData.append("section" + i + "Type", type);
                    formData.append("section" + i + "Difficulty", difficulty);
                }
            }

            // Submit form
            fetch("/wpmain/generateQuestionPaper", {
                method: "POST",
                body: formData
            })
            .then(function(response) { return response.json(); })
            .then(function(data) {
                if (data.success) {
                    // Redirect to view the generated question paper
                    window.location.href = "/wpmain/qpview?id=" + data.questionPaperId + "&bookId=" + bookId;
                } else {
                    alert("Error generating question paper: " + data.message);
                    document.getElementById("generatingPaper").style.display = "none";
                    document.getElementById("step2").classList.remove("hidden");
                }
            })
            .catch(function(error) {
                console.error("Error:", error);
                alert("Error generating question paper");
                document.getElementById("generatingPaper").style.display = "none";
                document.getElementById("step2").classList.remove("hidden");
            });
        }
    </script>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
