<%@ page contentType="text/html;charset=UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${questionPaper.patternName}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>
    <style>
        /* Professional PDF Export Styling */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            font-size: 14px;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .paper-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #333;
        }
        
        .paper-title {
            font-size: 2.5em;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .paper-subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
            font-weight: 400;
        }
        
        .paper-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #666;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .paper-info > div {
            margin: 5px 0;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }
        
        .instructions h3 {
            font-size: 1.1em;
            margin-bottom: 10px;
            color: #333;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .section {
            margin-bottom: 35px;
            page-break-inside: avoid;
        }
        
        .section-header {
            background: #f1f3f4;
            padding: 12px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .section-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #333;
            margin: 0 0 5px 0;
        }
        
        .section-info {
            font-size: 0.9em;
            color: #666;
            margin: 0;
        }
        
        .question {
            margin-bottom: 25px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            page-break-inside: avoid;
        }
        
        .question:last-child {
            border-bottom: none;
        }
        
        .question-number {
            font-weight: 600;
            color: #007bff;
            margin-bottom: 8px;
            font-size: 1em;
        }
        
        .question-text {
            font-size: 1em;
            line-height: 1.6;
            color: #333;
            margin-bottom: 12px;
            text-align: justify;
        }
        
        .question-options {
            margin-left: 20px;
            margin-top: 10px;
        }
        
        .option {
            margin-bottom: 6px;
            font-size: 0.95em;
            color: #555;
            line-height: 1.4;
        }
        
        .marks-info {
            float: right;
            font-size: 0.8em;
            color: #666;
            font-style: italic;
            margin-top: -20px;
        }
        
        .answer-space {
            border: 1px dashed #ccc;
            min-height: 60px;
            margin-top: 10px;
            padding: 10px;
            background: #fafafa;
        }
        
        .answer-space::before {
            content: "Answer:";
            font-size: 0.8em;
            color: #666;
            font-style: italic;
        }
        
        .student-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .student-field {
            flex: 1;
            margin-right: 20px;
        }
        
        .student-field:last-child {
            margin-right: 0;
        }
        
        .student-field label {
            font-weight: 600;
            font-size: 0.9em;
            color: #333;
            display: block;
            margin-bottom: 5px;
        }
        
        .student-field .line {
            border-bottom: 1px solid #333;
            height: 20px;
            width: 100%;
        }

        .match-following-header {
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .match-following-container {
            display: flex;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .match-column {
            flex: 1;
            min-width: 200px;
        }

        .match-column h4 {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
        }

        .match-item {
            margin-bottom: 8px;
            font-size: 0.95em;
            color: #333;
            padding: 5px 0;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                padding: 15mm;
                max-width: none;
                margin: 0;
            }
            
            .paper-title {
                font-size: 2em;
            }
            
            .section {
                page-break-inside: avoid;
            }
            
            .question {
                page-break-inside: avoid;
            }
            
            @page {
                margin: 15mm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Paper Header -->
        <div class="paper-header">
            <div class="paper-title">${questionPaper.patternName}</div>
            <div class="paper-subtitle">${questionPaper.header}</div>
            <div class="paper-info">
                <div><strong>Total Marks:</strong> ${questionPaper.totalMarks}</div>
                <div><strong>Date:</strong> <g:formatDate date="${questionPaper.dateCreated}" format="dd-MM-yyyy"/></div>
                <div><strong>Time:</strong> _____ hours</div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <div class="student-field">
                <label>Name:</label>
                <div class="line"></div>
            </div>
            <div class="student-field">
                <label>Roll No:</label>
                <div class="line"></div>
            </div>
            <div class="student-field">
                <label>Class:</label>
                <div class="line"></div>
            </div>
            <div class="student-field">
                <label>Date:</label>
                <div class="line"></div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <h3>Instructions:</h3>
            <ul>
                <li>Read all questions carefully before attempting.</li>
                <li>Answer all questions in the space provided.</li>
                <li>Write clearly and legibly.</li>
                <li>Marks are indicated against each question.</li>
                <li>Time allowed: _____ hours</li>
            </ul>
        </div>

        <!-- Question Sections -->
        <g:each in="${sections}" var="section" status="sectionIndex">
            <div class="section">
                <div class="section-header">
                    <div class="section-title">${section.sectionHeading}</div>
                    <div class="section-info">
                        Answer all questions. Each question carries ${section.totalMarks / section.noOfQuestions} marks.
                        (Total: ${section.totalMarks} marks)
                    </div>
                </div>

                <g:each in="${section.questions}" var="question" status="questionIndex">
                    <div class="question">
                        <div class="question-number">
                            Q${sectionIndex + 1}.${questionIndex + 1}
                            <span class="marks-info">[${section.totalMarks / section.noOfQuestions} marks]</span>
                        </div>
                        <div class="question-text">
                            <g:if test="${question.qType == 'MatchFollowing'}">
                                <script>
                                    // Parse and format MatchFollowing question
                                    document.addEventListener("DOMContentLoaded", function() {
                                        formatMatchFollowingQuestionPrint("${question.id}");
                                    });
                                </script>
                                <div id="match-question-print-${question.id}" data-question="${question.question?.encodeAsHTML()}">
                                    ${question.question}
                                </div>
                            </g:if>
                            <g:else>
                                ${question.question}
                            </g:else>
                        </div>

                        <g:if test="${question.qType == 'MCQ'}">
                            <div class="question-options">
                                <g:if test="${question.option1}">
                                    <div class="option">(a) ${question.option1}</div>
                                </g:if>
                                <g:if test="${question.option2}">
                                    <div class="option">(b) ${question.option2}</div>
                                </g:if>
                                <g:if test="${question.option3}">
                                    <div class="option">(c) ${question.option3}</div>
                                </g:if>
                                <g:if test="${question.option4}">
                                    <div class="option">(d) ${question.option4}</div>
                                </g:if>
                                <g:if test="${question.option5}">
                                    <div class="option">(e) ${question.option5}</div>
                                </g:if>
                            </div>
                        </g:if>
                        <g:elseif test="${question.qType != 'MatchFollowing'}">
                            <div class="answer-space"></div>
                        </g:elseif>
                    </div>
                </g:each>
            </div>
        </g:each>
    </div>

    <script>
        function formatMatchFollowingQuestionPrint(questionId) {
            var questionElement = document.getElementById("match-question-print-" + questionId);
            if (!questionElement) return;

            var questionText = questionElement.getAttribute("data-question");
            if (!questionText || !questionText.includes("Match the following:")) return;

            // Parse the question text
            var parts = questionText.split("Match the following:");
            if (parts.length < 2) return;

            var header = "Match the following:";
            var content = parts[1].trim();

            // Split into sections by double line breaks
            var sections = content.split(/\n\s*\n/);
            if (sections.length < 2) {
                // Try splitting by <br><br>
                sections = content.split(/<br\s*\/?>\s*<br\s*\/?>/i);
            }

            if (sections.length < 2) return;

            var leftColumn = [];
            var rightColumn = [];

            // Parse left column (A, B, C, D...)
            var leftSection = sections[0].trim();
            var leftItems = leftSection.split(/<br\s*\/?>/i);
            leftItems.forEach(function(item) {
                item = item.trim();
                if (item && (item.match(/^[A-Z]\./))) {
                    leftColumn.push(item);
                }
            });

            // Parse right column (1, 2, 3, 4...)
            var rightSection = sections[1].trim();
            var rightItems = rightSection.split(/<br\s*\/?>/i);
            rightItems.forEach(function(item) {
                item = item.trim();
                if (item && (item.match(/^\d+\./))) {
                    rightColumn.push(item);
                }
            });

            // Create the formatted HTML
            var formattedHTML = "<div class=\"match-following-header\">" + header + "</div>" +
                               "<div class=\"match-following-container\">" +
                               "<div class=\"match-column\">" +
                               "<h4>Column A</h4>";

            leftColumn.forEach(function(item) {
                formattedHTML += "<div class=\"match-item\">" + item + "</div>";
            });

            formattedHTML += "</div><div class=\"match-column\">" +
                            "<h4>Column B</h4>";

            rightColumn.forEach(function(item) {
                formattedHTML += "<div class=\"match-item\">" + item + "</div>";
            });

            formattedHTML += "</div></div>";

            questionElement.innerHTML = formattedHTML;
        }

        // Auto-trigger print dialog
        window.onload = function() {
            // Render math formulas first
            if (typeof renderMathInElement !== "undefined") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });
            }

            // Trigger print dialog after a short delay to ensure rendering is complete
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
