<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

/* Book Details Page Styles */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --text-dark: #2d3748;
    --text-light: #718096;
    --bg-light: #f7fafc;
    --border-color: #e2e8f0;
    --success-color: #48bb78;
    --warning-color: #ed8936;
}

.book-details-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Hero Section */
.hero-section {
    background: #f8f9fa;
    color: var(--text-dark);
    padding: 60px 0;
    margin-bottom: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid var(--border-color);
}

.book-cover-container {
    text-align: center;
    margin-bottom: 30px;
}

.book-cover-placeholder {
    width: 250px;
    height: 350px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin: 0 auto;
    overflow: hidden;
}

.book-cover-image {
    width: 100%;
    height: 100%;
    object-fit: inherit;
    border-radius: 4px;
}

.book-cover-title {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    padding: 20px;
    line-height: 1.4;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.book-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-align: center;
}

.book-meta {
    text-align: center;
    margin-bottom: 30px;
}

.book-meta span {
    margin: 0 15px;
    opacity: 0.8;
    color: var(--text-light);
}

/* Pricing Section */
.pricing-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.price-display {
    text-align: center;
    margin-bottom: 30px;
}

.current-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--success-color);
}

.original-price {
    font-size: 1.5rem;
    text-decoration: line-through;
    color: var(--text-light);
    margin-left: 15px;
}

.discount-badge {
    background: var(--warning-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-left: 15px;
}

/* CTA Buttons - Professional E-commerce Style */
.cta-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.cta-btn {
    padding: 12px 24px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 130px;
    display: inline-block;
    text-align: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.3px;
}

.btn-try-free {
    background: #10b981;
    color: white;
    border-color: #10b981;
}

.btn-try-free:hover {
    background: #059669;
    border-color: #059669;
    color: white;
    text-decoration: none;
}

.btn-buy-now {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.btn-buy-now:hover {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
    text-decoration: none;
}

.btn-add-cart {
    background: white;
    color: #374151;
    border-color: #d1d5db;
}

.btn-add-cart:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
    text-decoration: none;
}

/* Book Overview Section */
.overview-section {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

.chapter-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.chapter-card {
    background: var(--bg-light);
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.chapter-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.chapter-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.chapter-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.stat-item i {
    color: var(--primary-color);
}

/* Book Summary Section */
.book-summary-section {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.book-summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    text-align: center;
}

.book-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.book-stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.book-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.book-stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.book-stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.book-stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Show All Functionality */
.show-all-container {
    text-align: center;
    margin-top: 30px;
}

.show-all-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.show-all-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.chapters-hidden {
    display: none;
}

.chapters-visible {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Related Books Loader */
.related-books-loader {
    text-align: center;
    padding: 60px 20px;
}

.loader-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    color: var(--text-light);
    font-size: 1rem;
}

/* Related Books Section */
.related-books-section {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.related-books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.related-book-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    width: 250px;
}

.related-book-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.related-book-cover {
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    background: #f8f9fa;
}

.related-book-cover img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    object-position: center;
}

.related-book-cover-title {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    padding: 20px;
    line-height: 1.4;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
}

.related-book-info {
    padding: 15px;
}

.related-book-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-book-author {
    color: var(--text-light);
    font-size: 0.85rem;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-book-pricing {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.related-book-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--success-color);
}

.related-book-original-price {
    font-size: 0.9rem;
    text-decoration: line-through;
    color: var(--text-light);
}

.related-book-discount {
    background: var(--warning-color);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Testimonials Section */
.testimonials-section {
    background: var(--bg-light);
    padding: 40px;
    border-radius: 15px;
    margin-bottom: 40px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.testimonial-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 15px;
}

.testimonial-info h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-dark);
}

.testimonial-info p {
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
}

.testimonial-rating {
    margin-bottom: 15px;
}

.testimonial-rating i {
    color: #ffd700;
    margin-right: 2px;
}

.testimonial-comment {
    color: var(--text-dark);
    line-height: 1.6;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .book-details-container {
        padding: 10px;
    }

    .hero-section {
        padding: 40px 0;
    }

    .book-title {
        font-size: 2rem;
    }

    .current-price {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
    }

    .chapter-overview-grid,
    .testimonials-grid,
    .book-stats-grid {
        grid-template-columns: 1fr;
    }

    .related-books-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 15px;
    }

    .related-book-cover {
        height: 240px;
    }

    .book-summary-section {
        padding: 20px;
    }

    .overview-section,
    .related-books-section,
    .testimonials-section {
        padding: 20px;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);">
    <div class="book-details-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="book-cover-container">
                            <div class="book-cover-placeholder" id="mainBookCover">
                                <g:if test="${booksMst.coverImage}">
                                    <img src="${booksMst.coverImage}" alt="${booksMst.title}" class="book-cover-image"
                                         onerror="showColorCover(this, '${booksMst.title}', 0)">
                                </g:if>
                                <g:else>
                                    <div class="book-cover-title" id="mainBookTitle">${booksMst.title}</div>
                                </g:else>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h1 class="book-title">${booksMst.title}</h1>
                        <div class="book-meta">
                            <g:if test="${booksMst.authors}">
                                <span><i class="fas fa-user"></i> ${booksMst.authors}</span>
                            </g:if>
                            <g:if test="${booksMst.subjectyear}">
                                <span><i class="fas fa-graduation-cap"></i> ${booksMst.subjectyear}</span>
                            </g:if>
                            <g:if test="${booksMst.rating}">
                                <span><i class="fas fa-star"></i> ${booksMst.rating}/5</span>
                            </g:if>
                        </div>

                        <!-- Pricing Display -->
                        <div class="price-display">
                            <g:if test="${bookPriceDtl}">
                                <span class="current-price">₹${bookPriceDtl.sellPrice}</span>
                                <g:if test="${bookPriceDtl.listPrice && bookPriceDtl.listPrice > bookPriceDtl.sellPrice}">
                                    <span class="original-price">₹${bookPriceDtl.listPrice}</span>
                                    <span class="discount-badge">
                                        ${Math.round(((bookPriceDtl.listPrice - bookPriceDtl.sellPrice) / bookPriceDtl.listPrice) * 100)}% OFF
                                    </span>
                                </g:if>
                            </g:if>
                            <g:else>
                                <span class="current-price">₹199</span>
                                <span class="original-price">₹599</span>
                                <span class="discount-badge">67% OFF</span>
                            </g:else>
                        </div>

                        <!-- CTA Buttons -->
                        <div class="cta-buttons">
                            <a href="javascript:void(0)" class="cta-btn btn-try-free" onclick="startFreeTrial('${booksMst.id}')">
                                <i class="fas fa-play"></i> Try for Free
                            </a>
                            <a href="javascript:void(0)" class="cta-btn btn-buy-now" onclick="addToCart('${booksMst.id}', '${bookPriceDtl.bookType}')">
                                <i class="fas fa-shopping-bag"></i> Buy Now
                            </a>
                            <a href="javascript:void(0)" class="cta-btn btn-add-cart" onclick="addToCart('${booksMst.id}', '${bookPriceDtl.bookType}')">
                                <i class="fas fa-cart-plus"></i> Add to Cart
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Book Overview Section -->
        <div class="overview-section">
            <h2 class="section-title">
                <i class="fas fa-book-open"></i> What's Inside This Book
            </h2>
            <p style="text-align: center; color: var(--text-light); font-size: 1.1rem; margin-bottom: 30px;">
                Comprehensive chapter-wise content with AI-powered learning features
            </p>

            <!-- Book Level Summary -->
            <div class="book-summary-section">
                <h3 class="book-summary-title">
                    <i class="fas fa-chart-bar"></i> Complete Book Overview
                </h3>
                <div class="book-stats-grid">
                    <div class="book-stat-card">
                        <i class="fas fa-book book-stat-icon" style="color: #667eea;"></i>
                        <div class="book-stat-number">${bookLevelSummary.totalChapters}</div>
                        <div class="book-stat-label">Total Chapters</div>
                    </div>
                    <div class="book-stat-card">
                        <i class="fas fa-pencil-alt book-stat-icon" style="color: #48bb78;"></i>
                        <div class="book-stat-number">${bookLevelSummary.totalExercises}</div>
                        <div class="book-stat-label">Exercise Solutions</div>
                    </div>
                    <div class="book-stat-card">
                        <i class="fas fa-question-circle book-stat-icon" style="color: #ed8936;"></i>
                        <div class="book-stat-number">${bookLevelSummary.totalQuestions}</div>
                        <div class="book-stat-label">Total Questions</div>
                    </div>

                    <!-- Question Type Breakdown -->
                    <g:set var="questionTypeMap" value="${[
                        'LongAnswer': [title: 'Long Answer Type', icon: 'fas fa-align-left', color: '#e53e3e'],
                        'ShortAnswer': [title: 'Short Answer Type', icon: 'fas fa-align-center', color: '#dd6b20'],
                        'VeryShortAnswer': [title: 'Very Short Answer Type', icon: 'fas fa-align-justify', color: '#d69e2e'],
                        'AssertionReason': [title: 'Assertion / Reasoning Type', icon: 'fas fa-balance-scale', color: '#38a169'],
                        'Problem': [title: 'Problem', icon: 'fas fa-calculator', color: '#319795'],
                        'Multiple Choice Questions': [title: 'Multiple Choice Questions', icon: 'fas fa-list-ul', color: '#3182ce'],
                        'FillBlank': [title: 'Fill in the Blanks', icon: 'fas fa-edit', color: '#805ad5'],
                        'TrueFalse': [title: 'True or False', icon: 'fas fa-check-circle', color: '#d53f8c'],
                        'MatchFollowing': [title: 'Match the Following', icon: 'fas fa-link', color: '#00b5d8'],
                        'ArrangeSequence': [title: 'Arrange in Right Sequence', icon: 'fas fa-sort-numeric-down', color: '#0bc5ea']
                    ]}" />

                    <g:each in="${['LongAnswer', 'ShortAnswer', 'VeryShortAnswer', 'AssertionReason', 'Problem', 'Multiple Choice Questions', 'FillBlank', 'TrueFalse', 'MatchFollowing', 'ArrangeSequence']}" var="qType">
                        <g:if test="${bookLevelSummary.questionTypeCounts[qType] > 0}">
                            <div class="book-stat-card">
                                <i class="${questionTypeMap[qType].icon} book-stat-icon" style="color: ${questionTypeMap[qType].color};"></i>
                                <div class="book-stat-number">${bookLevelSummary.questionTypeCounts[qType]}</div>
                                <div class="book-stat-label">${questionTypeMap[qType].title}</div>
                            </div>
                        </g:if>
                    </g:each>
                </div>
            </div>

            <!-- Chapter-wise Details -->
            <h3 style="text-align: center; color: var(--text-dark); margin: 40px 0 20px 0; font-size: 1.4rem;">
                <i class="fas fa-list"></i> Chapter-wise Breakdown
            </h3>

            <div class="chapter-overview-grid" id="chapterOverviewGrid">
                <g:each in="${bookOverviewData}" var="chapter" status="index">
                    <div class="chapter-card ${index >= 6 ? 'chapters-hidden' : ''}" data-chapter-index="${index}">
                        <h3 class="chapter-title">
                            <i class="fas fa-bookmark" style="color: var(--primary-color); margin-right: 10px;"></i>
                            ${chapter.chapterName}
                        </h3>
                        <div class="chapter-stats">
                            <g:if test="${chapter.exerciseCount > 0}">
                                <div class="stat-item">
                                    <i class="fas fa-pencil-alt"></i>
                                    <span>${chapter.exerciseCount} Exercise Solutions</span>
                                </div>
                            </g:if>
                            <g:if test="${chapter.totalQuestions > 0}">
                                <div class="stat-item">
                                    <i class="fas fa-question-circle"></i>
                                    <span>${chapter.totalQuestions} Practice Questions</span>
                                </div>
                            </g:if>
                            <div class="stat-item">
                                <i class="fas fa-robot"></i>
                                <span>AI Explanations</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-lightbulb"></i>
                                <span>Interactive Learning</span>
                            </div>
                        </div>
                    </div>
                </g:each>
            </div>

            <!-- Show All Button -->
            <g:if test="${bookOverviewData.size() > 6}">
                <div class="show-all-container">
                    <button class="show-all-btn" id="showAllChaptersBtn" onclick="toggleAllChapters()">
                        <i class="fas fa-chevron-down"></i> Show All ${bookOverviewData.size()} Chapters
                    </button>
                </div>
            </g:if>
        </div>

        <!-- Related Books Section -->
        <div class="related-books-section">
            <h2 class="section-title">
                <i class="fas fa-books"></i> Related Books You Might Like
            </h2>

            <!-- Loader -->
            <div class="related-books-loader" id="relatedBooksLoader">
                <div class="loader-spinner"></div>
                <div class="loader-text">Loading related books...</div>
            </div>

            <!-- Related Books Grid -->
            <div class="related-books-grid" id="relatedBooksGrid" style="display: none;">
                <!-- Books will be populated via AJAX -->
            </div>
        </div>

        <!-- Testimonials Section -->
        <div class="testimonials-section">
            <h2 class="section-title">
                <i class="fas fa-comments"></i> What Our Students Say
            </h2>

            <div class="testimonials-grid">
                <g:each in="${testimonials}" var="testimonial">
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">
                                ${testimonial.avatar}
                            </div>
                            <div class="testimonial-info">
                                <h4>${testimonial.name}</h4>
                                <p>${testimonial.role}</p>
                            </div>
                        </div>
                        <div class="testimonial-rating">
                            <g:each in="${1..testimonial.rating}" var="star">
                                <i class="fas fa-star"></i>
                            </g:each>
                            <g:each in="${testimonial.rating+1..5}" var="star">
                                <i class="far fa-star"></i>
                            </g:each>
                        </div>
                        <p class="testimonial-comment">"${testimonial.comment}"</p>
                    </div>
                </g:each>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>

    // Book Details Page JavaScript

    // Array of solid colors for book covers
    var bookCoverColors = [
        "#667eea", "#764ba2", "#f093fb", "#f5576c", "#4facfe", "#00f2fe",
        "#43e97b", "#38f9d7", "#ffecd2", "#fcb69f", "#a8edea", "#fed6e3",
        "#ff9a9e", "#fecfef", "#ffeaa7", "#fab1a0", "#fd79a8", "#fdcb6e",
        "#6c5ce7", "#a29bfe", "#fd79a8", "#e17055", "#00b894", "#00cec9"
    ];

    document.addEventListener("DOMContentLoaded", function() {
        // Initialize main book cover if no image
        initializeMainBookCover();

        // Load related books via AJAX
        loadRelatedBooks();

        // Rest of the existing initialization code
        // Add smooth scrolling for internal links
        document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
            anchor.addEventListener("click", function(e) {
                e.preventDefault();
                var target = document.querySelector(this.getAttribute("href"));
                if (target) {
                    target.scrollIntoView({
                        behavior: "smooth"
                    });
                }
            });
        });

        // Add loading states to CTA buttons
        document.querySelectorAll(".cta-btn").forEach(function(button) {
            button.addEventListener("click", function(e) {
                if (this.getAttribute("href") === "#") {
                    e.preventDefault();

                    // Add loading state
                    var originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                    this.style.pointerEvents = "none";

                    // Simulate loading (remove this in production)
                    setTimeout(function() {
                        button.innerHTML = originalText;
                        button.style.pointerEvents = "auto";

                        // Show appropriate message based on button type
                        var buttonText = button.textContent.trim();
                        if (buttonText.includes("Try for Free")) {
                            alert("Redirecting to free trial...");
                        } else if (buttonText.includes("Buy Now")) {
                            alert("Redirecting to purchase page...");
                        } else if (buttonText.includes("Add to Cart")) {
                            alert("Added to cart successfully!");
                        }
                    }, 1500);
                }
            });
        });

        // Add hover effects to cards
        document.querySelectorAll(".chapter-card, .related-book-card, .testimonial-card").forEach(function(card) {
            card.addEventListener("mouseenter", function() {
                this.style.transform = "translateY(-5px)";
            });

            card.addEventListener("mouseleave", function() {
                this.style.transform = "translateY(0)";
            });
        });

        // Animate elements on scroll
        function animateOnScroll() {
            var elements = document.querySelectorAll(".chapter-card, .related-book-card, .testimonial-card");

            elements.forEach(function(element) {
                var elementTop = element.getBoundingClientRect().top;
                var elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    element.style.opacity = "1";
                    element.style.transform = "translateY(0)";
                } else {
                    element.style.opacity = "0";
                    element.style.transform = "translateY(30px)";
                }
            });
        }

        // Initialize animation styles
        document.querySelectorAll(".chapter-card, .related-book-card, .testimonial-card").forEach(function(element) {
            element.style.transition = "all 0.6s ease";
            element.style.opacity = "0";
            element.style.transform = "translateY(30px)";
        });

        // Run animation on scroll
        window.addEventListener("scroll", animateOnScroll);
        animateOnScroll(); // Run once on load

        // Related book click handlers are now handled via onclick attribute in HTML

        console.log("Book details page initialized successfully");
    });

    // Function to handle book purchase
    function purchaseBook(bookId, bookType) {
        // Show loading
        var buyButton = document.querySelector(".btn-buy-now");
        if (buyButton) {
            var originalText = buyButton.innerHTML;
            buyButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            buyButton.style.pointerEvents = "none";

            // In production, make AJAX call to purchase endpoint
            setTimeout(function() {
                buyButton.innerHTML = originalText;
                buyButton.style.pointerEvents = "auto";
                alert("Purchase functionality will be implemented here");
            }, 2000);
        }
    }

    // Function to add book to cart
    function addToCart(bookId, bookType) {
        // Show loading
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId+'&bookType='+bookType" onSuccess='booksAddedInCart(bookId, data);'/>

    }

    function booksAddedInCart(id, data) {
        window.location.href = "/wsshop/cart";
    }

    // Function to start free trial
    function startFreeTrial(bookId) {
        // In production, redirect to the aibook page with preview mode
        var bookId = "${booksMst.id}";
        window.location.href = "/wpmain/aibook?bookId=" + bookId + "&previewMode=true&siteName="+siteName;
    }

    // Function to toggle all chapters visibility
    function toggleAllChapters() {
        var hiddenChapters = document.querySelectorAll(".chapters-hidden");
        var showAllBtn = document.getElementById("showAllChaptersBtn");
        var isExpanded = hiddenChapters.length === 0 || hiddenChapters[0].classList.contains("chapters-visible");

        if (isExpanded) {
            // Hide chapters beyond first 6
            var allChapters = document.querySelectorAll(".chapter-card");
            for (var i = 6; i < allChapters.length; i++) {
                allChapters[i].classList.remove("chapters-visible");
                allChapters[i].classList.add("chapters-hidden");
            }
            showAllBtn.innerHTML = '<i class="fas fa-chevron-down"></i> Show All ${bookOverviewData.size()} Chapters';
        } else {
            // Show all chapters
            hiddenChapters.forEach(function(chapter) {
                chapter.classList.remove("chapters-hidden");
                chapter.classList.add("chapters-visible");
            });
            showAllBtn.innerHTML = '<i class="fas fa-chevron-up"></i> Show Less';
        }

        // Scroll to the button for better UX
        showAllBtn.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    // Function to initialize main book cover
    function initializeMainBookCover() {
        var mainCover = document.getElementById("mainBookCover");
        var hasImage = mainCover.querySelector("img");

        if (!hasImage) {
            var colorIndex = 0; // Use first color for main book
            mainCover.style.background = bookCoverColors[colorIndex];
        }
    }

    // Function to show color cover when image fails to load
    function showColorCover(imgElement, title, colorIndex) {
        var container = imgElement.parentElement;
        imgElement.style.display = "none";

        var titleDiv = document.createElement("div");
        titleDiv.className = "book-cover-title";
        titleDiv.textContent = title;

        container.style.background = bookCoverColors[colorIndex % bookCoverColors.length];
        container.appendChild(titleDiv);
    }

    // Function to load related books via AJAX
    function loadRelatedBooks() {
        var bookId = "${booksMst.id}";

        // Make AJAX call to wsshop/getRelatedBooks
        fetch("/wsshop/getRelatedBooks?fromApp=false&relatedBooks=true&bookId=" + bookId, {
            method: "GET",
            headers: {
                "Content-Type": "application/json"
            }
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            displayRelatedBooks(data);
        })
        .catch(function(error) {
            console.error("Error loading related books:", error);
            hideRelatedBooksLoader();
            showRelatedBooksError();
        });
    }

    // Function to display related books
    function displayRelatedBooks(data) {
        var loader = document.getElementById("relatedBooksLoader");
        var grid = document.getElementById("relatedBooksGrid");

        // Hide loader
        loader.style.display = "none";

        if (data && data.books && data.books.length > 0) {
            var booksData = JSON.parse(data.books);
            var html = "";

            booksData.forEach(function(book, index) {
                var colorIndex = (index + 1) % bookCoverColors.length;

                html += '<div class="related-book-card" onclick="navigateToBook(' + book.id + ')">' +
                       '<div class="related-book-cover" style="background: ' + bookCoverColors[colorIndex] + ';">';

                if (book.coverImage) {
                    html += '<img src="' + book.coverImage + '" alt="' + book.title + '" ' +
                           'onerror="showColorCover(this, \'' + book.title + '\', ' + colorIndex + ')">';
                } else {
                    html += '<div class="related-book-cover-title">' + book.title + '</div>';
                }

                html += '</div>' +
                       '<div class="related-book-info">' +
                       '<h4 class="related-book-title">' + book.title + '</h4>';

                if (book.authors) {
                    html += '<p class="related-book-author">by ' + book.authors + '</p>';
                }

                // Pricing section with sell price and list price
                html += '<div class="related-book-pricing">';

                if (book.bookgptSellPrice) {
                    html += '<span class="related-book-price">₹' + book.bookgptSellPrice + '</span>';

                    if (book.bookgptListPrice && book.bookgptListPrice > book.bookgptSellPrice) {
                        html += '<span class="related-book-original-price">₹' + book.bookgptListPrice + '</span>';

                        var discountPercent = Math.round(((book.bookgptListPrice - book.bookgptSellPrice) / book.bookgptListPrice) * 100);
                        html += '<span class="related-book-discount">' + discountPercent + '% OFF</span>';
                    }
                } else if (book.price) {
                    html += '<span class="related-book-price">₹' + book.price + '</span>';
                } else {
                    html += '<span class="related-book-price">Price Available Soon</span>';
                }

                html += '</div>';
                html += '</div></div>';
            });

            grid.innerHTML = html;
        } else {
            // Show placeholder books if no data
            showPlaceholderBooks();
        }

        // Show grid
        grid.style.display = "grid";
    }

    // Function to show placeholder books
    function showPlaceholderBooks() {
        var grid = document.getElementById("relatedBooksGrid");
        var html = "";

        var placeholderPrices = [
            {sell: 199, list: 599},
            {sell: 299, list: 799},
            {sell: 149, list: 399},
            {sell: 249, list: 699},
            {sell: 179, list: 499},
            {sell: 329, list: 899}
        ];

        for (var i = 1; i <= 6; i++) {
            var colorIndex = i % bookCoverColors.length;
            var priceData = placeholderPrices[i - 1];
            var discountPercent = Math.round(((priceData.list - priceData.sell) / priceData.list) * 100);

            html += '<div class="related-book-card" onclick="alert(\'This is a placeholder book\')">' +
                   '<div class="related-book-cover" style="background: ' + bookCoverColors[colorIndex] + ';">' +
                   '<div class="related-book-cover-title">Related Book ' + i + '</div>' +
                   '</div>' +
                   '<div class="related-book-info">' +
                   '<h4 class="related-book-title">Related Book ' + i + '</h4>' +
                   '<p class="related-book-author">by Author Name</p>' +
                   '<div class="related-book-pricing">' +
                   '<span class="related-book-price">₹' + priceData.sell + '</span>' +
                   '<span class="related-book-original-price">₹' + priceData.list + '</span>' +
                   '<span class="related-book-discount">' + discountPercent + '% OFF</span>' +
                   '</div>' +
                   '</div></div>';
        }

        grid.innerHTML = html;
    }

    // Function to hide loader
    function hideRelatedBooksLoader() {
        var loader = document.getElementById("relatedBooksLoader");
        loader.style.display = "none";
    }

    // Function to show error message
    function showRelatedBooksError() {
        var grid = document.getElementById("relatedBooksGrid");
        grid.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--text-light);">' +
                        '<i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>' +
                        '<p>Unable to load related books. Please try again later.</p></div>';
        grid.style.display = "block";
    }

    // Function to navigate to related book details
    function navigateToBook(bookId) {
        if (bookId) {
            window.location.href = "/wpmain/aiBookDtl?bookId=" + bookId+"&siteName="+siteName;
        }
    }
</script>


</body>
</html>
