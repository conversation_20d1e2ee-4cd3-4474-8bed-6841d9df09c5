<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.form-group label {
    font-weight: 600;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

#chaptersResult .card {
    border-left: 4px solid #667eea;
}

#chaptersResult .card-title {
    color: #333;
    font-size: 1.1rem;
}

.chapter-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.chapter-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.subtopics-title {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 0.5rem;
}

.subtopic-card {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.subtopic-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.subtopic-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.subtopic-objective {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.subtopic-bloom {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.subtopic-concepts {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.subtopic-prereq {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.subtopic-apps {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
    color: #28a745;
}

.subtopic-formulas {
    font-size: 0.85rem;
    margin-bottom: 0;
    font-weight: 600;
    color: #dc3545;
}

.chapter-card .card-header .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.chapter-card .card-header .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pyq-result .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.chapter-checkbox {
    margin-right: 8px;
}

.subtopics-title {
    color: #333 !important;
    text-decoration: none !important;
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    width: 100% !important;
    text-align: left !important;
    padding: 10px 15px !important;
    border-radius: 4px !important;
}

.subtopics-title:hover {
    color: #495057 !important;
    text-decoration: none !important;
    background-color: #e9ecef !important;
}

.subtopics-title:focus {
    box-shadow: none !important;
    outline: none !important;
}

#bulkContentProgress {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

#progressList .badge {
    font-size: 0.8rem;
}

.accordion .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0 !important;
}

.accordion .card {
    border: 1px solid #dee2e6 !important;
    margin-bottom: 10px !important;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="text-center">Create Chapters from Syllabus</h3>
                </div>
                <div class="card-body">
                    <form id="syllabusForm">
                        <input type="hidden" id="bookId" name="bookId" value="${params.bookId}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="subject">Subject</label>
                                    <input type="text" class="form-control" id="subject" name="subject"
                                           placeholder="Engineering Mathematics or BCom Accountancy" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="university">University</label>
                                    <input type="text" class="form-control" id="university" name="university"
                                           placeholder="Anna University or Delhi University" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="syllabusText">Syllabus Text</label>
                            <textarea class="form-control" id="syllabusText" name="syllabusText"
                                      rows="10" placeholder="Enter the complete syllabus text here..." required></textarea>
                        </div>

                        <div class="text-center">
                            <button type="button" class="btn btn-primary btn-lg" onclick="createChapters()">
                                Create Chapters
                            </button>
                        </div>
                    </form>

                    <div id="existingChaptersResult" style="display: none;">
                        <hr>
                        <h4>Existing Chapters</h4>
                        <div id="existingChaptersList"></div>
                    </div>

                    <div id="chaptersResult" style="display: none;">
                        <hr>
                        <h4>Created Chapters</h4>
                        <div id="chaptersList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    // Load existing chapters when page loads
    $(document).ready(function() {
        loadExistingChapters();
    });

    function loadExistingChapters() {
        var bookId = document.getElementById("bookId").value;
        if (bookId) {
            <g:remoteFunction controller="theoryBooks" action="getExistingChapters"
                             onSuccess="displayExistingChapters(data);"
                             params="'bookId=' + bookId"/>
        }
    }

    function displayExistingChapters(data) {
        if (data.status === "OK" && data.chapters && data.chapters.length > 0) {
            var chaptersHtml = displayChaptersWithSubtopics(data.chapters);
            document.getElementById("existingChaptersList").innerHTML = chaptersHtml;
            document.getElementById("existingChaptersResult").style.display = "block";

            // Hide syllabus form when chapters are present
            document.getElementById("syllabusText").style.display = "none";
            var createButton = document.querySelector("button[onclick='createChapters()']");
            if (createButton) {
                createButton.style.display = "none";
            }
            // Hide the syllabus text label
            var syllabusLabel = document.querySelector("label[for='syllabusText']");
            if (syllabusLabel) {
                syllabusLabel.style.display = "none";
            }
        } else {
            // Show syllabus form when no chapters are present
            document.getElementById("syllabusText").style.display = "block";
            var createButton = document.querySelector("button[onclick='createChapters()']");
            if (createButton) {
                createButton.style.display = "block";
            }
            // Show the syllabus text label
            var syllabusLabel = document.querySelector("label[for='syllabusText']");
            if (syllabusLabel) {
                syllabusLabel.style.display = "block";
            }
        }

        // Set subject and university fields if they exist in the response
        if (data.subject) {
            document.getElementById("subject").value = data.subject;
        }
        if (data.university) {
            document.getElementById("university").value = data.university;
        }
    }

    function displayChaptersWithSubtopics(chapters) {
        var chaptersHtml = "<div class='row'>";

        for (var i = 0; i < chapters.length; i++) {
            var chapter = chapters[i];
            chaptersHtml += "<div class='col-md-12 mb-4'>" +
                "<div class='card chapter-card'>" +
                "<div class='card-header d-flex justify-content-between align-items-center'>" +
                "<h5 class='mb-0'>" + (i + 1) + ". " + chapter.topicTitle + "</h5>" +
                "<div>";

            // Determine button and status based on PYQ and content status
            if (chapter.pyqStatus && chapter.pyqStatus.created) {
                if (chapter.contentStatus && chapter.contentStatus.created) {
                    // Both PYQs and content are created
                    chaptersHtml += "<span class='badge badge-success'>Content Created</span>";
                } else {
                    // PYQs created but content not created
                    chaptersHtml += "<button class='btn btn-sm btn-outline-light' onclick='createChapterContent(" + chapter.id + ", this)' id='content-btn-" + chapter.id + "'>" +
                        "Create Chapter Content" +
                        "</button>" +
                        "<span class='ml-2 badge badge-success'>" + chapter.pyqStatus.questionCount + " PYQs created</span>";
                }
            } else {
                // PYQs not created yet
                chaptersHtml += "<button class='btn btn-sm btn-outline-light' onclick='getPYQsForChapter(" + chapter.id + ", this)' id='pyq-btn-" + chapter.id + "'>" +
                    "Get PYQs" +
                    "</button>" +
                    "<span class='ml-2 pyq-result' id='pyq-result-" + chapter.id + "' style='display: none;'></span>";
            }

            chaptersHtml += "</div>" +
                "</div>" +
                "<div class='card-body'>";

            // Add checkbox for chapter selection
            chaptersHtml += "<div class='mb-3'>" +
                "<input type='checkbox' class='chapter-checkbox' id='chapter-" + chapter.id + "' value='" + chapter.id + "'>" +
                "<label for='chapter-" + chapter.id + "' class='ml-2'>Select this chapter for content creation</label>" +
                "</div>";

            if (chapter.subtopics && chapter.subtopics.length > 0) {
                chaptersHtml += "<div class='accordion' id='accordion-" + chapter.id + "'>" +
                    "<div class='card'>" +
                    "<div class='card-header' id='heading-" + chapter.id + "'>" +
                    "<h6 class='mb-0'>" +
                    "<button class='btn btn-link collapsed subtopics-title' type='button' data-toggle='collapse' data-target='#collapse-" + chapter.id + "' aria-expanded='false' aria-controls='collapse-" + chapter.id + "'>" +
                    "Subtopics (" + chapter.subtopics.length + ")" +
                    "</button>" +
                    "</h6>" +
                    "</div>" +
                    "<div id='collapse-" + chapter.id + "' class='collapse' aria-labelledby='heading-" + chapter.id + "' data-parent='#accordion-" + chapter.id + "'>" +
                    "<div class='card-body'>" +
                    "<div class='row'>";

                for (var j = 0; j < chapter.subtopics.length; j++) {
                    var subtopic = chapter.subtopics[j];
                    chaptersHtml += "<div class='col-md-6 mb-3'>" +
                        "<div class='subtopic-card'>" +
                        "<h6 class='subtopic-title'>" + (subtopic.title || "Untitled Subtopic") + "</h6>";

                    if (subtopic.learningObjective) {
                        chaptersHtml += "<p class='subtopic-objective'><strong>Learning Objective:</strong> " + subtopic.learningObjective + "</p>";
                    }

                    if (subtopic.bloomLevel) {
                        chaptersHtml += "<p class='subtopic-bloom'><strong>Bloom Level:</strong> " + subtopic.bloomLevel + "</p>";
                    }

                    if (subtopic.keyConcepts && Array.isArray(subtopic.keyConcepts) && subtopic.keyConcepts.length > 0) {
                        chaptersHtml += "<p class='subtopic-concepts'><strong>Key Concepts:</strong> " + subtopic.keyConcepts.join(", ") + "</p>";
                    }

                    if (subtopic.prerequisites && Array.isArray(subtopic.prerequisites) && subtopic.prerequisites.length > 0) {
                        chaptersHtml += "<p class='subtopic-prereq'><strong>Prerequisites:</strong> " + subtopic.prerequisites.join(", ") + "</p>";
                    }

                    if (subtopic.applications && Array.isArray(subtopic.applications) && subtopic.applications.length > 0) {
                        chaptersHtml += "<p class='subtopic-apps'><strong>Applications:</strong> " + subtopic.applications.join(", ") + "</p>";
                    }

                    if (subtopic.hasFormulas !== undefined) {
                        chaptersHtml += "<p class='subtopic-formulas'><strong>Has Formulas:</strong> " + (subtopic.hasFormulas ? "Yes" : "No") + "</p>";
                    }

                    chaptersHtml += "</div></div>";
                }

                chaptersHtml += "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>";
            } else {
                chaptersHtml += "<div class='accordion' id='accordion-" + chapter.id + "'>" +
                    "<div class='card'>" +
                    "<div class='card-header' id='heading-" + chapter.id + "'>" +
                    "<h6 class='mb-0'>" +
                    "<button class='btn btn-link collapsed subtopics-title' type='button' data-toggle='collapse' data-target='#collapse-" + chapter.id + "' aria-expanded='false' aria-controls='collapse-" + chapter.id + "'>" +
                    "Subtopics (0)" +
                    "</button>" +
                    "</h6>" +
                    "</div>" +
                    "<div id='collapse-" + chapter.id + "' class='collapse' aria-labelledby='heading-" + chapter.id + "' data-parent='#accordion-" + chapter.id + "'>" +
                    "<div class='card-body'>" +
                    "<p class='text-muted'>No subtopics available for this chapter.</p>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>";
            }

            chaptersHtml += "</div></div></div>";
        }

        chaptersHtml += "</div>";

        // Add bulk content creation controls
        chaptersHtml += "<div class='mt-4 p-3 border rounded bg-light'>" +
            "<div class='row'>" +
            "<div class='col-md-6'>" +
            "<div class='form-check'>" +
            "<input type='checkbox' class='form-check-input' id='selectAllChapters' onchange='toggleAllChapters(this)'>" +
            "<label class='form-check-label' for='selectAllChapters'>" +
            "<strong>Select All Chapters</strong>" +
            "</label>" +
            "</div>" +
            "</div>" +
            "<div class='col-md-6 text-right'>" +
            "<button type='button' class='btn btn-success' onclick='createBulkChapterContent()'>" +
            "<i class='fa fa-file-text'></i> Create Chapter Content" +
            "</button>" +
            "</div>" +
            "</div>" +
            "<div id='bulkContentProgress' style='display: none;'>" +
            "<hr>" +
            "<h6>Content Creation Progress:</h6>" +
            "<div id='progressList'></div>" +
            "</div>" +
            "</div>";

        return chaptersHtml;
    }

    function createChapters() {
        var bookId = document.getElementById("bookId").value;
        var subject = document.getElementById("subject").value;
        var university = document.getElementById("university").value;
        var syllabusText = document.getElementById("syllabusText").value;

        // Validation
        if (!subject.trim()) {
            alert("Please enter the subject");
            return;
        }

        if (!university.trim()) {
            alert("Please enter the university");
            return;
        }

        if (!syllabusText.trim()) {
            alert("Please enter the syllabus text");
            return;
        }

        if (!bookId) {
            alert("Book ID is required");
            return;
        }

        // Show loading
        $(".loading-icon").removeClass("hidden");

        // Make AJAX call
        <g:remoteFunction controller="theoryBooks" action="createChaptersFromSyllabus"
                         onSuccess="displayChapters(data);"
                         onFailure="handleError();"
                         params="'bookId=' + bookId + '&subject=' + encodeURIComponent(subject) + '&university=' + encodeURIComponent(university) + '&syllabusText=' + encodeURIComponent(syllabusText)"/>
    }

    function displayChapters(data) {
        // Hide loading
        $(".loading-icon").addClass("hidden");

        if (data.status === "OK" && data.chapters) {
            // Hide the created chapters result section to avoid duplication
            document.getElementById("chaptersResult").style.display = "none";

            // Hide syllabus form after creating chapters
            document.getElementById("syllabusText").style.display = "none";
            var createButton = document.querySelector("button[onclick='createChapters()']");
            if (createButton) {
                createButton.style.display = "none";
            }
            var syllabusLabel = document.querySelector("label[for='syllabusText']");
            if (syllabusLabel) {
                syllabusLabel.style.display = "none";
            }

            // Show success message
            alert("Success! " + data.chapters.length + " chapters have been created successfully.");

            // Reload existing chapters to show the newly created chapters
            loadExistingChapters();

            // Scroll to existing chapters section
            setTimeout(function() {
                document.getElementById("existingChaptersResult").scrollIntoView({ behavior: "smooth" });
            }, 500);
        } else {
            alert("Error creating chapters. Please try again.");
        }
    }

    function handleError() {
        // Hide loading
        $(".loading-icon").addClass("hidden");
        alert("An error occurred while creating chapters. Please try again.");
    }

    function getPYQsForChapter(chapterId, buttonElement) {
        // Disable button and show loading state
        buttonElement.disabled = true;
        buttonElement.innerHTML = "<i class='fa fa-spinner fa-spin'></i> Processing...";

        // Hide any previous result
        document.getElementById("pyq-result-" + chapterId).style.display = "none";

        // Get subject and university from form (if available)
        var subject = document.getElementById("subject").value || "General";
        var university = document.getElementById("university").value || "General University";

        // Make AJAX call
        <g:remoteFunction controller="theoryBooks" action="getPYQsForChapter"
                         onSuccess="handlePYQSuccess(data, chapterId);"
                         onFailure="handlePYQError(chapterId);"
                         params="'chapterId=' + chapterId + '&subject=' + encodeURIComponent(subject) + '&university=' + encodeURIComponent(university)"/>
    }

    function handlePYQSuccess(data, chapterId) {
        // Show result
        if (data.status === "OK" && data.noOfQuestions !== undefined) {
            // Reload existing chapters to show updated status
            loadExistingChapters();

            // Show success message
            alert(data.noOfQuestions + " PYQ questions have been created successfully!");
        } else {
            // Re-enable button
            var button = document.getElementById("pyq-btn-" + chapterId);
            if (button) {
                button.disabled = false;
                button.innerHTML = "Get PYQs";
            }

            // Show result
            var resultElement = document.getElementById("pyq-result-" + chapterId);
            if (resultElement) {
                resultElement.innerHTML = "<span class='badge badge-warning'>No questions created</span>";
                resultElement.style.display = "inline";
            }
        }
    }

    function handlePYQError(chapterId) {
        // Re-enable button
        var button = document.getElementById("pyq-btn-" + chapterId);
        button.disabled = false;
        button.innerHTML = "Get PYQs";

        // Show error
        var resultElement = document.getElementById("pyq-result-" + chapterId);
        resultElement.innerHTML = "<span class='badge badge-danger'>Error occurred</span>";
        resultElement.style.display = "inline";
    }

    function createChapterContent(chapterId, buttonElement) {
        // Disable button and show loading state
        buttonElement.disabled = true;
        buttonElement.innerHTML = "<i class='fa fa-spinner fa-spin'></i> Creating Content...";

        // Make AJAX call to create chapter content
        <g:remoteFunction controller="theoryBooks" action="createChapterContents"
                         onSuccess="handleContentCreationSuccess(data, chapterId);"
                         onFailure="handleContentCreationError(chapterId);"
                         params="'chapterId=' + chapterId"/>
    }

    function handleContentCreationSuccess(data, chapterId) {
        // Reload existing chapters to show updated status
        loadExistingChapters();

        // Show success message
        alert("Chapter content has been created successfully!");
    }

    function handleContentCreationError(chapterId) {
        // Re-enable button
        var button = document.getElementById("content-btn-" + chapterId);
        if (button) {
            button.disabled = false;
            button.innerHTML = "Create Chapter Content";
        }

        // Show error message
        alert("Error occurred while creating chapter content. Please try again.");
    }

    function toggleAllChapters(selectAllCheckbox) {
        var checkboxes = document.querySelectorAll(".chapter-checkbox");
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = selectAllCheckbox.checked;
        }
    }

    function createBulkChapterContent() {
        var selectedChapters = [];
        var checkboxes = document.querySelectorAll(".chapter-checkbox:checked");

        if (checkboxes.length === 0) {
            alert("Please select at least one chapter for content creation.");
            return;
        }

        for (var i = 0; i < checkboxes.length; i++) {
            selectedChapters.push(checkboxes[i].value);
        }

        // Show progress section
        document.getElementById("bulkContentProgress").style.display = "block";
        document.getElementById("progressList").innerHTML = "";

        // Disable the button during processing
        var button = document.querySelector("button[onclick='createBulkChapterContent()']");
        button.disabled = true;
        button.innerHTML = "<i class='fa fa-spinner fa-spin'></i> Creating Content...";

        // Process chapters one by one
        processBulkChapters(selectedChapters, 0);
    }

    function processBulkChapters(chapterIds, currentIndex) {
        if (currentIndex >= chapterIds.length) {
            // All chapters processed
            var button = document.querySelector("button[onclick='createBulkChapterContent()']");
            button.disabled = false;
            button.innerHTML = "<i class='fa fa-file-text'></i> Create Chapter Content";

            // Reload existing chapters to show updated status
            loadExistingChapters();

            alert("All selected chapters have been processed!");
            return;
        }

        var chapterId = chapterIds[currentIndex];
        var startTime = new Date();

        // Add progress item
        var progressHtml = "<div id='progress-" + chapterId + "' class='mb-2'>" +
            "<div class='d-flex justify-content-between align-items-center'>" +
            "<span>Chapter " + chapterId + ":</span>" +
            "<span class='badge badge-info'><i class='fa fa-spinner fa-spin'></i> Processing...</span>" +
            "</div>" +
            "</div>";
        document.getElementById("progressList").innerHTML += progressHtml;

        // Make AJAX call for this chapter
        <g:remoteFunction controller="theoryBooks" action="createPDF"
                         onSuccess="handleBulkChapterSuccess(data, chapterId, startTime, chapterIds, currentIndex);"
                         onFailure="handleBulkChapterError(chapterId, startTime, chapterIds, currentIndex);"
                         params="'chapterId=' + chapterId"/>
    }

    function handleBulkChapterSuccess(data, chapterId, startTime, chapterIds, currentIndex) {
        var endTime = new Date();
        var timeTaken = ((endTime - startTime) / 1000).toFixed(2);

        // Update progress item
        var progressElement = document.getElementById("progress-" + chapterId);
        if (progressElement) {
            progressElement.innerHTML = "<div class='d-flex justify-content-between align-items-center'>" +
                "<span>Chapter " + chapterId + ":</span>" +
                "<span class='badge badge-success'>Completed in " + timeTaken + "s</span>" +
                "</div>";
        }

        // Process next chapter
        processBulkChapters(chapterIds, currentIndex + 1);
    }

    function handleBulkChapterError(chapterId, startTime, chapterIds, currentIndex) {
        var endTime = new Date();
        var timeTaken = ((endTime - startTime) / 1000).toFixed(2);

        // Update progress item
        var progressElement = document.getElementById("progress-" + chapterId);
        if (progressElement) {
            progressElement.innerHTML = "<div class='d-flex justify-content-between align-items-center'>" +
                "<span>Chapter " + chapterId + ":</span>" +
                "<span class='badge badge-danger'>Failed after " + timeTaken + "s</span>" +
                "</div>";
        }

        // Process next chapter even if this one failed
        processBulkChapters(chapterIds, currentIndex + 1);
    }
</script>


</body>
</html>
