<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${chaptersMst?.name ?: 'Theory Chapter'} - PDF</title>

    <!-- KaTeX for math rendering - Load synchronously for Puppeteer -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

    <style>
        /* Print-optimized CSS */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Noto Sans", "Segoe UI", "Roboto", sans-serif;
            font-size: 1em;
            line-height: 1.6;
            color: #000;
            margin: 2cm 1.5cm;
            padding: 0;
            background: white;
        }

        /* Page break controls */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        .page-break-inside-avoid {
            page-break-inside: avoid;
        }

        /* Typography */
        .chapter-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        h2 {
            font-size: 1.8em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h3 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 1px solid #3498db;
            padding-bottom: 0.3em;
        }

        h4 {
            font-size: 1.3em;
            font-weight: bold;
            margin-top: 1.2em;
            margin-bottom: 0.8em;
            color: #333;
        }

        h5 {
            font-size: 1.1em;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.6em;
            color: #333;
        }

        h6 {
            font-size: 1em;
            font-weight: bold;
            margin-top: 0.8em;
            margin-bottom: 0.5em;
            color: #333;
        }

        p {
            margin: 0.8em 0;
            text-align: justify;
        }

        /* Special classes for theory content */
        .definition, .def {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .definition-title, .def-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5em;
        }

        .theorem, .thm {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .theorem-title, .thm-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 0.5em;
        }

        .example, .ex {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .example-title, .ex-title {
            font-weight: bold;
            color: #0c5460;
            margin-bottom: 0.5em;
        }

        .note {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .note-title {
            font-weight: bold;
            color: #155724;
            margin-bottom: 0.5em;
        }

        .important, .imp {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .important-title, .imp-title {
            font-weight: bold;
            color: #721c24;
            margin-bottom: 0.5em;
        }

        /* Additional theory book classes */
        .concept, .key-concept {
            background: #e7f3ff;
            border-left: 4px solid #0066cc;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .concept-title, .key-concept-title {
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 0.5em;
        }

        .summary {
            background: #f0f0f0;
            border: 2px solid #666;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .summary-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5em;
            text-align: center;
        }

        .exercise, .problem {
            background: #fff8e1;
            border-left: 4px solid #ff9800;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .exercise-title, .problem-title {
            font-weight: bold;
            color: #e65100;
            margin-bottom: 0.5em;
        }

        .solution {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .solution-title {
            font-weight: bold;
            color: #6a1b9a;
            margin-bottom: 0.5em;
        }

        .highlight, .highlight-text {
            background: #ffeb3b;
            padding: 0.2em 0.4em;
            border-radius: 0.2em;
            font-weight: 500;
        }

        .bold, .strong {
            font-weight: bold;
        }

        .italic, .emphasis {
            font-style: italic;
        }

        .underline {
            text-decoration: underline;
        }

        .center, .text-center {
            text-align: center;
        }

        .right, .text-right {
            text-align: right;
        }

        .left, .text-left {
            text-align: left;
        }

        .formula {
            text-align: center;
            font-size: 1.2em;
            margin: 1.5em 0;
            padding: 1em;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .formula-title {
            font-weight: bold;
            margin-bottom: 0.5em;
            color: #495057;
        }

        /* Lists */
        ul, ol {
            margin: 1em 0;
            padding-left: 2em;
        }

        li {
            margin: 0.5em 0;
        }

        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 0.75em;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        /* Images */
        img {
            max-width: 100%;
            height: auto;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        .figure, .image-container {
            text-align: center;
            margin: 1.5em 0;
            page-break-inside: avoid;
        }

        .figure-caption, .image-caption {
            font-style: italic;
            margin-top: 0.5em;
            color: #6c757d;
        }

        /* Code blocks */
        code {
            background: #f8f9fa;
            padding: 0.2em 0.4em;
            border-radius: 0.2em;
            font-family: "Courier New", monospace;
            font-size: 0.9em;
        }

        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.25em;
            padding: 1em;
            overflow-x: auto;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        pre code {
            background: none;
            padding: 0;
        }

        /* Blockquotes */
        blockquote {
            border-left: 4px solid #6c757d;
            padding-left: 1em;
            margin: 1em 0;
            font-style: italic;
            color: #6c757d;
        }

        /* Horizontal rules */
        hr {
            border: none;
            border-top: 2px solid #dee2e6;
            margin: 2em 0;
        }

        /* Special formatting for common theory book elements */
        .chapter-intro {
            font-size: 1.1em;
            font-style: italic;
            margin: 2em 0;
            padding: 1em;
            background: #f8f9fa;
            border-radius: 0.25em;
        }

        .learning-objectives {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .learning-objectives-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 0.5em;
        }

        .key-points {
            background: #fff9c4;
            border-left: 4px solid #fbc02d;
            padding: 1em;
            margin: 1em 0;
            border-radius: 0.25em;
            page-break-inside: avoid;
        }

        .key-points-title {
            font-weight: bold;
            color: #f57f17;
            margin-bottom: 0.5em;
        }

        /* Math formulas */
        .katex-display {
            text-align: center;
            font-size: 1.2em;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        .katex {
            page-break-inside: avoid;
        }

        /* Content sections */
        .content-section {
            margin: 2em 0;
        }

        /* Print media queries */
        @media print {
            body {
                font-size: 0.9em;
                margin: 2cm 1.5cm;
                padding: 0;
            }

            .page-break-before {
                page-break-before: always;
            }

            .page-break-after {
                page-break-after: always;
            }

            .page-break-inside-avoid {
                page-break-inside: avoid;
            }
        }

        /* No content message */
        .no-content-message {
            text-align: center;
            padding: 3em 1em;
            color: #666;
            font-style: italic;
            border: 2px dashed #ccc;
            border-radius: 0.5em;
            margin: 2em 0;
        }

        /* AI Footer */
        .ai-footer {
            margin-top: 4em;
            padding: 2em 0;
            text-align: center;
            border-top: 2px solid #3498db;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            page-break-inside: avoid;
        }

        .ai-footer-content {
            font-size: 1.1em;
            color: #1a237e;
            font-weight: 600;
            margin-bottom: 0.5em;
        }

        .ai-footer-link {
            font-size: 1.2em;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
            border: 2px solid #3498db;
            padding: 0.5em 1.5em;
            border-radius: 25px;
            display: inline-block;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
        }

        .ai-footer-link:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .ai-footer-icon {
            margin-right: 0.5em;
            font-size: 1.3em;
        }
    </style>
</head>
<body>
    <!-- Chapter Title -->
    <h1 class="chapter-title">${chaptersMst?.name ?: 'Theory Chapter'}</h1>

    <!-- Chapter Content -->
    <div class="content-section">
        <g:if test="${chapterContent}">
            ${raw(chapterContent)}
        </g:if>
        <g:else>
            <div class="no-content-message">
                <h3>No Content Available</h3>
                <p>The theory chapter content file was not found at:</p>
                <p><code>${chapterFilePath}</code></p>
                <p>Please ensure the chapter content has been uploaded to the correct location.</p>
            </div>
        </g:else>
    </div>



    <!-- JavaScript for math rendering -->
    <script>
        // Math formula rendering function
        function renderMathFormulas() {
            if (typeof renderMathInElement === "function") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });

                // Wrap display math in formula class
                document.querySelectorAll('.katex-display').forEach(function(element) {
                    if (!element.parentElement.classList.contains('formula')) {
                        var wrapper = document.createElement('div');
                        wrapper.className = 'formula';
                        element.parentNode.insertBefore(wrapper, element);
                        wrapper.appendChild(element);
                    }
                });

                // Signal that math rendering is complete for Puppeteer
                window.mathRenderingDone = true;
            }
        }

        // Render math on page load
        document.addEventListener("DOMContentLoaded", function() {
            renderMathFormulas();
        });
    </script>
</body>
</html>
