<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${booksMst?.title ?: 'Book'} - Overview</title>

    <!-- KaTeX for math rendering - Load synchronously for Puppeteer -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

    <style>
        /* Print-optimized CSS */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Noto Sans", "Segoe UI", "Roboto", sans-serif;
            font-size: 1em;
            line-height: 1.6;
            color: #000;
            margin: 2cm 1.5cm;
            padding: 0;
            background: white;
        }

        /* Page break controls */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        .page-break-inside-avoid {
            page-break-inside: avoid;
        }

        /* Typography */
        .book-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        .section-heading {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        h2 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h3 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        p {
            margin: 0.8em 0;
            text-align: justify;
        }

        /* Book Overview Section - Kindle Compatible Tables */
        .book-overview {
            border: 2px solid #333;
            margin: 2em 0;
            page-break-inside: avoid;
        }

        .overview-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 0;
            padding: 15px;
            color: #333;
            text-align: center;
            border-bottom: 1px solid #333;
            background: #f5f5f5;
        }

        .overview-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .overview-table td {
            padding: 12px 15px;
            border: 1px solid #666;
            vertical-align: top;
        }

        .overview-table .label-cell {
            font-weight: bold;
            background: #f8f8f8;
            width: 40%;
            color: #333;
        }

        .overview-table .value-cell {
            background: white;
            width: 60%;
            color: #666;
        }

        /* Question Type Colors for text */
        .qtype-long-answer { color: #2196F3; font-weight: bold; }
        .qtype-short-answer { color: #FF9800; font-weight: bold; }
        .qtype-very-short { color: #9C27B0; font-weight: bold; }
        .qtype-assertion { color: #607D8B; font-weight: bold; }
        .qtype-problem { color: #795548; font-weight: bold; }
        .qtype-mcq { color: #F44336; font-weight: bold; }
        .qtype-fill-blank { color: #3F51B5; font-weight: bold; }
        .qtype-true-false { color: #009688; font-weight: bold; }
        .qtype-match { color: #E91E63; font-weight: bold; }
        .qtype-sequence { color: #FF5722; font-weight: bold; }

        /* Chapter Cards - Kindle Compatible */
        .chapter-overview-container {
            margin-top: 30px;
        }

        .chapter-card {
            border: 2px solid #1a237e;
            margin-bottom: 2em;
            page-break-inside: avoid;
        }

        .chapter-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1a237e;
            margin: 0;
            padding: 15px;
            background: #f8f8f8;
            border-bottom: 1px solid #1a237e;
        }

        .chapter-stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .chapter-stats-table td {
            padding: 10px 15px;
            border: 1px solid #ccc;
            font-size: 0.9rem;
        }

        .chapter-stats-table .stat-label {
            font-weight: bold;
            background: #f5f5f5;
            width: 50%;
            color: #333;
        }

        .chapter-stats-table .stat-value {
            background: white;
            width: 50%;
            color: #1a237e;
            font-weight: 600;
        }

        /* Book Summary Section - Kindle Compatible */
        .book-summary-section {
            border: 2px solid #1a237e;
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        .book-summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a237e;
            margin: 0;
            padding: 20px;
            text-align: center;
            background: #f0f0f0;
            border-bottom: 1px solid #1a237e;
        }

        .book-stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .book-stats-table td {
            padding: 15px;
            border: 1px solid #ccc;
            text-align: center;
            vertical-align: middle;
        }

        .book-stats-table .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1a237e;
        }

        .book-stats-table .stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
            padding-top: 5px;
        }

        .book-stats-table .stat-cell {
            background: #f9f9f9;
            width: 25%;
        }

        /* Content sections */
        .content-section {
            margin: 3em 0;
            page-break-inside: avoid;
        }

        .section {
            page-break-before: always;
        }

        .section-header {
            margin-bottom: 2em;
            padding-bottom: 0.5em;
            border-bottom: 1px solid #ccc;
        }

        /* AI Footer - Kindle Compatible */
        .ai-footer {
            margin-top: 4em;
            padding: 2em 0;
            text-align: center;
            border-top: 2px solid #3498db;
            background: #f8f9fa;
            page-break-inside: avoid;
        }

        .ai-footer-content {
            font-size: 1.1em;
            color: #1a237e;
            font-weight: 600;
            margin-bottom: 0.5em;
        }

        .ai-footer-link {
            font-size: 1.2em;
            color: #3498db;
            text-decoration: underline;
            font-weight: bold;
            display: inline-block;
        }

        /* Print media queries */
        @media print {
            body {
                font-size: 0.9em;
                margin: 2cm 1.5cm;
                padding: 0;
            }
            
            .page-break-before {
                page-break-before: always;
            }
            
            .page-break-after {
                page-break-after: always;
            }
            
            .page-break-inside-avoid {
                page-break-inside: avoid;
            }
        }

        /* No data message */
        .no-data-message {
            text-align: center;
            padding: 3em 1em;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Book Title -->
    <h1 class="book-title">${booksMst?.title ?: 'Book Overview'}</h1>

   

    <!-- Book Description -->
    <g:if test="${booksMst?.description}">
        <div class="content-section">
            <h2 class="section-heading">Book Description</h2>
            <p>${raw(booksMst.description?.replaceAll('<br>', '<br/>'))}</p>
        </div>
    </g:if>

    <!-- Book Level Summary -->
    <g:if test="${bookLevelSummary}">
        <div class="book-summary-section">
            <h3 class="book-summary-title">
                Complete Book Overview
            </h3>
            <table class="book-stats-table">
                <tr>
                    <td class="stat-cell">
                        <div class="stat-number">${bookLevelSummary.totalChapters}</div>
                        <div class="stat-label">Total Chapters</div>
                    </td>
                    <td class="stat-cell">
                        <div class="stat-number">${bookLevelSummary.totalExercises}</div>
                        <div class="stat-label">Exercise Solutions</div>
                    </td>
                    <td class="stat-cell">
                        <div class="stat-number">${bookLevelSummary.totalQuestions}</div>
                        <div class="stat-label">Total Questions</div>
                    </td>
                    <td class="stat-cell">
                        <div class="stat-number">AI</div>
                        <div class="stat-label">Powered Learning</div>
                    </td>
                </tr>

            </table>

            <!-- Question Type Breakdown Table -->
            <g:if test="${bookLevelSummary?.questionTypeCounts}">
                <table class="overview-table" style="margin-top: 20px;">
                    <g:each in="${['LongAnswer', 'ShortAnswer', 'VeryShortAnswer', 'AssertionReason', 'Problem', 'Multiple Choice Questions', 'FillBlank', 'TrueFalse', 'MatchFollowing', 'ArrangeSequence']}" var="qType">
                        <g:if test="${bookLevelSummary.questionTypeCounts[qType] > 0}">
                            <tr>
                                <td class="label-cell">
                                    <span class="<g:if test="${qType == 'LongAnswer'}">qtype-long-answer</g:if><g:elseif test="${qType == 'ShortAnswer'}">qtype-short-answer</g:elseif><g:elseif test="${qType == 'VeryShortAnswer'}">qtype-very-short</g:elseif><g:elseif test="${qType == 'AssertionReason'}">qtype-assertion</g:elseif><g:elseif test="${qType == 'Problem'}">qtype-problem</g:elseif><g:elseif test="${qType == 'Multiple Choice Questions'}">qtype-mcq</g:elseif><g:elseif test="${qType == 'FillBlank'}">qtype-fill-blank</g:elseif><g:elseif test="${qType == 'TrueFalse'}">qtype-true-false</g:elseif><g:elseif test="${qType == 'MatchFollowing'}">qtype-match</g:elseif><g:elseif test="${qType == 'ArrangeSequence'}">qtype-sequence</g:elseif>">
                                        <g:if test="${qType == 'LongAnswer'}">Long Answer Type</g:if>
                                        <g:elseif test="${qType == 'ShortAnswer'}">Short Answer Type</g:elseif>
                                        <g:elseif test="${qType == 'VeryShortAnswer'}">Very Short Answer Type</g:elseif>
                                        <g:elseif test="${qType == 'AssertionReason'}">Assertion / Reasoning Type</g:elseif>
                                        <g:elseif test="${qType == 'Problem'}">Problem</g:elseif>
                                        <g:elseif test="${qType == 'Multiple Choice Questions'}">Multiple Choice Questions</g:elseif>
                                        <g:elseif test="${qType == 'FillBlank'}">Fill in the Blanks</g:elseif>
                                        <g:elseif test="${qType == 'TrueFalse'}">True or False</g:elseif>
                                        <g:elseif test="${qType == 'MatchFollowing'}">Match the Following</g:elseif>
                                        <g:elseif test="${qType == 'ArrangeSequence'}">Arrange in Right Sequence</g:elseif>
                                        <g:else>${qType}</g:else>
                                    </span>
                                </td>
                                <td class="value-cell">
                                    <strong>${bookLevelSummary.questionTypeCounts[qType]}</strong> Questions
                                </td>
                            </tr>
                        </g:if>
                    </g:each>
                </table>
            </g:if>
        </div>
    </g:if>

    <!-- Table of Contents / Chapter Details -->
    <div class="content-section page-break-before">
        <h2 class="section-heading">Table of Contents</h2>

        <g:if test="${bookOverviewData}">
            <div class="chapter-overview-container">
                <g:each in="${bookOverviewData}" var="chapter" status="index">
                    <div class="chapter-card">
                        <h3 class="chapter-title">
                            Chapter ${index + 1}: ${chapter.chapterName}
                        </h3>
                        <table class="chapter-stats-table">
                            <g:if test="${chapter.exerciseCount > 0}">
                                <tr>
                                    <td class="stat-label">Exercise Solutions</td>
                                    <td class="stat-value">${chapter.exerciseCount}</td>
                                </tr>
                            </g:if>
                            <g:if test="${chapter.totalQuestions > 0}">
                                <tr>
                                    <td class="stat-label">Practice Questions</td>
                                    <td class="stat-value">${chapter.totalQuestions}</td>
                                </tr>
                            </g:if>

                        </table>
                    </div>
                </g:each>
            </div>
        </g:if>

        <g:else>
            <div class="no-data-message">
                <p>Chapter information is being prepared. Please check back later.</p>
            </div>
        </g:else>
    </div>

    <!-- AI Footer -->
    <div class="ai-footer">
        <div class="ai-footer-content">
            AI version available at
        </div>
        <a href="https://www.gptsir.ai" class="ai-footer-link" target="_blank">
            www.gptsir.ai
        </a>
    </div>

    <!-- JavaScript for math rendering -->
    <script>
        // Math formula rendering function
        function renderMathFormulas() {
            if (typeof renderMathInElement === "function") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });

                // Wrap display math in formula class
                document.querySelectorAll('.katex-display').forEach(function(element) {
                    if (!element.parentElement.classList.contains('formula')) {
                        var wrapper = document.createElement('div');
                        wrapper.className = 'formula';
                        element.parentNode.insertBefore(wrapper, element);
                        wrapper.appendChild(element);
                    }
                });

                // Signal that math rendering is complete for Puppeteer
                window.mathRenderingDone = true;
            }
        }

        // Render math on page load
        document.addEventListener("DOMContentLoaded", function() {
            renderMathFormulas();
        });
    </script>
</body>
</html>
