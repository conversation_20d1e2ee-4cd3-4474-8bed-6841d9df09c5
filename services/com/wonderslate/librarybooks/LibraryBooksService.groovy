package com.wonderslate.librarybooks

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import grails.transaction.Transactional
import groovy.sql.Sql

import java.util.stream.Collectors
import java.util.stream.Stream

@Transactional
class LibraryBooksService {

    def redisService
    def grailsApplication
    DataProviderService dataProviderService

    String getAllBatchIds(String batchId){
        String batchIds = batchId
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(batchId))
        if(courseBatchesDtl!=null&&"Default".equals(courseBatchesDtl.name)){
            InstituteMst instituteMst = dataProviderService.getInstituteMst(courseBatchesDtl.conductedBy)
            if(instituteMst.associatedLibraries!=null&&!"".equals(instituteMst.associatedLibraries)){
                String [] associatedLibraries = instituteMst.associatedLibraries.split(",")
                for(String associatedLibrary:associatedLibraries){
                    batchIds += ","+CourseBatchesDtl.findByConductedBy(new Long(associatedLibrary)).id
                }
            }
        }
        return batchIds
    }

    def getInstituteBooksPagination(batchId, int pageNumber = 1) {
    String batchIds = getAllBatchIds(""+batchId)
    int noOfBooksPerPage = 30
    int offset = (pageNumber - 1) * noOfBooksPerPage

    String sql = """
    select bm.id, bm.title, bm.isbn, bm.status, bm.publisher_id, bbd.number_of_licenses,
           bbd.validity, bbd.batch_id, bm.cover_image, bm.price, bm.book_type,
           bm.language, bm.external_link, bm.test_type_book
    from books_mst bm
    join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
    where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
      and bbd.batch_id in (${batchIds})
      and (bm.show_in_library='Yes' or bm.show_in_library is null)
      limit ${noOfBooksPerPage} offset ${offset}
    

    """

    def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
    def sql2 = new Sql(dataSource)
    def results = sql2.rows(sql)
        def instituteMst = null

        List books = results.collect { comp ->
            if(instituteMst==null)
            instituteMst = InstituteMst.findById(CourseBatchesDtl.findById(comp.batch_id.toLong()).conductedBy)
        String publisherName = ''
        if (comp.publisher_id) {
            def publishers = dataProviderService.getPublisher(comp.publisher_id.toLong())
            if (publishers) publisherName = publishers.name
        }

        def booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)

        [
            id: comp.id, title: comp.title, coverImage: comp.cover_image ?: "",
            bookStatus: comp.status ?: 'unpublished', batchId: comp.batch_id,
            publisher: publisherName, bookType: comp.book_type ?: "",
            bookLangauge: comp.language ?: "", noOfLic: comp.number_of_licenses ?: "",
            validity: comp.validity ?: "", level: booksTagDtl?.level ?: "",
            syllabus: booksTagDtl?.syllabus ?: "", grade: booksTagDtl?.grade ?: "",
            subject: booksTagDtl?.subject ?: "", instituteId: instituteMst?.id,
            showtabs: instituteMst?.paidFreeTab ?: "", forceUserValidity: instituteMst?.forceUserValidity ?: "false",
            externalLink: comp.external_link, testTypeBook: comp.test_type_book
        ]
    }
    // Cache the results for the current page
    Gson gson = new Gson()
    String element = gson.toJson(books, new TypeToken<List>() {}.getType())
    redisService.set("instituteLibraryBooklist_${batchId}_page_${pageNumber}", element)

    // Calculate and cache the total number of books only for the first page
    if (pageNumber == 1) {
        //reset the time here
        String countSql = """
            select count(*) as total
            from books_mst bm
            join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
            where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
              and bbd.batch_id in (${batchIds})
           
        """

        def totalResults = sql2.firstRow(countSql)
        int totalBooks = totalResults.total
        redisService.set("instituteLibrary_${batchId}_totalBooks", totalBooks.toString())
    }
}


    String getDefaultBatchId(String batchId){
        String defaultBatchId = batchId
        CourseBatchesDtl courseBatchesDtl = dataProviderService.getCourseBatchesDtl(new Integer(batchId))
        if(courseBatchesDtl!=null&&!"Default".equals(courseBatchesDtl.name)){

            CourseBatchesDtl defaultCBD = dataProviderService.getDefaultCourseBatchesDtl(courseBatchesDtl.conductedBy)
            defaultBatchId = ""+defaultCBD.id
        }
        return defaultBatchId
    }


    def userShelfBooks(String userName){
        String tempUsername = userName.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) userName=null

        int noOfBooksPerPage = 30
        String booksList=","
        //username will be in format of siteId_username, split it and get the siteId
        String siteId = userName.split("_")[0]
        def sql = "select bm.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId,bm.title," +
                " bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,bp.expiry_date,bm.package_book_ids," +
                "'' batch_name,bm.show_in_library,bp.package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book" +
                "  from books_mst bm, wsuser.books_permission bp" +
                " where bp.book_id=bm.id " +
                " and  (bm.show_in_library='Yes' or bm.show_in_library is null)  and bp.username='"+userName+"'"+
                " union "+
                // to get package books
                "select bm1.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                " bm1.title,bm1.book_type,bm1.test_start_date,bm1.test_end_date,bm1.cover_image,bm1.price,bm1.site_id," +
                "bp.expiry_date,bm1.package_book_ids,'' batch_name,bm1.show_in_library,bm.id package_book_id,bm1.publisher_id,bm1.book_type,bm.language,bm.test_type_book" +
                " from  books_mst bm, books_permission bp, books_mst bm1 " +
                " where  bm1.show_in_library='Yes'  and bp.username='"+userName+"' and bm.id=bp.book_id  " +
                " and bm.package_book_ids is not null and FIND_IN_SET(bm1.id,bm.package_book_ids)!=0 " +
                " and bm1.site_id=bm.site_id ";

        if(Integer.parseInt(siteId)==71){
            sql += " union "+
                    "select bm.id,bm.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                    " bm.title,bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id," +
                    "null expiry_date,bm.package_book_ids,'' batch_name,bm.show_in_library,null package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book" +
                    " from  books_mst bm,wsshop.books_tag_dtl btd, wsshop.book_price_dtl bpd " +
                    " where  bm.status='published' and btd.book_id=bm.id and bpd.book_id=bm.id   " +
                    " and  btd.syllabus='Current Affairs'  and bpd.sell_price=0 and bm.site_id=71";
        }
        sql += " order by dateCreated desc";

        println("sql="+sql)

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String publisherName
        Publishers publishers = null
        List books =  results.collect {  comp ->
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
            publisherName = ''
            if(comp.publisher_id!=null && comp.publisher_id!= ""){
                publishers = dataProviderService.getPublisher(new Long(comp.publisher_id))
                if (publishers!=null) publisherName = publishers.name
            }
            return [id   : comp.id, title: comp.title, coverImage: comp.cover_image != null ? comp.cover_image : "", level: (booksTagDtl != null) ? booksTagDtl.level : "", syllabus: (booksTagDtl != null) ? booksTagDtl.syllabus : "",
                    publisher: publisherName,
                    bookType: comp.book_type!=null?comp.book_type:"",bookLanguage: comp.language!=null?comp.language:"",
                    grade: (booksTagDtl != null) ? booksTagDtl.grade : "", subject: (booksTagDtl != null) ? booksTagDtl.subject : "",'packageBookId':comp.package_book_id,
                    'packageBookIds':comp.package_book_ids,price:comp.price,showInLibrary:comp.show_in_library,
                    expiryDate:comp.expiry_date!=null?(""+comp.expiry_date).replace(':','~'):"",testTypeBook:comp.test_type_book]

        }

        List books1 = new ArrayList();
        if (books != null && books.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(books.size()/noOfBooksPerPage);
            if(books.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<books.size()){
                        tempBooks.add(books.get(booksIndex))
                        booksList +=books[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("userShelfBooks_"+ userName) = element
                redisService.("userShelfBooks_"+ userName+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(books.size <= i) break;
                books1.add(books.get(i));
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("userShelfBooksAll_"+userName) = element
        redisService.("userShelfBooks_"+ userName+"_totalBooks") = ""+books.size()
        getUserShelfBookIds(userName,results)
    }

    def getUserShelfBookIds(username,results){
        List books = results.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userBooksListString = String.join(",", userBooksArray);
        else userBooksListString = ""
        redisService.("userShelfBooks_"+username+"_"+"bookIds") = userBooksListString

    }


    def instituteLibraryFilters(int pageNo, params) {
        String batchIds = getAllBatchIds(params.batchId)
        pageNo = pageNo - 1

        int noOfBooksPerPage = 30
        String optionalCondition = ""
        String searchSuccessful = "true"

        if ("yes".equals(params.freeBooks)) {
            optionalCondition += " and ifnull(bk.price,0)=0"
        }
        if ("yes".equals(params.paidBooks)) {
            optionalCondition += " and ifnull(bk.price,0)!=0"
        }
        if ("yes".equals(params.testSeries)) {
            optionalCondition += " and bk.has_quiz='true'"
        }
        if (params.syllabus && params.syllabus != 'null') {
            optionalCondition += " and btd.syllabus in (${toSingleQuotes(params.syllabus)})"
        }
        if (params.grade && params.grade != 'null') {
            optionalCondition += " and btd.grade in (${toSingleQuotes(params.grade)})"
        }
        if (params.searchBookId && params.searchBookId != 'null' && params.searchBookId != '-1') {
            optionalCondition += " and bk.id = ${params.searchBookId}"
        } else if ("-1".equals(params.searchBookId)) {
            searchSuccessful = "false"
        }

        String limitCondition = " limit ${pageNo * noOfBooksPerPage}, ${noOfBooksPerPage}"

        // Using MAX(...) on columns not in GROUP BY
        // If your MySQL version is < 5.7.8, replace MAX with MAX/MIN as needed
        String sql = """

    SELECT 
       bk.id,
       bk.title,
       MAX(bk.isbn)            AS isbn,
       MAX(bk.status)          AS status,
       MAX(bk.publisher_id)    AS publisher_id,
       MAX(bk.cover_image)     AS cover_image,
       MAX(bk.price)           AS price,
       MAX(bk.book_type)       AS book_type,
       MAX(bk.language)        AS language,
       MAX(bk.external_link)   AS external_link,
       MAX(bbd.number_of_licenses) AS number_of_licenses,
       MAX(bbd.validity)       AS validity,
       MAX(bbd.batch_id)       AS batch_id,
       MAX(btd.level)          AS level,
       MAX(btd.syllabus)       AS syllabus,
       MAX(btd.grade)          AS grade,
       MAX(btd.subject)        AS subject,
       MAX(bk.test_type_book)  AS test_type_book
    FROM wsshop.books_mst bk
         JOIN wsuser.books_batch_dtl bbd 
            ON bk.id = bbd.book_id
         LEFT JOIN wsshop.books_tag_dtl btd 
            ON bk.id = btd.book_id
    WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
      AND bbd.batch_id IN (${batchIds})
      AND (bk.show_in_library = 'Yes' OR bk.show_in_library IS NULL)
      ${optionalCondition}
    GROUP BY bk.id, bk.title 
    ${limitCondition}


"""


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new groovy.sql.Sql(dataSource)
        def results = sql2.rows(sql)

        List filteredBooks = results.collect { comp ->
            def courseBatch = CourseBatchesDtl.findById(params.batchId as Long)
            InstituteMst instituteMst = InstituteMst.findById(courseBatch?.conductedBy)
            return [
                    id               : comp.id,
                    title            : comp.title,
                    coverImage       : comp.cover_image ?: "",
                    bookStatus       : comp.status ?: 'unpublished',
                    batchId          : (params.batchId ?: "") + "",
                    publisher        : comp.publisher_id
                            ? dataProviderService.getPublisher(comp.publisher_id as Long).name
                            : '',
                    bookType         : comp.book_type ?: "",
                    bookLangauge     : comp.language ?: "",
                    level            : comp.level ?: "",
                    syllabus         : comp.syllabus ?: "",
                    noOfLic          : comp.number_of_licenses,
                    validity         : comp.validity,
                    grade            : comp.grade ?: "",
                    subject          : comp.subject ?: "",
                    instituteId      : instituteMst?.id,
                    showtabs         : instituteMst?.paidFreeTab ?: "",
                    forceUserValidity: instituteMst?.forceUserValidity ?: "false",
                    externalLink     : comp.external_link,
                    testTypeBook     : comp.test_type_book
            ]
        }

        sql = """

            SELECT 
              count(*) as total
            FROM wsshop.books_mst bk
                 JOIN wsuser.books_batch_dtl bbd 
                    ON bk.id = bbd.book_id
                 LEFT JOIN wsshop.books_tag_dtl btd 
                    ON bk.id = btd.book_id
            WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
              AND bbd.batch_id IN (${batchIds})
              AND (bk.show_in_library = 'Yes' OR bk.show_in_library IS NULL)
              ${optionalCondition}
         
            """

        println("the count sql is $sql")
         dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
         sql2 = new groovy.sql.Sql(dataSource)
         results = sql2.rows(sql)
        println("the results are $results")
        // Convert to JSON
        def gson = new Gson()
        def element = gson.toJson(filteredBooks, new TypeToken<List>() {}.getType())
        def json = [
                books           : element,
                count           : results[0].total,
                searchSuccessful: searchSuccessful
        ]
        return json
    }



    String toSingleQuotes(String source){
        String[] parts = source.split(",")
        return Stream.of(parts).collect(Collectors.joining("','", "'", "'"));
    }



    @Transactional
    def getInstituteBooksTags(batchId) {
        String batchIds = getAllBatchIds(""+batchId)

        Map<String, List> usersMyLibraryBooks = new HashMap<String, List>()
        List<String> batchIdsList = batchIds.split(',').collect { it as Long }
        List batchBooks = BooksBatchDtl.findAllByBatchIdInList(batchIdsList)

        String books = ""
        batchBooks.each {
            books += it.bookId + ","
        }
        if (!books.equals("")) {
            String sql = "SELECT distinct syllabus as syllabus,grade FROM wsshop.books_tag_dtl btd where  book_id in (" + org.apache.commons.lang.StringUtils.removeEnd(books, ",") + ") order by syllabus,grade"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.each { book ->
                if (usersMyLibraryBooks.get("" + book.syllabus) == null) {
                    List bList = new ArrayList()
                    bList.add(book.grade)
                    usersMyLibraryBooks.put("" + book.syllabus, bList)
                } else {
                    usersMyLibraryBooks.get("" + book.syllabus).add(book.grade)
                }
            }
            Gson gson = new Gson();
            String element = gson.toJson(usersMyLibraryBooks, new TypeToken<HashMap<String, List>>() {}.getType())
            if (results != null) redisService.("batchBooksFilters_" + batchId) = element
        }
    }

}
