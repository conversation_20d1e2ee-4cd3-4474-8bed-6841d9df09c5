package com.wonderslate.shop

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.admin.AdminService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksCodeMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ExternalOrders
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.log.BooksCodeLog
import com.wonderslate.log.BooksCodeUsageLog
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksPermissionCopy
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import  com.wonderslate.shop.ShoppingCartActiveDtl

import java.text.DecimalFormat
import java.util.stream.Collectors
import java.util.stream.Stream
import com.wonderslate.sqlutil.SafeSql

@Transactional
class WsshopService {

    def grailsApplication
    DataProviderService dataProviderService
    UtilService utilService
    UserManagementService userManagementService
    def springSecurityService
    def redisService
    WsLibraryService wsLibraryService
    AdminService adminService
    BookPriceService bookPriceService
    DeliveryChargesService deliveryChargesService

    def createBookAccessCode( int siteId,Long noOfTokens){
        int index = 0
        int endIndex = noOfTokens
        String code
        while (index < endIndex){
             code = UUID.randomUUID().toString().substring(0,5).toUpperCase();
             if(BooksCodeMst.findByCodeAndSiteId(code,siteId)==null){
                BooksCodeMst booksCodeMst = new BooksCodeMst(code:code,siteId: siteId,dateCreated: new Date())
                booksCodeMst.save(flush: true, failOnError: true)
                index++;

            }
        }
    }

    @Secured(['ROLE_USER'])
    def bookCdValidate(params,username,Integer siteId) {
        String bookCode=params.bookCode
        String status = "invalid"
        Boolean  canAccess =true
        String code
        Integer bookId=null
        String campaignCode=null
        try{
        if(bookCode.length()>=13){
            BooksCodeMst booksCodeMst  = BooksCodeMst.findByCodeAndSiteId(bookCode,siteId)
            if(booksCodeMst!=null) {
                code =bookCode
                if(booksCodeMst.bookId!=null)
                    bookId= booksCodeMst.bookId
                else if(booksCodeMst.campaignCode!=null){
                    campaignCode=booksCodeMst.campaignCode
                    return 'campaignCode_'+campaignCode
                }

            }
        }else {
            code = bookCode
        }

           Integer validityDays = new Integer(365)
            if("true".equals(params.bookLevel)){
                if(bookId!=Integer.parseInt(params.bookId)) {
                    canAccess = false
                }
            }
            BooksCodeMst booksCodeMst
            if(bookId!=null)
            {
                booksCodeMst = BooksCodeMst.findByCodeAndBookIdAndSiteId(code,bookId,siteId)
            }
            else
            {
                booksCodeMst = BooksCodeMst.findByCodeAndSiteId(code,siteId)
                bookId=booksCodeMst.bookId
            }

        if(booksCodeMst!=null && canAccess) {
            BooksPermission booksPermission = BooksPermission.findByBookCodeAndBookId(code, bookId)
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(booksCodeMst.bookId))
            if (booksPermission == null) {
                booksPermission = new BooksPermission()
                booksPermission.bookId = booksCodeMst.bookId
                booksPermission.bookCode = code
                booksPermission.bookCodeLogId = booksCodeMst.bookCodeLogId
                booksPermission.username = username
                if(booksMst!=null){
                    Integer tokenCount = 50
                    if (redisService.("bookPriceDetails_" + bookId) == null) bookPriceService.getBookPrices(new Integer("" + bookId))
                    List bookPriceDtls = new JsonSlurper().parseText(redisService.("bookPriceDetails_" + bookId));
                    bookPriceDtls.each { bookPrice ->
                        if ("bookGPT".equals(bookPrice.bookType)) {
                            if(bookPrice.freeChatTokens!=null){
                                tokenCount = bookPrice.freeChatTokens
                            }
                        }
                    }
                    booksPermission.bookType = booksMst.bookType
                    booksPermission.testsPurchased = booksMst.bookType.toLowerCase() == "testseries" ? true : false
                    booksPermission.chatTokensBalance = tokenCount
                }

                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, validityDays)
                booksPermission.expiryDate = c.getTime()

                booksPermission.save(failOnError: true, flush: true)
              wsLibraryService.userShelfBooks(username);
                status = "allocated"
                booksCodeMst.delete(flush: true)

                //add record to BooskCodeUsageLog
                BooksCodeUsageLog booksCodeUsageLog = new BooksCodeUsageLog(bookId: booksPermission.bookId, bookCode: code, username: username, dateCreated: new Date(),
                        validityDate: booksPermission.expiryDate,bookCodeLogId: booksPermission.bookCodeLogId)
                booksCodeUsageLog.save(failOnError: true, flush: true)
            } else {
                status = "taken"
            }
        }
        }catch(Exception e){
            //do nothing
            println(e)
        }

       return status
    }
    def addExternalOrder(String orderFrom,String orderId,String itemCode,String poValue,String username, Integer siteId){
        if("US-S9A7-LXSA".equals(itemCode)) itemCode="25719"
        PurchaseOrder po = PurchaseOrder.findByPaymentIdAndPoMethodAndItemCode(orderId,"Amazon",new Integer(itemCode))
        if(po==null) {
            //add the corresponding purchase order
            po = new PurchaseOrder()
            po.dateCreated = new Date()
            po.itemCode = new Integer(itemCode)
            po.amount = poValue!=null?new Double(poValue):0.0
            po.currency ="INR"
            po.status = 'Active'
            po.username = username
            po.siteId = siteId
            po.paymentId = orderId
            po.poMethod=orderFrom
            po.poFor="book"
            po.save(failOnError: true, flush: true)

            return po.id.intValue()
        }else{
            println("not creating po")
            return po.id.intValue()
        }

    }

    def externalOrder(ExternalOrders externalOrders,String username){


        //initially we are assuming it to be amazon order

        //move to cache if the
        BooksMst booksMst = dataProviderService.getBooksMstByAsin(externalOrders.itemCode)
        if(booksMst!=null){
            BooksPermission booksPermission = new BooksPermission()
            booksPermission.bookId = booksMst.id
            booksPermission.username = username
            booksPermission.clientPo = externalOrders.orderId
            booksPermission.poType = "EXTERNAL_PURCHASE"

            if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, booksMst.validityDays)
                booksPermission.expiryDate = c.getTime()
            }

            booksPermission.save(failOnError: true, flush: true)
            if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(username, booksMst)
            externalOrders.status = "bookAdded"
            externalOrders.save(failOnError: true, flush: true)
            dataProviderService.getBooksListForUser(username)

        }

        return booksMst!=null?""+booksMst.id: "-1"

    }

    def externalOrderCancelled(ExternalOrders externalOrders){
        if("bookAdded".equals(externalOrders.status)){
            BooksPermission booksPermission = BooksPermission.findByClientPo(externalOrders.orderId)
            String username = booksPermission.username
            if(booksPermission!=null) booksPermission.delete(failOnError: true, flush: true)
            dataProviderService.getBooksListForUser(username)
        }
        externalOrders.status="cancelled"
        externalOrders.save(failOnError: true, flush: true)
    }

    def addBookForAmazonOrder(String amazonOrderId){
        String status = "Incorrect order id"
        PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentIdAndPoMethodAndUsername(amazonOrderId,"Amazon","systemCreated")
        if(purchaseOrder!=null){
            BooksPermission booksPermission = new BooksPermission()
            booksPermission.bookId = purchaseOrder.itemCode
            booksPermission.username = springSecurityService.currentUser.username
            booksPermission.poNo = purchaseOrder.id
            booksPermission.poType = "PURCHASE"
            BooksMst booksMst = dataProviderService.getBooksMst(purchaseOrder.itemCode)
            if(booksMst!=null && booksMst.validityDays!=null && booksMst.validityDays!="") {
                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, booksMst.validityDays)
                booksPermission.expiryDate = c.getTime()
            }

            booksPermission.save(failOnError: true, flush: true)

            // update the username in purchase order table
            purchaseOrder.username = springSecurityService.currentUser.username
            purchaseOrder.save(failOnError: true, flush: true)
            status = "Book Added"
        }else{
            purchaseOrder = PurchaseOrder.findByPaymentIdAndPoMethodAndUsername(amazonOrderId,"Amazon",springSecurityService.currentUser.username)
            if(purchaseOrder!=null) status = "Book already added to user"
            else status = "Not valid order id"
        }

        return status
    }

    def activeCategories(Integer siteId) {
        def sql
        String querySiteId=""+siteId


        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)


        String siteIdList=""+siteId;
        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
            querySiteId="1"
        }else{
            List separateLevels = LevelsMst.findAllBySiteId(siteId)
            // if there are no site specific levels
            if(separateLevels.size()==0) querySiteId="1"
        }


            sql = "SELECT distinct(btd.level) FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm" +
                    " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' and lm.site_id="+querySiteId+" and lm.name=btd.level" +
                    "  order by level";

           def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);


            List categories = results.collect { category ->
                return [level: category[0]]
            }

            Gson gson = new Gson();
            String element = gson.toJson(categories, new TypeToken<List>() {}.getType())

        if("true".equals(siteMst.appInApp)) {
           redisService.("activeCategories_" + siteMst.id) = element
        }
        else redisService.("activeCategories_" + siteId) = element


        }

    def getActiveCategoriesAndSyllabus(Integer siteId){
        def sql
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;
        String levelSiteId = ""+siteId

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }else{
            List siteLevels = LevelsMst.findAllBySiteId(siteId)
            if(siteLevels.size()==0) levelSiteId="1"
        }

        sql = "SELECT level,syllabus FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' and lm.site_id="+levelSiteId+" and btd.level=lm.name" +
                "  group by level,syllabus order by level,syllabus";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        List categories = results.collect { category ->
            return [level: category[0],syllabus:category[1]]
        }

        Gson gson = new Gson();
        String element = gson.toJson(categories, new TypeToken<List>() {}.getType())

        if("true".equals(siteMst.appInApp)) {
            redisService.("activeCategoriesSyllabus_" + siteMst.id) = element
        }
        else redisService.("activeCategoriesSyllabus_" + siteId) = element

    }

    def getActiveGrades(Long siteId,String publisherId){
        def sql
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;
        String levelSiteId = ""+siteId

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }else{
            List siteLevels = LevelsMst.findAllBySiteId(siteId)
            if(siteLevels.size()==0) levelSiteId="1"
        }

        String additionalCondition=""
        if(publisherId!=null) additionalCondition = " and bm.publisher_id="+publisherId
        sql = "SELECT level,syllabus,grade FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' and lm.site_id="+levelSiteId+" and btd.level=lm.name" +additionalCondition+
                "  group by level,syllabus,grade order by level,syllabus,grade";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List grades = results.collect { category ->

           SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(category[1],category[2])
            if(syllabusGradeDtl==null&&"College".equals(""+category[0])) syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(category[1],(""+category[2]).replace("Semester","").trim())

            //if it is still null, it means that this grade is not present. Let's include it.
             if(syllabusGradeDtl==null) {

                 //first check if syllabus is created
                 LevelSyllabus levelSyllabus = LevelSyllabus.findByLevelAndSyllabus(category[0],category[1])
                 if(levelSyllabus==null){
                     LevelsMst levelsMst = LevelsMst.findByNameAndSiteId(category[0],new Integer(siteId.intValue()))
                     if(levelsMst==null) levelsMst = LevelsMst.findByNameAndSiteId(category[0],new Integer(1))
                     //create level syllabus
                     levelSyllabus = new LevelSyllabus(level:category[0],syllabus: category[1],gradeType: "Seperate",siteId:levelsMst.siteId)
                     levelSyllabus.save(failOnError: true, flush: true)
                 }

                 syllabusGradeDtl = new SyllabusGradeDtl(syllabus:category[1],grade: category[2],siteId: levelSyllabus.siteId,sort: new Integer(0) )
                 syllabusGradeDtl.save(failOnError: true, flush: true)

             }

            return [level: category[0],syllabus:category[1],grade:category[2],id:syllabusGradeDtl!=null?syllabusGradeDtl.id:null]
        }



        return grades

    }

    def getActiveSubjects(Long siteId,String publisherId){
        def sql
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;
        String levelSiteId = ""+siteId

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }else{
            List siteLevels = LevelsMst.findAllBySiteId(siteId)
            if(siteLevels.size()==0) levelSiteId="1"
        }

        String additionalCondition=""
        if(publisherId!=null) additionalCondition = " and bm.publisher_id="+publisherId
        sql = "SELECT level,syllabus,grade,subject FROM wsshop.books_tag_dtl btd, books_mst bm,levels_mst lm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' and lm.site_id="+levelSiteId+" and btd.level=lm.name and btd.syllabus='State Level' " +additionalCondition+
                "  group by level,syllabus,grade,subject order by level,syllabus,grade";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List subjects = results.collect { category ->
            SyllabusSubject syllabusSubject = SyllabusSubject.findBySyllabusAndSubject(category[1],category[3])
            return [level: category[0],syllabus:category[1],grade:category[2],subject:category[3],id:syllabusSubject!=null?syllabusSubject.id:null]
        }

        return subjects

    }

    def getActiveSubjects(Integer siteId){
         redisService.("activeSubjects_" + siteId) = "[]"
    }

    def getActiveGrades(Integer siteId){
         if(siteId.intValue()==55) {
             def grades = getActiveGrades(siteId,null)
             Gson gson = new Gson();
             String element = gson.toJson(grades, new TypeToken<List>() {}.getType())
             redisService.("activeGrades_" + siteId) = element
         }
         else redisService.("activeGrades_" + siteId) = "[]"
    }

      def mapBookAccessCode(Integer bookId,Integer siteId, int noOfTokens,String campaignName){
        String sql='select * from books_code_mst where code is not null and book_id is null order by id desc limit '+noOfTokens+';'
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        if(results.size()==noOfTokens)
        {
            BooksCodeLog booksCodeLog = new BooksCodeLog(siteId: siteId,bookId: bookId,noOfCodes: new Integer(noOfTokens),username:springSecurityService.currentUser.username,codeName: campaignName )
            booksCodeLog.save(failOnError: true, flush: true)
            results.each {result ->
                String sql2="update BooksCodeMst set bookId="+bookId+",siteId="+siteId+",createdBy='"+springSecurityService.currentUser.username+"',bookCodeLogId="+booksCodeLog.id+" where id="+result.id;
                BooksCodeMst.executeUpdate(sql2)
            }

            return results;
        }
        else{
            return null;
        }
    }

    def getBooksList(params, Long siteId, Integer pageNo) {

        String level,syllabus, grade, subject,publisherId = null
        boolean freeBooks = false
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String mcqBook=null
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)


        String siteIdList = siteId.toString()

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_1") == null) {
                dataProviderService.getSiteIdList(new Integer(1))
            }
            siteIdList = redisService.("siteIdList_1")
        }
        else if(siteMst.associatedSites!=null&&!"".equals(siteMst.associatedSites)) siteIdList += ","+siteMst.associatedSites
        String keyName = "";
        if("true".equals(params.wileyCollectionBooks)){
            keyName="wileyCollectionBooks"
        }
        if ("true".equals(params.freeBooks)) {
            freeBooks = true

        }

        if (params.level != null && !"null".equals(params.level)&& !"".equals(params.level)&&!"undefined".equals(params.level)) {
            keyName = params.level.replaceAll("\\s+", "")
            level = params.level
            if(level.toLowerCase().indexOf("select")!=-1||level.toLowerCase().indexOf("sleep")!=-1||level.toLowerCase().indexOf(" or ")!=-1||level.indexOf("||")!=-1) level=null
        }

        if (params.syllabus != null && !"null".equals(params.syllabus)&& !"".equals(params.syllabus)&&!"undefined".equals(params.syllabus)) {
            keyName = keyName + "_" + params.syllabus.replaceAll("\\s+", "")
            syllabus = params.syllabus
            if("Staff Selection Commission".equals(syllabus)){
                //do nothing
            }
            else if(syllabus.toLowerCase().indexOf("select")!=-1||syllabus.toLowerCase().indexOf("sleep")!=-1||syllabus.toLowerCase().indexOf(" or ")!=-1||syllabus.indexOf("||")!=-1) syllabus=null
        }

        if (params.grade != null && !"null".equals(params.grade)&& !"".equals(params.grade)&&!"undefined".equals(params.grade)) {
            keyName = keyName + "_" + params.grade.replaceAll("\\s+", "")
            grade = params.grade
            if(grade.toLowerCase().indexOf("select")!=-1||grade.toLowerCase().indexOf("sleep")!=-1||grade.toLowerCase().indexOf(" or ")!=-1||grade.indexOf("||")!=-1) grade=null
        }
        if (params.subject != null && !"null".equals(params.subject)&& !"".equals(params.subject)&&!"undefined".equals(params.subject)) {
            keyName = keyName + "_" + params.subject.replaceAll("\\s+", "")
            subject = params.subject
            if(subject.toLowerCase().indexOf("select")!=-1||subject.toLowerCase().indexOf("sleep")!=-1||subject.toLowerCase().indexOf(" or ")!=-1||subject.indexOf("||")!=-1) subject=null
        }
        if (params.gradeId != null && !"null".equals(params.gradeId)) {
            SyllabusGradeDtl syllabusGradeDtl = dataProviderService.getSyllabusGradeDtl(new Long(params.gradeId))
            if (syllabusGradeDtl != null && syllabusGradeDtl.syllabus != null) {
                keyName = keyName + "_" + syllabusGradeDtl.syllabus.replaceAll("\\s+", "")
                syllabus = syllabusGradeDtl.syllabus
            }
            if (syllabusGradeDtl != null && syllabusGradeDtl.grade != null) {
                keyName = keyName + "_" + syllabusGradeDtl.grade.replaceAll("\\s+", "")
                grade = syllabusGradeDtl.grade
            }
        }
        if (params.publisherId != null && !"null".equals(publisherId)) {
            keyName = keyName + "_" + params.publisherId.replaceAll("\\s+", "")
            publisherId = params.publisherId
            if(publisherId.toLowerCase().indexOf("select")!=-1||publisherId.toLowerCase().indexOf("sleep")!=-1||publisherId.toLowerCase().indexOf(" or ")!=-1||publisherId.indexOf("||")!=-1) publisherId=null
        }
        if (params.mcqBook != null && !"null".equals(mcqBook)) {
            keyName = keyName + "_" + params.mcqBook
            mcqBook = params.mcqBook
        }
        if ("true".equals(params.getSubscription)) {

            keyName = keyName + "subscription"

        }

        String fromApp="false"
        if(params.fromApp!=null) fromApp=params.fromApp
        if (redisService.("booksList_" + siteId + "_" + keyName.replaceAll("\\s+", "") + "_" + freeBooks+"_"+pageNo) == null) {
            println("is this compiled or not")
            dataProviderService.getNewBooksList(siteIdList, siteId, params.level, syllabus, grade, subject,keyName, freeBooks, publisherId,pageNo,mcqBook,params.getSubscription,fromApp,params.noOfBooks)
        }

        //removing it till we find a better solution.
        /**
        else if(redisService.("timeOfLastUpdate")!=null){
            //update the cache if some update has happened
            long lastUpdateTime = Long.parseLong(redisService.("timeOfLastUpdate"))
            long cacheTime = Long.parseLong(redisService.("timeOfBooksList_" + siteId + "_" + keyName.replaceAll("\\s+", "") + "_" + freeBooks+"_"+pageNo))

            if(lastUpdateTime>cacheTime){
                dataProviderService.getNewBooksList(siteIdList, siteId, params.level, syllabus, grade, subject,keyName, freeBooks, publisherId,pageNo,mcqBook,params.getSubscription)
            }
        }*/
        HashMap booksAndPublishers = new HashMap()
        booksAndPublishers.put("books",redisService.("booksList_" + siteId + "_" + keyName.replaceAll("\\s+", "") + "_" + freeBooks+"_"+pageNo))
        booksAndPublishers.put("publishers",redisService.("publishers_"+siteId+"_"+ keyName+"_"+freeBooks))
        booksAndPublishers.put("bookTags",redisService.("bookTags_"+siteId+"_"+ keyName+"_"+freeBooks))
        booksAndPublishers.put("level",level)
        booksAndPublishers.put("syllabus",syllabus)
        booksAndPublishers.put("grade",grade)
        booksAndPublishers.put("subject",subject)
        booksAndPublishers.put("publisherId",publisherId)
        if(redisService.("booksList_newlyReleasedEbook_"+siteId)==null) storeAdditionalInfo(siteId)
        booksAndPublishers.put("newlyReleasedEbook",redisService.("booksList_newlyReleasedEbook_"+siteId))
        booksAndPublishers.put("ebookOfTheDay",redisService.("booksList_ebookOfTheDay_"+siteId))
        booksAndPublishers.put("trendingNowEbook",redisService.("booksList_trendingNowEbook_"+siteId))
        booksAndPublishers.put("featuredPublishers",redisService.("featuredPublishers_"+siteId))
        return booksAndPublishers
    }



    def getPublisherByName(String publisherName){
        Publishers publisher  = redisService.memoizeDomainObject(Publishers, "publishername_"+publisherName) {
            String tempPubName=publisherName

            tempPubName=publisherName.split(' ').join('').toLowerCase()
            tempPubName=tempPubName.split('-').join('').toLowerCase()
            String sql= "select p.id" +
                    " from publishers p" +
                    " where lower(replace(p.name,' ',''))='"+tempPubName+"' limit 1"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)

            return Publishers.findById(results[0][0])
        }

        return publisher
    }

    def deleteCacheKeys(){
        String cacheKeys = redisService.("cacheKeys")

        String [] keysArray = cacheKeys.split("~")
        for(int i=0;i<keysArray.length;i++){
            redisService.deleteKeysWithPattern(keysArray[i])

        }
    }

    def getBookDetailsByIsbn(params){
        def json
        String sql= "select bk.id,bk.title,bk.listprice,bk.price,p.name" +
                " from books_mst bk, publishers p" +
                " where p.id=bk.publisher_id and bk.isbn="+params.isbn+" and bk.status in ('published') and bk.site_id=3 limit 1"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String baseUrl = 'https://elearning.arihantbooks.com/'
        if (results != null && results.size() > 0) {
            String bookName = results[0][1]
            json = [status: 'ok',bookurl: baseUrl + bookName.replaceAll("\\s", "-") + "/ebook-details?siteName=arihant&bookId=" + results[0][0] + "&preview=true", listPrice: results[0][2], costPrice: results[0][3]]
        }else {
            json = [status: 'no book']
        }
        return json
    }

    def storeAdditionalInfo(Long siteId){
      //first get all the KeyValueMst thingies
        KeyValueMst newlyReleasedEbook = KeyValueMst.findByKeyNameAndSiteId("newlyReleasedEbook",new Integer(siteId.intValue()))
        if(newlyReleasedEbook!=null) {
            String sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId," +
                    " GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium" +
                    " from books_mst bk,books_tag_dtl btd, publishers p  where bk.id=btd.book_id and" +
                    " bk.id in(" + newlyReleasedEbook.keyValue + ") and bk.status in ('free','published') and p.id=bk.publisher_id " +
                    " GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id order by date_published  desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            List books = results.collect { book ->
                return [id       : book[0], title: book[1], coverImage: "" + book[2],
                        language : book[3], bookType: book[4], subject: book.subject ? book.subject.split(",")[0] : null, buylink1: book[6], listPrice: book[7], offerPrice: book[8],
                        publisher: book[9], publisherId: book[10], grade: book.grade ? book.grade.split(",")[0] : null, syllabus: book.syllabus ? book.syllabus.split(",")[0] : null, level: book.level ? book.level.split(",")[0] : null, medium: book.medium ? book.medium : ""]
            }

            books = books.unique()
            Gson gson = new Gson();
            String element = gson.toJson(books.unique(), new TypeToken<List>() {}.getType())
            redisService.("booksList_newlyReleasedEbook_" + siteId) = element

        }else{
            redisService.("booksList_newlyReleasedEbook_" + siteId) = "Nothing present"
        }

        KeyValueMst ebookOfTheDay = KeyValueMst.findByKeyNameAndSiteId("ebookOfTheDay",siteId)
        if(ebookOfTheDay!=null) {
            String sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId," +
                    " GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium" +
                    " from books_mst bk,books_tag_dtl btd, publishers p  where bk.id=btd.book_id and" +
                    " bk.id in(" + ebookOfTheDay.keyValue + ") and bk.status in ('free','published') and p.id=bk.publisher_id " +
                    " GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id order by date_published  desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            List books = results.collect { book ->
                return [id       : book[0], title: book[1], coverImage: "" + book[2],
                        language : book[3], bookType: book[4], subject: book.subject ? book.subject.split(",")[0] : null, buylink1: book[6], listPrice: book[7], offerPrice: book[8],
                        publisher: book[9], publisherId: book[10], grade: book.grade ? book.grade.split(",")[0] : null, syllabus: book.syllabus ? book.syllabus.split(",")[0] : null, level: book.level ? book.level.split(",")[0] : null, medium: book.medium ? book.medium : ""]
            }

            books = books.unique()
            Gson gson = new Gson();
            String element = gson.toJson(books.unique(), new TypeToken<List>() {}.getType())
            redisService.("booksList_ebookOfTheDay_" + siteId) = element

        }else{
            redisService.("booksList_ebookOfTheDay_" + siteId) = "Nothing present"
        }

        KeyValueMst trendingNowEbook = KeyValueMst.findByKeyNameAndSiteId("trendingNowEbook",siteId)
        if(trendingNowEbook!=null) {
            String sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId," +
                    " GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium" +
                    " from books_mst bk,books_tag_dtl btd, publishers p  where bk.id=btd.book_id and" +
                    " bk.id in(" + trendingNowEbook.keyValue + ") and bk.status in ('free','published') and p.id=bk.publisher_id " +
                    " GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id order by date_published  desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            List books = results.collect { book ->
                return [id       : book[0], title: book[1], coverImage: "" + book[2],
                        language : book[3], bookType: book[4], subject: book.subject ? book.subject.split(",")[0] : null, buylink1: book[6], listPrice: book[7], offerPrice: book[8],
                        publisher: book[9], publisherId: book[10], grade: book.grade ? book.grade.split(",")[0] : null, syllabus: book.syllabus ? book.syllabus.split(",")[0] : null, level: book.level ? book.level.split(",")[0] : null, medium: book.medium ? book.medium : ""]
            }

            books = books.unique()
            Gson gson = new Gson();
            String element = gson.toJson(books.unique(), new TypeToken<List>() {}.getType())
            redisService.("booksList_trendingNowEbook_" + siteId) = element

        }else{
            redisService.("booksList_trendingNowEbook_" + siteId) = "Nothing present"
        }

        KeyValueMst featuredPublishers = KeyValueMst.findByKeyNameAndSiteId("featuredPublishers",siteId)
        if(featuredPublishers!=null) {

            String sql =
                    "select p.id publisherId,p.name publisher, logo, cover_image" +
                            " from publishers p " +
                            " where  p.id in(" + featuredPublishers.keyValue + ")"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            Gson gson = new Gson();
            String element = gson.toJson(results, new TypeToken<List>() {}.getType())
            redisService.("featuredPublishers_" + siteId) = element
        }else{
            redisService.("featuredPublishers_" + siteId) = "Nothing Present"
        }

        KeyValueMst featuredCategories = KeyValueMst.findByKeyNameAndSiteId("featuredCategories",siteId)
        if(featuredCategories!=null) {

            String sql =
                    "select level,syllabus" +
                            " from level_syllabus " +
                            " where  syllabus in(" + toSingleQuotes(featuredCategories.keyValue) + ")"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            Gson gson = new Gson();
            String element = gson.toJson(results, new TypeToken<List>() {}.getType())
            redisService.("featuredCategories_" + siteId) = element
        }else{
            redisService.("featuredCategories_" + siteId) = "Nothing Present"
        }
    }


    @Transactional
    def addBookToCart(params,siteId,username){
        def json
        def status="Book already purchased"
        boolean bookExists = false
        String bookType = params.bookType ?: "eBook"

           status="OK"
           BooksMst booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
           ShoppingCartActiveDtl shoppingCartActiveDtl = new ShoppingCartActiveDtl(username: username, bookId: new Long(params.bookId), siteId: siteId, bookType: bookType,
                    subscriptionId:params.subscriptionId,subsStartingBookId:params.subsStartingBookId,subsDuration:params.subsDuration,publisherId: booksMst.publisherId,bookWeight: booksMst.bookWeight)
            shoppingCartActiveDtl.save(failOnError: true, flush: true)
            dataProviderService.usersCartBooksDetailsByUsername(username, siteId)

        json=[status:status]
        return json
    }


    @Transactional
    def deleteBookFromCart(params,siteId,username){
        ShoppingCartDeletedDtl shoppingCartDeletedDtl = new ShoppingCartDeletedDtl(username: username,bookId:new Long(params.bookId),siteId:siteId)
        if(shoppingCartDeletedDtl!=null) shoppingCartDeletedDtl.save(failOnError: true, flush: true)
        ShoppingCartActiveDtl shoppingCartActiveDtl = ShoppingCartActiveDtl.findByUsernameAndBookIdAndSiteId(username,new Long(params.bookId),siteId)
        if(shoppingCartActiveDtl!=null) shoppingCartActiveDtl.delete(failOnError: true, flush: true)
        dataProviderService.usersCartBooksDetailsByUsername(username,siteId)
        def json = ["status":"OK"]
        return json
    }


    def usersCartBooksDetails(String username,siteId){
        Map<String,List> cartBooks = new HashMap<String,List>()
        cartBooks = getUserCartBooks(username)
        if(cartBooks != null) return cartBooks.get(username)
        else null
    }


    def getUserCartBooks(username){
        Map<String,List> books = new HashMap<String,List>()
        if(redisService.("usersCartBooksDetails_"+username)==null || redisService.("usersCartBooksDetails_"+username)=="null"){
            return null
        }
        books = new JsonSlurper().parseText(redisService.("usersCartBooksDetails_"+username))
        return books
    }


    def usersCartBooksDetailsByUsername(String username,siteId){
        dataProviderService.usersCartBooksDetailsByUsername(username,siteId)
    }

    def getUsersCartCount(username,siteId){
        if(redisService.("usersCartBooksDetails_"+username)==null){
            dataProviderService.usersCartBooksDetailsByUsername(username,siteId)
        }
        def json = [userCartCount: Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))]
        return json
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAutoDiscountForUser(bookId,siteId){
        def json
        double amount=0
        String tamount="0"
        DecimalFormat df = new DecimalFormat("##.##");
        //max discount amount by bookId and allBooks and Book Level
        Map<String, Double> map = new HashMap<String, Double>();
        def bookLevelAmountResults = adminService.getAutoDiscountForUser("Book Level","discount_value",null,null,bookId,siteId)
        if(bookLevelAmountResults.size()>=1) {
            map.put(bookLevelAmountResults[0][0]+"", new Double(bookLevelAmountResults[0][1]));
        }
        def bookLevelPercenTageResults = adminService.getAutoDiscountForUser("Book Level","discount_percentage",null,null,bookId,siteId)
        if(bookLevelPercenTageResults.size()>=1) {
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
            //calcalute the amount by percentage
            if(booksMst!=null && booksMst.price!=null) {
                Integer perc = bookLevelPercenTageResults[0][1];
                tamount = df.format((booksMst.price * perc) / 100)
                map.put(bookLevelPercenTageResults[0][0] + "", new Double(new Double(tamount)));
            }
        }
        String minKey="";
        double maxValueInMap=0
        if(map.size()>0) {
            maxValueInMap = (Collections.max(map.values()));  // This will return max value in the Hashmap
            for (Map.Entry<Integer, Integer> entry : map.entrySet()) {  // Itrate through hashmap
                if (entry.getValue() == maxValueInMap) {
                    minKey = entry.getKey();     // Print the key with max value
                }
            }
        }
        List poCount
        boolean  codeCount = true
        if(minKey!=""){
            if(DiscountMst.findById(new Long(minKey)).redeemCount!=null){
                Integer redeemCount= new Integer(DiscountMst.findById(new Long(minKey)).redeemCount)
                poCount=PurchaseOrder.findAllByDiscountId(new Long(minKey))
                if(redeemCount<=poCount.size()) codeCount=false
            }
        }
        if(codeCount) {
            json = ['discountValue': maxValueInMap, 'discountId': minKey, couponCode: minKey ? DiscountMst.findById(new Long(minKey)).couponCode : null]
        }else{
            json = ['discountValue': 0, 'discountId': "", couponCode : null]
        }
        return json
    }


    def  addCartBooksToBuy(jsonObj,siteId) {
        String totalPayableAmount =jsonObj.totalPayableAmount
        List cartData = jsonObj.cartData
        String addressHolderId = jsonObj.addressHolderId
        String discountAmount
        String payablePrice
        String bookPrice
        String discountId
        String bookId
        String siteIds
        String bookType
        double calculatedCost=0
        double discountAmountCart=0
        boolean exceptionHappened = false
        double checkedAmount=0
        Double deliveryCosts = new Double(0.0)
        boolean outOfStock = false
        String outOfStockBookId = null
        String outOfStockBookTitle = null


        if(siteId == 1){
            siteIds = getSiteIdList(siteId)
        }else {
            siteIds = siteId+","+"1"
        }
        ShoppingCartOrdersMst shoppingCartOrdersMst
        ShoppingCartOrdersDtl shoppingCartOrdersDtl
        BookPriceDtl bookPriceDtl

        if (cartData != null) {
            try {
                //get delivery charges
                deliveryCosts = deliveryChargesService.deliveryChargesCalculator(springSecurityService.currentUser.username,false,null)
                shoppingCartOrdersMst = new ShoppingCartOrdersMst(siteId: siteId, username: springSecurityService.currentUser.username, totalPrice: Double.parseDouble(totalPayableAmount),deliverCosts:deliveryCosts )
                shoppingCartOrdersMst.save(failOnError: true, flush: true)
                for (int i = 0; i <= cartData.size() - 1; i++) {
                    if(cartData[i].bookType==null)
                        bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(cartData[i].bookId),'eBook')
                    else bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(cartData[i].bookId),cartData[i].bookType)

                    BooksMst booksMst = BooksMst.findById(new Long(cartData[i].bookId))
                    if("printbook".equals(cartData[i].bookType)||"combo".equals(cartData[i].bookType)||"comboGPT".equals(cartData[i].bookType)) {
                        if (booksMst.currentStock != null && booksMst.currentStock.intValue() < 1) {
                            outOfStock = true
                            outOfStockBookId = "" + booksMst.id
                            outOfStockBookTitle = booksMst.title
                            exceptionHappened = true
                            break
                        }
                    }
                    if (cartData[i].discountId != null && cartData[i].discountId != "") {
                        def sql = "SELECT dm.id,dpd.discount_value,dpd.discount_percentage" +
                                " FROM wsshop.discount_mst dm" +
                                " INNER JOIN wsshop.discount_price_dtl dpd ON dpd.discount_id=dm.id   LEFT JOIN wsshop.discount_book_dtl  dbd ON dbd.discount_id=dm.id  WHERE  dm.type = 'Book Level' and dm.status = 'active' and dm.start_date <= SYSDATE() " +
                                " and (dm.all_books = 'true' OR dbd.book_id = " + cartData[i].bookId + ") and dm.id =" + cartData[i].discountId + " and dm.site_id IN (" + siteIds + ")"
                        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                        def sql1 = new SafeSql(dataSource)
                        def results = sql1.rows(sql)
                        if (results.size() > 0) {
                            if (results[0][1] != null) {
                                calculatedCost = bookPriceDtl.sellPrice - results[0][1]
                            } else if (results[0][2] != null) {
                                //calcalute the amount by percentage
                                Integer perc = results[0][2]
                                discountAmountCart = ((bookPriceDtl.sellPrice * perc) / 100)
                                calculatedCost = bookPriceDtl.sellPrice.doubleValue() - discountAmountCart
                                String pAchualPrice = String.format("%.2f", calculatedCost)
                                calculatedCost = Double.parseDouble(pAchualPrice)
                            }
                            if (Double.compare(new Double(calculatedCost), new Double(cartData[i].payablePrice)) == 0) {
                                checkedAmount += (new Double(calculatedCost)).doubleValue()
                                discountAmount = cartData[i].discountAmount
                                payablePrice = cartData[i].payablePrice
                                bookPrice = cartData[i].bookPrice
                                discountId = cartData[i].discountId
                                bookId = cartData[i].bookId
                                shoppingCartOrdersDtl = new ShoppingCartOrdersDtl(cartMstId: shoppingCartOrdersMst.id, username: springSecurityService.currentUser.username, bookId: new Long(bookId), siteId: siteId, discountAmount: discountAmount != null && discountAmount != "" ? Double.parseDouble(discountAmount) : null, bookPrice: bookPrice != null && bookPrice != "" ? Double.parseDouble(bookPrice) : null,
                                        payablePrice: payablePrice != null && payablePrice != "" ? Double.parseDouble(payablePrice) : null, discountId: discountId != null && discountId != "" ? new Integer(discountId) : null,bookType: cartData[i].bookType)
                                shoppingCartOrdersDtl.save(failOnError: true, flush: true)
                            }else{
                                exceptionHappened = true
                            }
                        }
                    } else {
                        boolean priceCorrect = false
                        bookType = cartData[i].bookType
                        double subscriptionCost
                        if(bookType==null||"null".equals(bookType)||"".equals(bookType)) bookType="eBook"
                       if ("subscription".equals(bookType)){
                             subscriptionCost = getSubscriptionCost(cartData[i].subscriptionId,cartData[i].subsDuration)
                            if(Double.compare(new Double(subscriptionCost), new Double(cartData[i].payablePrice)) == 0) priceCorrect = true
                        }else{
                            if(Double.compare(new Double(bookPriceDtl.sellPrice), new Double(cartData[i].payablePrice)) == 0) priceCorrect = true
                        }
                        if (priceCorrect) {
                            payablePrice = cartData[i].payablePrice
                            bookPrice = cartData[i].bookPrice
                            bookId = cartData[i].bookId
                            bookType = cartData[i].bookType
                            checkedAmount += (new Double(payablePrice)).doubleValue()
                            shoppingCartOrdersDtl = new ShoppingCartOrdersDtl(cartMstId: shoppingCartOrdersMst.id, username: springSecurityService.currentUser.username, bookId: new Long(bookId),
                                    siteId: siteId, discountAmount: null, bookPrice: bookPrice != null && bookPrice != "" ? Double.parseDouble(bookPrice) : null, payablePrice: payablePrice != null && payablePrice != "" ? Double.parseDouble(payablePrice) : null,
                                    discountId:null,bookType: bookType,subscriptionId: cartData[i].subscriptionId,subsStartingBookId: cartData[i].subsStartingBookId,subsDuration: cartData[i].subsDuration)
                            shoppingCartOrdersDtl.save(failOnError: true, flush: true)
                        }else{
                            exceptionHappened = true
                        }
                    }

                }

                if(addressHolderId != null && addressHolderId != ""){
                    BillShipAddressHolder billShipAddressHolder = BillShipAddressHolder.findById(new Long(addressHolderId))
                    if(billShipAddressHolder!=null) {
                        billShipAddressHolder.cartMstId = shoppingCartOrdersMst.id
                        billShipAddressHolder.save(failOnError: true, flush: true)
                    }
                }
            }catch(Exception e){
                exceptionHappened = true
                println("exception in adding book to cart"+e.printStackTrace())
            }
        }


        shoppingCartOrdersMst.totalPrice = new Double(checkedAmount+deliveryCosts.doubleValue())
        shoppingCartOrdersMst.save(failOnError: true, flush: true)
        def json = ["status":exceptionHappened?"NOT OK":"OK","shoppingCartId":shoppingCartOrdersMst.id,"outOfStock":""+outOfStock,
                    "outOfStockBookId":outOfStockBookId,"outOfStockBookTitle":outOfStockBookTitle]
        return json
    }

    String toSingleQuotes(String source){
        String[] parts = source.split(",")
        return Stream.of(parts).collect(Collectors.joining("','", "'", "'"));
    }



    def getSiteIdList(siteId){
        String siteIdList="";
        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                List sites = SiteMst.findAllByDisplayInMainSite("Y")

                sites.each{site->
                    siteIdList += site.id+","

                }

                siteIdList = siteIdList.substring(0,(siteIdList.length()-1))
                redisService.("siteIdList_"+siteId) = siteIdList
            }else{
                siteIdList = redisService.("siteIdList_"+siteId)
            }
        }else{
            siteIdList = siteId+""
            redisService.("siteIdList_"+siteId)=siteIdList
        }
        return siteIdList
    }

    def getFeaturedPublishersAndCategories(Integer siteId) {

        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
         if("true".equals(siteMst.appInApp)) siteId = new Integer(1)


        String siteIdList = siteId.toString()

       

        HashMap categoriesAndPublishers = new HashMap()

        if(redisService.("featuredPublishers_"+siteId)==null) storeAdditionalInfo(siteId)
        categoriesAndPublishers.put("featuredPublishers",redisService.("featuredPublishers_"+siteId))
        categoriesAndPublishers.put("featuredCategories",redisService.("featuredCategories_"+siteId))
        return categoriesAndPublishers
    }

    def getSubscriptionMst(String subscriptionId) {
        SubscriptionMst subscriptionMst = redisService.memoizeDomainObject(SubscriptionMst, "subscriptionMst_" + subscriptionId) {
            return SubscriptionMst.findById(new Integer(subscriptionId))
        }
        return subscriptionMst
    }

    double getSubscriptionCost(String subscriptionId,String subsDuration){
        double subscriptionCost
        SubscriptionMst subscriptionMst = getSubscriptionMst(subscriptionId)
        if ("1".equals(subsDuration)) subscriptionCost = subscriptionMst.price1.doubleValue()
        else if ("2".equals(subsDuration)) subscriptionCost = subscriptionMst.price2.doubleValue()
        else if ("3".equals(subsDuration)) subscriptionCost = subscriptionMst.price3.doubleValue()
        else if ("4".equals(subsDuration)) subscriptionCost = subscriptionMst.price4.doubleValue()
        else if ("5".equals(subsDuration)) subscriptionCost = subscriptionMst.price5.doubleValue()
        return subscriptionCost
    }

    def getSubscriptionPublishedVersions(String subscriptionId){
        String sql = "select year(date_published) year_published,monthname(date_published) month_published, id,title from wsshop.books_mst bm where subscription_id=" +subscriptionId +
                " and date(DATE_ADD(bm.date_published, INTERVAL '5:30' HOUR_MINUTE)) BETWEEN date(DATE_ADD(SYSDATE() , INTERVAL -12 MONTH)) and date(DATE_ADD(SYSDATE() , INTERVAL 0 MONTH))\n" +
                " order by date_published"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def addSubscriptionBooks(BooksMst booksMst){
        List subscriptions = SubscriptionDtl.findAllBySubscriptionIdAndEndDateGreaterThan(new Integer(booksMst.subscriptionId), new Date())
        subscriptions.each{ subs ->
            BooksPermission booksPermission = BooksPermission.findByUsernameAndBookId(subs.username, booksMst.id)
            //if the book has been deleted from user's library
            if(booksPermission==null) {
                booksPermission = new BooksPermission()
                booksPermission.bookId = booksMst.id
                booksPermission.username = subs.username
                booksPermission.poNo = subs.poNo
                booksPermission.poType = "PURCHASE"
                booksPermission.subscriptionId = subs.subscriptionId
                booksPermission.expiryDate = subs.bookExpiryDate
                booksPermission.save(failOnError: true, flush: true)
            }

        }
    }

    def moveCartItems(String oldUser,String newUser,siteId){

        //remove anything that logged user has earlier
        ShoppingCartActiveDtl.executeUpdate("update ShoppingCartActiveDtl set username='delete_-"+newUser+"' where username='"+newUser+"'")
        ShoppingCartActiveDtl.executeUpdate("update ShoppingCartActiveDtl set username='"+newUser+"' where username='"+oldUser+"'")
        BillShipAddressHolder.executeUpdate("delete BillShipAddressHolder where username='"+newUser+"'")
        BillShipAddressHolder.executeUpdate("update BillShipAddressHolder set username='"+newUser+"' where username='"+oldUser+"'")
        usersCartBooksDetailsByUsername(newUser,siteId)
        return
    }

    def addBillShipAddressToHolder(params,username,siteId){
        BillShipAddressHolder billShipAddressHolder = BillShipAddressHolder.findByUsername(username)
        if(billShipAddressHolder!=null) {
            billShipAddressHolder.username = "delete_-"+username
            billShipAddressHolder.save(flush: true, failOnError: true)
        }

        billShipAddressHolder = new BillShipAddressHolder(username: username,siteId: siteId,
                billFirstName : params.billName,
                billLastName : params.billLastName,
                shipMobile : params.shipMobile,
                shipEmail : params.shipEmail,
                billMobile : params.billMobile,
                billEmail : params.billEmail,
                billAddressLine1 : params.billAddressLine1,
                billAddressLine2 : params.billAddressLine2,
                billCity : params.billCity,
                billState : params.billState,
                billCountry : params.billCountry,
                billPincode : params.billPincode,
                billAndShippingSame : params.billAndShippingSame,
                shipFirstName : params.shipName,
                shipLastName : params.shipLastName,
                shipAddressLine1 : params.shipAddressLine1,
                shipAddressLine2 : params.shipAddressLine2,
                shipCity : params.shipCity,
                shipState : params.shipState,
                shipCountry : params.shipCountry,
                shipPincode : params.shipPincode,
                shippingCharges : params.shippingCharges,
                currencyCd : params.currencyCd,
                gstin : params.gstin
        )
        billShipAddressHolder.save(flush: true, failOnError: true)
        return billShipAddressHolder
    }

    @Transactional
     def addPurchaseBillShipAddress(username,siteId,cartMstId){
        try {
            BillShipAddressHolder billShipAddressHolder = BillShipAddressHolder.findByUsername(username)
            ShippingAddressMst shippingAddressMst = new ShippingAddressMst(username: username, siteId: siteId, cartMstId: cartMstId,
                    billFirstName: billShipAddressHolder.billFirstName,
                    billLastName: billShipAddressHolder.billLastName,
                    shipMobile: billShipAddressHolder.shipMobile,
                    shipEmail: billShipAddressHolder.shipEmail,
                    billMobile: billShipAddressHolder.billMobile,
                    billEmail: billShipAddressHolder.billEmail,
                    billAddressLine1: billShipAddressHolder.billAddressLine1,
                    billAddressLine2: billShipAddressHolder.billAddressLine2,
                    billCity: billShipAddressHolder.billCity,
                    billState: billShipAddressHolder.billState,
                    billCountry: billShipAddressHolder.billCountry,
                    billPincode: billShipAddressHolder.billPincode,
                    billAndShippingSame: billShipAddressHolder.billAndShippingSame,
                    shipFirstName: billShipAddressHolder.shipFirstName,
                    shipLastName: billShipAddressHolder.shipLastName,
                    shipAddressLine1: billShipAddressHolder.shipAddressLine1,
                    shipAddressLine2: billShipAddressHolder.shipAddressLine2,
                    shipCity: billShipAddressHolder.shipCity,
                    shipState: billShipAddressHolder.shipState,
                    shipCountry: billShipAddressHolder.shipCountry,
                    shipPincode: billShipAddressHolder.shipPincode,
                    shippingCharges: billShipAddressHolder.shippingCharges,
                    currencyCd: billShipAddressHolder.currencyCd,
                    gstin: billShipAddressHolder.gstin
            )
            shippingAddressMst.save(flush: true, failOnError: true)

        }catch (Exception e){
            println("Exception in adding delivery address and it is "+e.toString())
        }
        return
    }

    def getWileyCollectionMainBook(){
        BooksMst booksMst = BooksMst.findBySiteIdAndPackageBookIdsIsNotNull(new Integer(66))
        redisService.("wileyCollectionBook") = ""+booksMst.id
        redisService.("wileyCollectionBookTitle") = booksMst.title
        redisService.("wileyCollectionBooksList") = booksMst.packageBookIds
    }

    def getRechargeOptions(def siteId)
    {
        def rechargeOptions = redisService.("rechargeOptions_"+siteId)
        if(rechargeOptions == null)
        {
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql = new SafeSql(dataSource)
            def results = sql.rows("select bm.id,bm.title,bm.description,bpd.sell_price,bpd.free_chat_tokens from books_mst bm, book_price_dtl bpd " +
                    " where bm.site_id = "+siteId+" and bm.status='recharge' and bm.id=bpd.book_id  order by bpd.sell_price")
            if(results.size() == 0) results = sql.rows("select bm.id,bm.title,bm.description,bpd.sell_price,bpd.free_chat_tokens from books_mst bm, book_price_dtl bpd " +
                    " where bm.site_id = 1  and bm.status='recharge' and bm.id=bpd.book_id  order by bpd.sell_price")
            Gson gson = new Gson();
            String element = gson.toJson(results, new TypeToken<List>() {}.getType())
            redisService.("rechargeOptions_"+siteId) = element
            return element
        }
        return rechargeOptions
    }
}
