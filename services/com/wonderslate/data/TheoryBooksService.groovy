package com.wonderslate.data

import com.wonderslate.shop.PdfExporterService
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject

class TheoryBooksService {
    def grailsApplication
    PdfExporterService pdfExporterService
    def springSecurityService
    AutogptService autogptService

    def talkToGPT(String prompt, String apiEndpoint = "chat-completion") {
        JSONObject requestBody = new JSONObject()
        requestBody.put("prompt", prompt)
        URL url = new URL(grailsApplication.config.grails.aiserver.url + "/"+apiEndpoint)

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if (responseCode == 200) {
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            try {
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            } catch (Exception e) {
                println("Exception in getQuestionFix: " + e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString())
                return null
            }

        } else {
            println("Error in getQuestionFix: " + responseCode)
            return null
        }

    }

    def jsonCleaner(String jsonInput){
        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("html", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")

        return jsonInput
    }


    def createChapterContents(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl!=null) [status: "OK"]
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        BooksDtl booksDtl = BooksDtl.findByBookId(chaptersMst.bookId)
        Prompts prompts = Prompts.findByPromptType("subtopicContentCreator")
        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        List subtopics = ChaptersSubtopicDtl.findAllByChapterId(new Long(chapterId))
        String htmlContent = ""
        String prompt
        subtopics.each { subtopic ->
            println("subtopic: " + subtopic.title)
            prompt = prompts.basePrompt
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts+". "+subtopic.learningObjective)
            //get all questions
             resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
            List questions = ObjectiveMst.findAllByQuizIdAndSubTopic(new Integer(resourceDtl.resLink),subtopic.title)
            String questionText = ""
            int questionCount = 0
            questions.each { question ->
                questionCount++
                questionText += questionCount + ". " + question.question + "\n"
            }
            prompt = prompt+ "\n PYQs: \n"+questionText
            println("prompt: " + prompt)
            def responseString = talkToGPT(prompt)
            println("responseString: " + responseString.response)
            htmlContent += jsonCleaner(responseString.response)

        }

        prompts = Prompts.findByPromptType("chapterIntroductionCreator")
        prompt = prompts.basePrompt
        prompt = prompt.replaceAll("CHAPTERTITLE", chaptersMst.name)
        prompt = prompt+" \n"+  htmlContent
        println("Caling to get introduction")
        def responseString = talkToGPT(prompt)
        htmlContent = jsonCleaner(responseString.response) + htmlContent
        chapterHtmlFile.write(htmlContent)
        resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl==null) {
            resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "Notes", resourceName: chaptersMst.name, createdBy: springSecurityService.currentUser.username, resLink: "blank")
            resourceDtl.save(failOnError: true, flush: true)
        }
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+resourceDtl.id)
        resourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+resourceDtl.id+"/"+resourceDtl.id+".pdf"
        resourceDtl.filename = resourceDtl.id+".pdf"
        resourceDtl.save(failOnError: true, flush: true)

        def json1 = [status: "OK"]
        return json1

    }

    def getPYQsForChapter(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) [status: "OK"]
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        BooksDtl booksDtl = BooksDtl.findByBookId(chaptersMst.bookId)
        List subtopics = ChaptersSubtopicDtl.findAllByChapterId(new Long(chapterId))
        Prompts prompts = Prompts.findByPromptType("pyqsForSubtopic")
        int noOfQuestions = 0
        subtopics.each { subtopic ->
            String prompt = prompts.basePrompt
            prompt = prompt.replaceAll("SYLLABUSSUBJECT", booksDtl.syllabusSubject)
            prompt = prompt.replaceAll("UNIVERSITY", booksDtl.university)
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts)
            def responseString = talkToGPT(prompt,"perplexity-completion")
             resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
            if(resourceDtl==null){
                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                quizIdGenerator.save()
                resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "PYQs",
                        resLink: quizIdGenerator.id, createdBy: springSecurityService.currentUser.username)
                resourceDtl.save(failOnError: true, flush: true)
            }
            def jsonResponse

            try {
                jsonResponse = new JsonSlurper().parseText(jsonCleaner(responseString.response))
            }catch (Exception e){
                jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(responseString.response))
                println("Exception in getPYQsForChapter "+e.toString())
            }
            println("jsonResponse: "+jsonResponse)
            println("number of questions: "+jsonResponse.size())
            jsonResponse.each { question ->
                ObjectiveMst objectiveMst = new ObjectiveMst(quizId: new Integer(resourceDtl.resLink), quizType: "QA", question: question.question,
                        difficultylevel: question.difficulty,
                        qType: question.type,subTopic: subtopic.title,explainLink: question.university+","+question.year
                )
                objectiveMst.save(failOnError: true, flush: true)
                noOfQuestions++
            }

        }
        def json = [status: "OK", noOfQuestions: noOfQuestions]
        return json
    }
}
