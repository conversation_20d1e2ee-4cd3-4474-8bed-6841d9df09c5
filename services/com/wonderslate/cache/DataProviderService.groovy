package com.wonderslate.cache

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.wonderslate.data.AssessmentBookMst
import com.wonderslate.data.BooksDtl
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.DirectionsMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.MetainfoService
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.RelatedVideosNew
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.SubjectMst
import com.wonderslate.data.UtilService
import com.wonderslate.data.VideoMst
import com.wonderslate.groups.GroupsMembersDtl
import com.wonderslate.groups.GroupsMst
import com.wonderslate.institute.BatchResourcesDtl
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.ChaptersDownloadDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.log.AppVersion
import com.wonderslate.log.NomineeVotingDtl
import com.wonderslate.log.TeacherNomineeDtl
import com.wonderslate.prepjoy.DailyTestsDtl
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.QuizStatistics
import com.wonderslate.publish.Authors
import com.wonderslate.publish.BookRatingReviews
import com.wonderslate.publish.BooksAuthorDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ExamDtl
import com.wonderslate.publish.ExamMst
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.Polls
import com.wonderslate.publish.PollsDetails
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.seo.HeaderMst
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import com.wonderslate.shop.PagesMst
import com.wonderslate.shop.WsshopService
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserGradesDtl
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import com.wonderslate.institute.InstituteMst
import com.wonderslate.data.SiteDtl

import javax.sql.DataSource
import java.util.stream.Collectors
import java.util.stream.Stream

@Transactional
class DataProviderService {
    def redisService
    def grailsApplication
    def servletContext
    SpringSecurityService springSecurityService
    UtilService utilService
    MetainfoService metainfoService
    UserManagementService userManagementService
    DataProviderService dataProviderService
    WsshopService wsshopService
    BookPriceService  bookPriceService

    def getPublisherBookCategories(siteId,publisherId){
        def sql

        def siteIdList = ""+siteId
        sql = "SELECT btd.level,btd.syllabus,btd.grade FROM wsshop.books_tag_dtl btd, books_mst bm" +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' " +
                " and bm.publisher_id="+publisherId+
                " group by btd.level,btd.syllabus,btd.grade,btd.subject order by level,syllabus,grade*1,grade";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        List categories = results.collect{ category ->
            return [level:category[0], syllabus:category[1], grade:category[2]]
        }

        sql = "SELECT distinct(btd.level) FROM wsshop.books_tag_dtl btd, books_mst bm" +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' " +
                " and bm.publisher_id="+publisherId+
                " order by level";
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);


        List topLevel = results.collect{ category ->
            return [level:category[0]]
        }

        Gson gson = new Gson();
        String element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("publisherBookCategories_"+siteId+"_"+publisherId) = element

        gson = new Gson();
        element = gson.toJson(topLevel,new TypeToken<List>() {}.getType())
        redisService.("publisherTopLevel_"+siteId+"_"+publisherId) = element

    }

    def getBookCategories(Integer siteId){
        def sql
        String siteIdList;

        if(siteId.intValue()==1) {
            if(redisService.("siteIdList_"+siteId)==null) {
                getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
            sql = "SELECT btd.level,btd.syllabus,btd.grade,btd.subject FROM wsshop.books_tag_dtl btd, books_mst bm where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id and bm.status='published' group by btd.level,btd.syllabus,btd.grade,btd.subject order by level,syllabus,grade*1,grade,subject";
        } else {
            siteIdList = ""+siteId
            sql = "SELECT btd.level,btd.syllabus,btd.grade,btd.subject FROM wsshop.books_tag_dtl btd, books_mst bm where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published' group by btd.level,btd.syllabus,btd.grade,btd.subject order by level,syllabus,grade*1,grade,subject";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List categories = results.collect{ category ->
            return [level:category[0], syllabus:category[1], grade:category[2], subject:category[3]]
        }

        redisService.("bookCategories_"+siteId) = addDoubleQuotes(convertToJsonString(categories.toString()))



        //new code to get level and categories
        sql = "SELECT distinct(btd.syllabus) FROM wsshop.books_tag_dtl btd, books_mst bm where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='School' order by syllabus";
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0]]
        }

        redisService.("bookCategories_"+siteId+"_School") = addDoubleQuotes(convertToJsonString(categories.toString()))
        sql = "SELECT distinct(btd.syllabus) FROM wsshop.books_tag_dtl btd, books_mst bm where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='Healthcare' order by syllabus";
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0]]
        }

        redisService.("bookCategories_"+siteId+"_Healthcare") = addDoubleQuotes(convertToJsonString(categories.toString()))

        sql = "SELECT distinct(btd.syllabus),ls.degree_type FROM wsshop.books_tag_dtl btd, books_mst bm,level_syllabus ls where bm.site_id in(" + siteIdList + ") " +
                "and bm.id=btd.book_id  and bm.status='published'  and btd.level='College' and ls.syllabus=btd.syllabus order by ls.degree_type,syllabus";

        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],degreeType:category[1]]
        }
        redisService.("bookCategories_"+siteId+"_College") = addDoubleQuotes(convertToJsonString(categories.toString()))

        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='Competitive Exams' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_CompetitiveExams") = element

        //Engineering Entrances
        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='Engineering Entrances' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        gson = new Gson();
        element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_EngineeringEntrances") = element

        //Government Recuitments
        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='Government Recruitments' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        gson = new Gson();
        element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_GovernmentRecruitments") = element

        //ITI & Polytechnic
        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='ITI & Polytechnic' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        gson = new Gson();
        element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_ITI&Polytechnic") = element

        //Medical Entrances
        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='Medical Entrances' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        gson = new Gson();
        element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_MedicalEntrances") = element

        //General
        sql = "SELECT distinct btd.syllabus,btd.grade,sgd.state FROM wsshop.books_tag_dtl btd, books_mst bm, syllabus_grade_dtl sgd " +
                "where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and level='General' and sgd.grade=btd.grade " +
                " group by btd.syllabus,sgd.state,btd.grade order by btd.syllabus,sgd.state,btd.grade";


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        categories = results.collect{ category ->
            return [syllabus:category[0],grade:category[1],state:category[2]]
        }
        gson = new Gson();
        element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("bookCategories_"+siteId+"_General") = element

    }

    def getBooksList(siteIdList,siteId,level,syllabus,grade,subject,keyName){
        def optionalCondition="";

        if(!"null".equals(syllabus)) {
            optionalCondition +=" and btd.syllabus='"+syllabus+"'"

        };
        if(!"null".equals(grade)) {
            optionalCondition +=" and btd.grade='"+grade+"'"

        };
        if(!"null".equals(subject)) {
            optionalCondition +=" and btd.subject='"+subject+"'"

        };

        String sql =
                "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                        "(select name from publishers where id=bk.publisher_id) name," +
                        "bk.publisher_id publisherId," +
                        "null chapterDisplayPrice,null chapterPrice,bk.test_type_book,bk.book_type,date_published,bk.test_start_date,bk.test_end_date" +
                        " from books_mst bk, wsshop.books_tag_dtl btd "+
                        " where bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published') and btd.level='"+level+"' "+optionalCondition+" " +
                        " group by bk.id  order by book_type,date_published desc"


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        def totalMarks,noOfQuestions,language1,language2,resourceId
        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""
            resourceId=""
            if("test".equals(book[12])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]));
                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                    resourceId = testDetails[0].resourceID
                }
            }

            return [id: book[0], title: book[1],coverImage:""+book[2],subjectyear:book[3],
                    'listPrice':book[4],'rating':book[5],'offerPrice':book[6], 'publisher':book[7],'publisherId':book[8],
                    'chapterDisplayPrice':book[9],'chapterSellPrice':book[10],'chapterType':book[11], 'bookType':book[12],
                    'testStartDate':book[14]!=null?(""+book[14]):"",'testEndDate':book[15]!=null?(""+book[15]):""
                    ,'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2,'resourceId':resourceId]
        }

        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())

        redisService.("booksList_"+siteId+"_"+ keyName) = element
    }

    def getBooksListEvidya(siteId){
        String sql =
                "select bk.id,bk.title,bk.cover_image,bk.authors,bk.language" +
                        " from books_mst bk, wsshop.books_tag_dtl btd,publishers p "+
                        " where bk.id=btd.book_id and bk.site_id = "+siteId+" and bk.status in ('free','published') and btd.level='College' " +
                        " and p.id= bk.publisher_id " +
                        " group by bk.id  order by book_type,date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List books = results.collect { book ->
            return [id: book[0], title: (book[1]), coverImage:""+book[2],
                    authors:book[3]!=null?(book[3]):book[3], language:book[4]]
        }
        List books1 = new ArrayList();
        int booksLength=10;
        if(booksLength>books.size()) booksLength = books.size()

        for (int i = 0; i < booksLength; i ++) {
            books1.add(books.get(i));
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("booksList_"+siteId) = element
        Gson gson1 = new Gson();
        String element1 = gson1.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("startingBooksList_"+siteId) = element1

    }


    @Transactional
    def getPublisherLatestBooks(siteId,publisherId){
        def optionalCondition="";

        if(publisherId!=null&&!"null".equals(publisherId)) {
            optionalCondition +=" and bk.publisher_id="+publisherId

        };

        String sql =
                "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,btd.subject,bk.buylink1,bk.listprice,bk.price" +
                        " from books_mst bk, wsshop.books_tag_dtl btd "+
                        " where bk.id=btd.book_id and bk.site_id in("+siteId+") and bk.status in ('free','published') " + optionalCondition+
                        " order by book_type,date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List books = results.collect { book ->
            return [id: book[0], title: book[1], coverImage:""+book[2],
                    language:book[3],bookType: book[4],subject:book[5], buylink1:book[6],listPrice:book[7],offerPrice: book[8]]
        }
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("latestPublisherbooksList_"+siteId+"_"+ publisherId) = element

    }

    @Transactional
    def getNewBooksList(siteIdList,siteId,level,syllabus,grade,keyName,freeBooks,publisherId) {
        getNewBooksList(siteIdList,siteId,level,syllabus,grade,null,keyName,freeBooks,publisherId,-1,null,null,null,"20")
    }
    @Transactional
    def getNewBooksList(siteIdList,siteId,level,syllabus,grade,subject,keyName,freeBooks,publisherId,pageNo,mcqBook,getSubscription,fromApp,noOfBooks){

        def optionalCondition="";
        String limitCondition="";
        int noOfBooksPerPage = 20
        if(noOfBooks!=null&&!"null".equals(noOfBooks)) noOfBooksPerPage = Integer.parseInt(noOfBooks)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results
        def publisherCondition=""
        String levelSiteId = ""+siteId
        BookPriceDtl bookPriceDtl
        String tableList = ""
        String printCheckSql = ""
        String lastSoldSql="last_sold desc,"


        boolean levelPresent=false,syllabusPresent=false,gradePresent=false,subjectPresent=false,publisherPresent=false
        if(!"1".equals(levelSiteId)){
            List siteLevels = LevelsMst.findAllBySiteId(new Integer(""+levelSiteId))
            if(siteLevels.size()==0) levelSiteId="1"
        }
        if(!"1".equals(""+siteId)){
            SiteMst siteMst = dataProviderService.getSiteMst(new Integer(""+siteId))
            if(siteMst.sellPrintOnWhiteLabel==null||"No".equals(siteMst.sellPrintOnWhiteLabel)||"".equals(siteMst.sellPrintOnWhiteLabel)){
                tableList=",book_price_dtl bpd "
                printCheckSql = " and bpd.book_id=bk.id and bpd.book_type not in ('printbook')"
            }
            lastSoldSql=""
        }

        if(keyName.indexOf("related_")==0||keyName.indexOf("bestSellers")==0) limitCondition = " limit 10"
        if(keyName.indexOf("bestSellers")==0) {

        }

        if(level!=null&&!"null".equals(level)&&!"".equals(level)) {
            optionalCondition +=" and btd.level in ("+toSingleQuotes(level)+")"
            levelPresent = true
        }
        if(syllabus!=null&&!"null".equals(syllabus)&&!"".equals(syllabus)) {
            optionalCondition +=" and btd.syllabus in ("+toSingleQuotes(syllabus)+")"
            syllabusPresent=true
        }
        if(grade!=null&&!"null".equals(grade)&&!"".equals(grade)) {
            optionalCondition +=" and btd.grade in ("+toSingleQuotes(grade)+")"
            gradePresent = true
        }
        if(subject!=null&&!"null".equals(subject)&&!"".equals(subject)) {
            optionalCondition +=" and btd.subject in ("+toSingleQuotes(subject)+")"
            subjectPresent = true
        }
        if("true".equals(mcqBook)){
            optionalCondition +=" and bk.has_quiz='true'"
        }
        if(freeBooks){
            optionalCondition +=" and (bk.price=null or bk.price=0 or bk.price=0.0)"
        }

        if("true".equals(getSubscription)) {
            optionalCondition +=" and bk.allow_subscription='Yes' "
        }else{
            optionalCondition +=" and bk.status in ('free','published') "
        }
        publisherCondition = optionalCondition
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) {
            optionalCondition +=" and bk.publisher_id="+publisherId
            publisherPresent = true
        };

        String sql


        if(pageNo==-1) {
            sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,btd.subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId,btd.grade,btd.syllabus,btd.level,bk.medium,coalesce(bk.validity_days,' ') as validityDays," +
                    " CASE WHEN bk.price > 0 THEN 1 ELSE 0 end as free,bk.status,bk.allow_subscription,bk.has_quiz,bk.tests_price,bk.tests_listprice,bk.upgrade_price,bk.isbn,bk.test_type_book "+
                    " from books_mst bk, books_tag_dtl btd, publishers p " +
                    " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ")  and p.id=bk.publisher_id " + optionalCondition +
                    " order by "+lastSoldSql+" date_published  desc" + limitCondition
        }else if(keyName.indexOf("wileyCollectionBooks")==0){
            if(redisService.("wileyCollectionBook")==null) wsshopService.getWileyCollectionMainBook()

            sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,btd.subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId,btd.grade,btd.syllabus,btd.level,bk.medium,coalesce(bk.validity_days,' ') as validityDays," +
                    " CASE WHEN bk.price > 0 THEN 1 ELSE 0 end as free,bk.status,bk.allow_subscription,bk.has_quiz,bk.tests_price,bk.tests_listprice,bk.upgrade_price,bk.isbn,bk.test_type_book "+
                    " from books_mst bk, books_tag_dtl btd, publishers p " +
                    " where bk.id=btd.book_id and bk.id in(" + redisService.("wileyCollectionBooksList") + ")"  +
                    " order by "+lastSoldSql+" date_published  desc"
        }
        else{
            if(pageNo==0||redisService.("publishers_"+siteId+"_"+ keyName+"_"+freeBooks)==null){
                //get the publishers
                sql =
                        "select p.id publisherId,p.name publisher" +
                                " from books_mst bk, books_tag_dtl btd, publishers p " +
                                " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and p.id=bk.publisher_id " + publisherCondition +
                                " group by p.id,p.name order by p.name "

                results = sql1.rows(sql)
                Gson gson = new Gson();
                String element = gson.toJson(results,new TypeToken<List>() {}.getType())
                redisService.("publishers_"+siteId+"_"+ keyName+"_"+freeBooks) = element

            }
            if(redisService.("bookTags_"+siteId+"_"+ keyName+"_"+freeBooks)==null){
                if("true".equals(fromApp)||publisherPresent)
                    sql = "select btd.level,btd.syllabus,btd.grade,btd.subject" +
                            " from books_mst bk, books_tag_dtl btd, publishers p,levels_mst lm " +
                            " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and p.id=bk.publisher_id " + optionalCondition +
                            " and lm.site_id="+levelSiteId+" and lm.name=btd.level "+
                            " group by level,syllabus,grade,subject"
                else if(gradePresent)
                    sql = "select '' level,'' syllabus,btd.grade,btd.subject" +
                            " from books_mst bk, books_tag_dtl btd, publishers p,levels_mst lm " +
                            " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and p.id=bk.publisher_id " + optionalCondition +
                            " and lm.site_id="+levelSiteId+" and lm.name=btd.level "+
                            " group by grade,subject"
                else if(syllabusPresent)
                    sql = "select '' level,btd.syllabus,btd.grade,'' subject" +
                            " from books_mst bk, books_tag_dtl btd, publishers p,levels_mst lm " +
                            " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and p.id=bk.publisher_id " + optionalCondition +
                            " and lm.site_id="+levelSiteId+" and lm.name=btd.level "+
                            " group by syllabus,grade"
                else sql = "select level,syllabus,grade,subject from books_tag_dtl where id=-99"


                results = sql1.rows(sql)
                Gson gson = new GsonBuilder().disableHtmlEscaping().create();
                String element = gson.toJson(results,new TypeToken<List>() {}.getType())
                redisService.("bookTags_"+siteId+"_"+ keyName+"_"+freeBooks) = element

            }
            limitCondition = " limit "+(pageNo*noOfBooksPerPage)+","+noOfBooksPerPage
            sql = "select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId," +
                    " GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium,coalesce(bk.validity_days,' ') as validityDays," +
                    " CASE WHEN price > 0 THEN 1 ELSE 0 end as free,bk.status,bk.allow_subscription,bk.has_quiz,bk.tests_price,bk.tests_listprice,bk.upgrade_price,bk.isbn,bk.test_type_book "+
                    " from books_mst bk,books_tag_dtl btd, publishers p,levels_mst lm" +tableList+
                    "  where bk.id=btd.book_id and lm.site_id=" +levelSiteId+" and lm.name=btd.level and "+
                    " bk.site_id in(" + siteIdList + ")  and p.id=bk.publisher_id " + optionalCondition +printCheckSql+
                    " GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id,last_sold order by "+lastSoldSql+" date_published  desc" + limitCondition

        }

        def result = sql1.rows(sql)
        results = result.groupBy { it.id }.values()*.first()
        boolean defaultEbookPrice = false
        List books = results.collect { book ->
            def price=null,listPrice=null,testsPrice,testsListPrice,upgradePrice,bookgptListPrice,bookgptSellPrice
            defaultEbookPrice= false
            if("81".equals(""+siteId)){
                if(BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"ebook")!=null) defaultEbookPrice = true
            }

            bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"printbook")
            if(bookPriceDtl!=null&&!defaultEbookPrice){
                price = bookPriceDtl.sellPrice
                listPrice = bookPriceDtl.listPrice
            }else{
                bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"ebook")
                if(bookPriceDtl!=null){
                    price = bookPriceDtl.sellPrice
                    listPrice = bookPriceDtl.listPrice
                }
                else{
                    bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"testseries")
                    if(bookPriceDtl!=null){
                        price = bookPriceDtl.sellPrice
                        listPrice = bookPriceDtl.listPrice
                    }
                }
            }
            bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"testseries")
            if(bookPriceDtl!=null){
                testsPrice = bookPriceDtl.sellPrice
                testsListPrice = bookPriceDtl.listPrice
            }
            bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"upgrade")
            if(bookPriceDtl!=null){
                upgradePrice = bookPriceDtl.sellPrice
            }

            bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"bookGPT")
            if(bookPriceDtl!=null){
                bookgptListPrice = bookPriceDtl.listPrice
                bookgptSellPrice = bookPriceDtl.sellPrice
            }
            if(listPrice==null){
                 bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Long(""+book[0]),"ibookgptpro")
                if(bookPriceDtl!=null) {
                    price = bookPriceDtl.sellPrice
                    listPrice = bookPriceDtl.listPrice
                    bookgptListPrice = bookPriceDtl.listPrice
                    bookgptSellPrice = bookPriceDtl.sellPrice
                }
            }
           def priceList = BookPriceDtl.findAllByBookId(new Long(""+book[0]))
            boolean printOnly = false
            if(priceList.size()==1 && priceList[0].bookType=="printbook"){
                printOnly =true
            }
            return [id: book[0], title: book[1], coverImage:""+book[2],
                    language:book[3],bookType: book[4],subject:book.subject?book.subject.split(",")[0]:null, buylink1:book[6],listPrice:listPrice,offerPrice: price,
                    publisher:book[9],publisherId:book[10],grade:book.grade?book.grade.split(",")[0]:null,syllabus: book.syllabus?book.syllabus.split(",")[0]:null,
                    level:book.level?book.level.split(",")[0]:null,medium:book.medium?book.medium:"",status:book.status?book.status:"",allowSubscription:book.allow_subscription?book.allow_subscription:"",
                    validityDays:book.validityDays,hasQuiz:book.has_quiz!=null?book.has_quiz:"",testsPrice:testsPrice,testsListprice:testsListPrice,upgradePrice:upgradePrice,
                    isbn:book.isbn!=null?book.isbn:"",bookgptListPrice:bookgptListPrice,bookgptSellPrice:bookgptSellPrice,printOnly:printOnly,testTypeBook:book.test_type_book]
        }


        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String element = gson.toJson(books.unique(),new TypeToken<List>() {}.getType())

        if(pageNo>-1)
        {
            redisService.("booksList_"+siteId+"_"+ keyName+"_"+freeBooks+"_"+pageNo) = element
            Date date = new Date()
            //set the time of the cache
            redisService.("timeOfBooksList_"+siteId+"_"+ keyName+"_"+freeBooks+"_"+pageNo) = ""+date.getTime()

        }
        else
        {
            redisService.("booksList_"+siteId+"_"+ keyName+"_"+freeBooks) = element

        }
        //not the most elegant solution. But a quick one .. this is delete all keys when book is published or unpublished


    }

    def getBooksList(siteIdList,siteId,level){
        String sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,p.name,p.id publisherId, "+
                " null display_price, "+
                " null sell_price, "+
                " bk.test_type_book,bk.book_type,date_published,bk.test_start_date,bk.test_end_date,bk.language,bk.authors,bk.description "+
                " from books_mst bk, publishers p "+(level!=null?", (select book_id from wsshop.books_tag_dtl where level='"+level+"' group by book_id) btd ":"")+
                " where  bk.site_id in("+siteIdList+") "+(level!=null?"and bk.id=btd.book_id":"")+" and bk.status in ('free','published') and p.id= bk.publisher_id "+
                " order by book_type,date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        def totalMarks,noOfQuestions,language1,language2

        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""

            if("test".equals(book[12])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]));

                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                }
            }

            if(siteId.intValue()==12) {
                return [id: book[0], title: (book[1]), coverImage:""+ book[2], subjectyear: book[3],
                        'listPrice': book[4], 'rating': book[5], 'offerPrice': book[6], 'publisher': book[7], 'publisherId': book[8],
                        'chapterDisplayPrice': book[9], 'chapterSellPrice': book[10], 'chapterType': book[11], 'bookType': book[12],
                        'testStartDate' : book[14] != null ? ("" + book[14]).replace(':', '~') : "",
                        'testEndDate': book[15] != null ? ("" + book[15]).replace(':', '~') : "", 'totalMarks': totalMarks,
                        'noOfQuestions': noOfQuestions, 'language1': language1, 'language2': language2, 'language': book[16],
                        'authors': (""+book[17]).replace(':', ' ').replace(',', '-')]
            } else {
                return [id: book[0], title: (book[1]), coverImage:""+ book[2], subjectyear: book[3],
                        'listPrice': book[4], 'rating': book[5], 'offerPrice': book[6], 'publisher': book[7], 'publisherId': book[8],
                        'chapterDisplayPrice': book[9], 'chapterSellPrice': book[10], 'chapterType': book[11], 'bookType': book[12],
                        'testStartDate'      : book[14] != null ? ("" + book[14]).replace(':', '~') : "",
                        'testEndDate': book[15] != null ? ("" + book[15]).replace(':', '~') : "", 'totalMarks': totalMarks,
                        'noOfQuestions': noOfQuestions, 'language1': language1, 'language2': language2, 'language': book[16],
                        'authors': (""+book[17]).replace(':', ' ').replace(',', '-')]
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("booksList_"+(level!=null?level.replaceAll("\\s+","")+"_":"")+siteId) = element
    }



    def getPublishedPublishersList(siteIdList,siteId){
        String sql = "SELECT p.id,p.name FROM wsshop.publishers p, books_mst bm " +
                "where bm.site_id in ("+siteIdList+") and bm.status='published' and bm.publisher_id=p.id\n" +
                "group by p.id order by p.name;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)


        List publishers = results.collect { publisher ->
            return [publisherId: publisher[0], publisher: publisher[1]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(publishers,new TypeToken<List>() {}.getType())
        redisService.("publishedPublishers_"+siteId) = element
    }

    def getPopularBooksList(siteIdList,siteId,level){
        String sql

        if("all".equals(level)){
            sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,"+
                    " bk.price,p.name,p.id publisherId,bk.book_type,'all',bk.test_start_date,bk.test_end_date"+
                    " from books_mst bk, publishers p,"+
                    " (select book_id, count(book_id) from wslog.books_view_dtl"+
                    "     where date_created > date(DATE_ADD(SYSDATE(),interval -7 day))"+
                    "     group by book_id order by count(book_id) desc limit 20) bvd"+
                    " where bk.id=bvd.book_id and bk.site_id in ("+siteIdList+") and bk.status in ('free','published')"+
                    " and  p.id=bk.publisher_id and bk.book_type not in ('print')"
        } else if("allLevel".equals(level)){
            sql = "(select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,"+
                    " bk.price,p.name,p.id publisherId,bk.book_type,'School',bk.test_start_date,bk.test_end_date" +
                    " from books_mst bk,publishers p, "+
                    " (select ibvd.book_id, count(ibvd.book_id) from wslog.books_view_dtl ibvd, wsshop.books_tag_dtl ibtd"+
                    "     where date_created > date(DATE_ADD(SYSDATE(),interval -7 day)) and ibvd.book_id=ibtd.book_id"+
                    "     and ibtd.level='School'"+
                    "     group by ibvd.book_id order by count(ibvd.book_id) desc limit 20) bvd"+
                    " where  bk.id=bvd.book_id and bk.site_id in ("+siteIdList+") and"+
                    " bk.status in ('free','published') and p.id= bk.publisher_id and bk.book_type not in ('print'))"+
                    " union "+
                    " (select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,"+
                    " bk.price,p.name,p.id publisherId,bk.book_type,'Competitive Exams',bk.test_start_date,bk.test_end_date" +
                    " from books_mst bk, publishers p, "+
                    " (select ibvd.book_id, count(ibvd.book_id) from wslog.books_view_dtl ibvd, wsshop.books_tag_dtl ibtd"+
                    "     where date_created > date(DATE_ADD(SYSDATE(),interval -7 day)) and ibvd.book_id=ibtd.book_id"+
                    "     and ibtd.level='Competitive Exams'"+
                    "     group by ibvd.book_id order by count(ibvd.book_id) desc limit 20) bvd"+
                    " where bk.id=bvd.book_id and bk.site_id in ("+siteIdList+") and"+
                    " bk.status in ('free','published') and p.id= bk.publisher_id and bk.book_type not in ('print'))" +
                    " union "+
                    " (select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,"+
                    " bk.price,p.name,p.id publisherId,bk.book_type,'College',bk.test_start_date,bk.test_end_date" +
                    " from books_mst bk, publishers p, "+
                    " (select ibvd.book_id, count(ibvd.book_id) from wslog.books_view_dtl ibvd, wsshop.books_tag_dtl ibtd"+
                    "     where date_created > date(DATE_ADD(SYSDATE(),interval -7 day)) and ibvd.book_id=ibtd.book_id"+
                    "     and ibtd.level='College'"+
                    "     group by ibvd.book_id order by count(ibvd.book_id) desc limit 20) bvd"+
                    " where bk.id=bvd.book_id and bk.site_id in ("+siteIdList+") and"+
                    " bk.status in ('free','published') and p.id= bk.publisher_id and bk.book_type not in ('print'))"
        } else {
            sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,p.name,p.id"+
                    " publisherId,bk.book_type,'"+level+"',bk.test_start_date,bk.test_end_date" +
                    " from books_mst bk, publishers p, "+
                    " (select ibvd.book_id, count(ibvd.book_id) from wslog.books_view_dtl ibvd, wsshop.books_tag_dtl ibtd"+
                    "     where date_created > date(DATE_ADD(SYSDATE(),interval -7 day)) and ibvd.book_id=ibtd.book_id"+
                    "     and ibtd.level='"+level+"'"+
                    "     group by ibvd.book_id order by count(ibvd.book_id) desc limit 20) bvd"+
                    " where bk.id=bvd.book_id and bk.site_id in ("+siteIdList+") and"+
                    " bk.status in ('free','published') and p.id=bk.publisher_id and bk.book_type not in ('print')"
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        def totalMarks,noOfQuestions,language1,language2
        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""
            if("test".equals(book[9])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]));
                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                }
            }
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6], 'publisher':book[7],'publisherId':book[8],
                    'bookType': book[9],
                    'testStartDate':book[10]!=null?(""+book[10]):"",'testEndDate':book[11]!=null?(""+book[11]):""
                    ,'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("popularbooksList_"+level.replaceAll("\\s+","") +"_"+ siteId) = element

    }

    def getBooksListForUser(){
        getBooksListForUser(springSecurityService.currentUser.username);
    }

    def getBooksListForUser(String username){
        String tempUsername = username.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) username=null

        //adding the site id condition here. This is a quick fix. Correct fix should get the site information and then decide.
        def sql = "select bm.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId,bm.title," +
                " bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,bp.expiry_date,bm.package_book_ids," +
                "'' batch_name,bm.show_in_library,bp.package_book_id" +
                "  from books_mst bm, wsuser.books_permission bp" +
                " where bp.book_id=bm.id and bp.username='"+username+"'" +
                " union "+
                " SELECT book_id,cbd.start_date dateCreated,'book' permissionType,instructor_controlled,bud.batch_id,bm.title," +
                " bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,null expiry_date,bm.package_book_ids," +
                " cbd.name batch_name,bm.show_in_library,'' package_book_id " +
                " FROM wsuser.books_batch_dtl bbd,wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud, books_mst bm" +
                " where bm.id=bbd.book_id and cbd.id=bbd.batch_id" +
                " and cbd.status='active' and bud.username='"+username+"'  and bud.batch_id=cbd.id "+
                " union "+
                //include below books along with user books
                " select bm.id,bm.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId," +
                " bm.title,bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,null expiry_date," +
                "bm.package_book_ids,'' batch_name,bm.show_in_library,'' package_book_id" +
                " from  books_mst bm, key_value_mst kvm,user u" +
                " where  bm.id=kvm.key_value and  bm.site_id=kvm.site_id and u.site_id=kvm.site_id and kvm.key_name='booksToInclude'  and u.username='"+username+"'  "+
                " union "+
                // to get package books
                "select bm1.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                " bm1.title,bm1.book_type,bm1.test_start_date,bm1.test_end_date,bm1.cover_image,bm1.price,bm1.site_id," +
                "bp.expiry_date,bm1.package_book_ids,'' batch_name,bm1.show_in_library,bm.id package_book_id" +
                " from  books_mst bm, books_permission bp, books_mst bm1 " +
                " where bp.username='"+username+"' and bm.id=bp.book_id  " +
                " and bm.package_book_ids is not null and FIND_IN_SET(bm1.id,bm.package_book_ids)!=0 " +
                " and bm1.site_id=bm.site_id "
        "order by dateCreated desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        def totalMarks,noOfQuestions,language1,language2,resId,quizId

        List books = results.collect { book ->

            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""


            BooksTagDtl booksTagDtl = getBooksTagDtl(book.id)
            return [id: book.id,permissionType:book.permissionType,instructorControlled:book.instructorControlled,batchId:book.batchId,
                    title:(book.title),bookType:book.book_type,
                    'dateAdded':book.dateCreated!=null?(""+book.dateCreated):"",
                    'testStartDate':book.test_start_date!=null?(""+book.test_start_date):"",
                    'testEndDate':book.test_end_date!=null?(""+book.test_end_date):""
                    ,'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2,
                    'coverImage':book.cover_image!=null?book.cover_image:"", 'resourceId':resId,'quizId':quizId,price:book.price,siteId:book.site_id,
                    level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                    grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",
                    'expiryDate':book.expiry_date!=null?(""+book.expiry_date).replace(':','~'):"",
                    'packageBookIds':book.package_book_ids,'showInLibrary':book.show_in_library,'packageBookId':book.package_book_id,
                    'batchName':book.batch_name!=null?(""+book.batch_name).replace(':','~'):""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.(username+"_"+"booksList") = element

        getUserBookIds(username,results)
    }

    def getUserBookIds(username,results){
        List books = results.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userBooksListString = String.join(",", userBooksArray);
        else userBooksListString = ""

        redisService.(username+"_"+"bookIds") = userBooksListString
    }

    def getAddOnBooks(bookId){
        def books = ""
        List keyValueMst = redisService.memoizeDomainList(KeyValueMst, "booksAddOn_%") {
            return KeyValueMst.findAllByKeyName("booksAddOn_"+bookId)
        }

        keyValueMst.collect { book ->
            books += (books==""?"":",")+book.keyValue
        }

        return books
    }

    def getBooksListCreatedBy(siteId,publisherId,createdBy){
        String sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status from books_mst bk "+
                " where  bk.site_id in ("+siteId+")"+(publisherId!=null?" and bk.publisher_id="+publisherId:"")+" and created_by='"+createdBy+"' order by bk.id desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6],'status':book[7]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())

        redisService.("booksListForPublishing_"+createdBy) = element
    }

    def getWSUnpublishedMyBooks(){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher,COALESCE(bk.isbn,' ')\n" +
                " from books_mst bk where bk.status is null and bk.site_id>0 and bk.publisher_id is null  and (bk.book_type not in ('print') or bk.book_type is null)  and bk.created_by='"+springSecurityService.currentUser.username+"'\n" +
                "UNION\n" +
                " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,COALESCE(bk.isbn,' ')\n" +
                " from books_mst bk, publishers p where bk.status is  null and bk.site_id>0 and bk.publisher_id=p.id  and bk.created_by='"+springSecurityService.currentUser.username+"'\n" +
                " order by id desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],
                    'rating':book[5],'offerPrice':book[6],'status':book[7], publisher:book[8]!=null? (book[8]):"",isbn:book[9]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("getWSUnpublishedMyBooks_"+springSecurityService.currentUser.username) = element
    }

    def getAllBooks(siteId,publisherId,level,syllabus,grade,subject){
        String siteIdList=""+siteId

        if(siteId.intValue()==1) siteIdList = getSiteIdList(siteId)
        //get all institute publisher ids
        String sql = "select group_concat(distinct(id)) from publishers where site_id in ("+siteIdList+") and publisher_type='systemCreated'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String institutePublisherIds = results[0][0]
        sql ="select bk.id id,bk.title,COALESCE(bk.status,'unpublished'),COALESCE(bk.isbn,' '),bk.language,p.name,  " +
                " CASE WHEN bk.price > 0 THEN 'Paid' ELSE 'Free' end as freePaid,btd.subject "+
                " from books_mst bk, wsshop.books_tag_dtl btd,publishers p "+
                " where bk.id=btd.book_id and bk.site_id in ("+siteIdList+") " ;
        if(publisherId) {
            sql += " and  bk.publisher_id=" + publisherId
        }
        if(level!=null&&!"".equals(level)){
            sql += " and  btd.level='" + level+"'"
        }
        if(syllabus!=null&&!"".equals(syllabus)){
            sql += " and  btd.syllabus='" + syllabus+"'"
        }
        if(grade!=null&&!"".equals(grade)){
            sql += " and  btd.grade='" + grade+"'"
        }
        if(subject!=null&&!"".equals(subject)){
            sql += " and  btd.subject='" + subject+"'"
        }


        if(institutePublisherIds!=null&&institutePublisherIds.length()>0) sql +=" and bk.publisher_id not in ("+institutePublisherIds+") "
        sql += " and p.id= bk.publisher_id " +
                " group by bk.id,btd.subject  order by bk.id desc"


        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        List booksList = results.collect { res ->
            return [id: res[0], title: res[1],status: res[2],isbn:res[3],language: res.language,name:res.name,freePaid:res.freePaid,subject:res.subject]
        }
        return booksList
    }

    def getWSPublishedMyBooks(){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher,COALESCE(bk.isbn,' ')\n" +
                " from books_mst bk where bk.status='published' and bk.publisher_id is null  and (bk.book_type not in ('print') or bk.book_type is null)  and bk.created_by='"+springSecurityService.currentUser.username+"'\n" +
                "UNION\n" +
                " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,COALESCE(bk.isbn,' ')" +
                " from books_mst bk, publishers p where (bk.status='published' ||bk.status='institutePublished' ) and bk.publisher_id=p.id  and bk.created_by='"+springSecurityService.currentUser.username+"'\n" +
                " order by id desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],
                    'rating':book[5],'offerPrice':book[6],'status':book[7], publisher:book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",isbn:book[9]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("getWSPublishedMyBooks_"+springSecurityService.currentUser.username) = element
    }

    def getPrintBooksListForPubDesk(){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher\n" +
                " from books_mst bk where  bk.publisher_id is null and bk.book_type='print'\n" +
                " UNION\n" +
                " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher\n" +
                " from books_mst bk, publishers p where  bk.publisher_id=p.id\n" +
                "  and bk.book_type='print' order by id desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],
                    'rating':book[5],'offerPrice':book[6],'status':book[7], publisher:book[8]!=null? (book[8]):""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("getPrintBooksListForPubDesk") = element
    }

    def getBooksListForPubDeskForPublisher(publisherId){
        String sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status and (bk.book_type not in ('print') or bk.book_type is null) " +
                " from books_mst bk where  bk.status is null and bk.publisher_id= "+publisherId+
                " order by bk.id desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6],'status':book[7]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("getBooksListForPubDeskForPublisher_"+publisherId) = element
    }

    def getPublishedBooksListForAdmin(siteId,start,length,search){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher, null name,COALESCE(bk.isbn,' ') " +
                " from books_mst bk where  bk.publisher_id is null and (bk.book_type not in ('print') or bk.book_type is null) and bk.status='published' and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql +=  " AND (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')";
        }
        sql +=        " UNION " +
                " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,null name,COALESCE(bk.isbn,' ') " +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status='published'   and (bk.title  LIKE '%"+search+"%' OR bk.id LIKE '%"+search+"%' OR p.name LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%') order by id desc limit " + start + "," + length +"";
        }else{
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status='published'  order by id desc limit " + start + "," + length + "";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6],
                    'status':book[7], 'publisher':book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",'creator':book[9],isbn:book[10]]
        }
    }
    def getPublishedBooksListForPublisherAdmin(siteId,start,length,search,publisherId){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,null name,COALESCE(bk.isbn,' '),bk.authors " +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id and bk.site_id="+siteId +" and bk.publisher_id="+publisherId;
        if(search!=null && search!="") {
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and (bk.status = 'published' || bk.status = 'institutePublished')   and (bk.title  LIKE '%"+search+"%' OR bk.id LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%') order by id ";
        }else{
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and (bk.status = 'published' || bk.status = 'institutePublished')  order by id desc limit " + start + "," + length + "";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6],
                    'status':book[7], 'publisher':book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",'creator':book[9],isbn:book[10],'authors':book[11]]
        }
    }

    def getUnpublishedBooksForAdmin(siteId,start,length,search){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher, null name,COALESCE(bk.isbn,' ')" +
                " from books_mst bk where  bk.publisher_id is null and (bk.book_type not in ('print') or bk.book_type is null) " +
                " and bk.status is null  and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql +=  " AND (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')";
        }
        sql +=      " UNION " +
                " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher, null name,COALESCE(bk.isbn,' ')" +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status is null   and (bk.title  LIKE '%"+search+"%' OR bk.id LIKE '%"+search+"%' OR p.name LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%') order by id desc limit " + start + "," + length +"";
        }else{
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status is null   order by id desc limit " + start + "," + length + "";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],
                    'offerPrice':book[6],'status':book[7], 'publisher':book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",'creator':book[9],isbn:book[10]]
        }
    }
    def getUnpublishedBooksForPublisherAdmin(siteId,start,length,search,publisherId){
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher, null name,COALESCE(bk.isbn,' ')" +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id and bk.site_id="+siteId +" and bk.publisher_id="+publisherId;
        if(search!=null && search!="") {
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status is null   and (bk.title  LIKE '%"+search+"%' OR bk.id LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%') order by id ";
        }else{
            sql += " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status is null   order by id desc limit " + start + "," + length + "";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],
                    'offerPrice':book[6],'status':book[7], 'publisher':book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",'creator':book[9],isbn:book[10]]
        }
    }
    def getPublishedBooksCount(siteId,search){
        String sql = "select count(id) from (select  bk.id  FROM  books_mst bk  WHERE  bk.publisher_id IS NULL AND (bk.book_type NOT IN ('print')  OR bk.book_type IS NULL)"+
                " AND bk.status = 'published' and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql +=  " AND (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')";
        }
        sql +=      " UNION SELECT bk.id FROM  books_mst bk,publishers p "+
                " WHERE  bk.publisher_id = p.id and bk.site_id="+siteId +
                " AND (bk.book_type NOT IN ('print') OR bk.book_type IS NULL)";

        if(search!=null && search!="") {
            sql +=  " AND bk.status = 'published'   and (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%'  OR p.name  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')) x;"
        }else{
            sql +=  " AND bk.status = 'published') x;"
        }


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results.get(0).values()
    }

    def getPublishedBooksCountPublisher(siteId,search,publisherId){
        String sql =  "  SELECT count(bk.id) FROM  books_mst bk,publishers p "+
                " WHERE  bk.publisher_id = p.id and bk.site_id="+siteId +
                " AND (bk.book_type NOT IN ('print') OR bk.book_type IS NULL) and p.id="+publisherId;

        if(search!=null && search!="") {
            sql +=  " AND (bk.status = 'published' || bk.status = 'institutePublished')   and (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%');"
        }else{
            sql +=  " AND (bk.status = 'published' || bk.status = 'institutePublished');"
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results.get(0).values()
    }


    def getUnpublishedBooksCount(siteId,search){
        String sql = "select count(id) from (select  bk.id  FROM  books_mst bk  WHERE  bk.publisher_id IS NULL AND (bk.book_type NOT IN ('print')  OR bk.book_type IS NULL)"+
                " AND bk.status is null and bk.site_id="+siteId ;
        if(search!=null && search!="") {
            sql +=  " AND (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')";
        }
        sql +=   " UNION SELECT bk.id FROM  books_mst bk,publishers p"+
                " WHERE  bk.publisher_id = p.id  and bk.site_id="+siteId +
                " AND (bk.book_type NOT IN ('print') OR bk.book_type IS NULL)";
        if(search!=null && search!="") {
            sql +=  " AND bk.status is null   and (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR p.name  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%')) x;"
        }else{
            sql +=  " AND bk.status is null) x;"
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results.get(0).values()
    }
    def getUnpublishedBooksCountPublisher(siteId,search,publisherId){
        String sql =   "  SELECT count(bk.id) FROM  books_mst bk,publishers p"+
                " WHERE  bk.publisher_id = p.id  and bk.site_id="+siteId +
                " AND (bk.book_type NOT IN ('print') OR bk.book_type IS NULL) and p.id="+publisherId;
        if(search!=null && search!="") {
            sql +=  " AND bk.status is null   and (bk.title  LIKE '%"+search+"%' OR bk.id  LIKE '%"+search+"%' OR bk.isbn  LIKE '%"+search+"%');"
        }else{
            sql +=  " AND bk.status is null;"
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results.get(0).values()
    }

    String convertToJsonString(String inputString){
        String jsonString = inputString
        if(jsonString!=null&&jsonString.length()>0) {
            jsonString = jsonString.substring(1, (jsonString.length() - 1))
            jsonString = jsonString.replace('[', '{');
            jsonString = jsonString.replace(']', '}');
            jsonString = "[" + jsonString + "]";
        }

        return jsonString
    }

    def addDoubleQuotes(inputString){
        return inputString.replace("{","{\"").replace("}","\"}").replace(":","\":\"").replace(", ","\", \"").replace("}\"","}").replace("\"{","{");
    }

    def getSiteIdList(siteId){
        String siteIdList="";
        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                List sites = SiteMst.findAllByDisplayInMainSite("Y")

                sites.each{site->
                    siteIdList += site.id+","

                }

                siteIdList = siteIdList.substring(0,(siteIdList.length()-1))
                redisService.("siteIdList_"+siteId) = siteIdList
            }else{
                siteIdList = redisService.("siteIdList_"+siteId)
            }
        }else{
            siteIdList = siteId+""
        }
        return siteIdList
    }

    def getBooksMst(Long bookId){

        BooksMst book
        try {
            book = redisService.memoizeDomainObject(BooksMst, "booksMst_" + bookId) {
                return BooksMst.findById(bookId)
            }
        }catch (Exception e){
            println("Exception to get the book id="+bookId)
            book = BooksMst.findById(bookId)
        }

        return book
    }

    def getBooksDtl(Long bookId){

        BooksDtl book
        try {
            book = redisService.memoizeDomainObject(BooksDtl, "booksDtl_" + bookId) {
                return BooksDtl.findByBookId(bookId)
            }
        }catch (Exception e){
            println("Exception to get the book id="+bookId)
            book = BooksDtl.findByBookId(bookId)
        }

        return book
    }

    def getDirectionsMst(Long directionId){
        DirectionsMst directionsMst  = redisService.memoizeDomainObject(DirectionsMst, "directionsMst_"+directionId) {
            return DirectionsMst.findById(directionId)
        }

        return directionsMst
    }

    def getBooksMstByAsin(String asin){
        BooksMst book  = redisService.memoizeDomainObject(BooksMst, "booksMstAsin_"+asin) {
            return BooksMst.findByAsin(asin)
        }

        return book
    }

    def getBooksMstByIsbn(String isbn){
        BooksMst book  = redisService.memoizeDomainObject(BooksMst, "booksMstIsbn_"+isbn) {
            return BooksMst.findByIsbn(isbn)
        }
        return book
    }

    def getBooksMstByBookCode(String bookCode){
        BooksMst book  = redisService.memoizeDomainObject(BooksMst, "booksMstBookCode_"+bookCode) {
            return BooksMst.findByBookCodeAndStatus(bookCode,"published")
        }
        return book
    }

    def getBooksMstByIsbnAndPublished(String isbn){
        BooksMst book  = redisService.memoizeDomainObject(BooksMst, "booksMstIsbn_"+isbn) {
            return BooksMst.findByIsbnAndStatus(isbn,"published")
        }

        return book
    }

    def getSiteMst(Long siteId){
        SiteMst sitemst  = redisService.memoizeDomainObject(SiteMst, "siteMst_"+siteId) {
            return SiteMst.findById(siteId)
        }

        return sitemst
    }
    def getSiteMstBySiteName(String siteName){
        SiteMst sitemst  = redisService.memoizeDomainObject(SiteMst, "siteMst_"+siteName) {
            return SiteMst.findBySiteName(siteName)
        }

        return sitemst
    }

    def deleteBooksMst(Long bookId){
        redisService.deleteKeysWithPattern("booksMst_"+params.bookId)
    }

    def getBooksTagDtl(Long bookId){
        def currentTag=(redisService.("booksTagDtl_" + bookId))
        if((redisService.("booksTagDtl_" + bookId)!="null" && redisService.("booksTagDtl_" + bookId)!=null)){
            if(BooksTagDtl.findById(currentTag)==null) {
                def bookTagDtl = BooksTagDtl.findByBookId(bookId)
                redisService.deleteKeysWithPattern("booksTagDtl_" + bookId)
                if(bookTagDtl) redisService.("booksTagDtl_" + bookId) = bookTagDtl.id
                return bookTagDtl
            }
            else {
                BooksTagDtl booksTagDtl = redisService.memoizeDomainObject(BooksTagDtl, "booksTagDtl_" + bookId) {
                    return BooksTagDtl.findByBookId(bookId)
                }
                return booksTagDtl
            }
        }
        else {
            BooksTagDtl booksTagDtl = redisService.memoizeDomainObject(BooksTagDtl, "booksTagDtl_" + bookId) {
                return BooksTagDtl.findByBookId(bookId)
            }
            return booksTagDtl
        }
    }

    def getAuthors(Long bookId){
        if(redisService.("authors_"+bookId)) {
            def sql = "SELECT name from authors a, books_author_dtl bad where bad.book_id=" + bookId + " and a.id=bad.author_id"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)

            def authors = ""
            results.each { author ->
                authors = authors + author[0] + " "
            }

            redisService.("authors_"+bookId) = authors
        }
    }

    def getChaptersList(Long bookId,String sort ){
        println("bookId in the beginning"+bookId)
        BooksMst booksMst = getBooksMst(bookId)
        Long queryBookId = bookId

        BooksDtl booksDtl = dataProviderService.getBooksDtl(bookId)
        if(booksDtl!=null&&booksDtl.masterBookId!=null) queryBookId = booksDtl.masterBookId
        println("queryBookId in the afterwards "+queryBookId)
        ChaptersMst chaptersMst1 = ChaptersMst.findByBookIdAndSortOrder(queryBookId,0)
        if(chaptersMst1 != null) sort = "sortOrder"
        String[] bookIds = [""+queryBookId]
        if(booksMst!=null&&booksMst.packageBookIds!=null&&"No".equals(booksMst.showInLibrary)){
            bookIds = (""+bookId+","+ booksMst.packageBookIds).split(',')
        }
        //loop through the bookIds
        for(int i=0;i<bookIds.length;i++){
            booksDtl = dataProviderService.getBooksDtl(new Long(bookIds[i]))
            if(booksDtl!=null&&booksDtl.masterBookId!=null) bookIds[i] = ""+booksDtl.masterBookId
        }


        List chaptersMst = ChaptersMst.findAllByBookIdInList(bookIds.toList(), [sort: sort, order: "asc"])
        Long previewChapterId
        List chapters = chaptersMst.collect { chapter ->
            if(redisService.("totalChapterMcqCount_"+chapter.id)==null) totalChapterMcqCount(chapter.id)
            if(redisService.("chapterResources_" + chapter.id)==null) getChapterResources(chapter.id)
            if("true".equals(chapter.previewChapter)){
                redisService.("previewchapter_"+bookId) = chapter.id
            }

            if("assessmentChapter".equals(chapter.chapterDesc)){
                redisService.("assessmentChapter_"+bookId) = chapter.id

            }

            return [id: chapter.id, bookId: chapter.bookId, name: chapter.name,
                    previewChapter: chapter.previewChapter, desc: chapter.chapterDesc!=null? chapter.chapterDesc:"",
                    chapterId:chapter.id,preview:chapter.previewChapter,totalMcqs:redisService.("totalChapterMcqCount_"+chapter.id)
                    ,totalResources:getChapterResources(chapter.id),mcqsExtracted:chapter.mcqsExtracted]
        }
        Gson gson = new Gson();
        String element = gson.toJson(chapters,new TypeToken<List>() {}.getType())
        redisService.("chapters_"+bookId) = element

    }



    def getChaptersList(Long bookId ){
        getChaptersList(bookId,"id")
    }
    @Transactional
    def getBookRatings(Long bookId){
        def totalRatings, averageRating;
        def sql="SELECT avg(rating),count(rating) FROM wsshop.book_rating_reviews where book_id="+bookId+" and (rating is not null or rating!='')";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        String average

        results.collect{ rating ->
            averageRating = ""+rating[0];
            totalRatings = ""+rating[1]
        }

        List ratings  = BookRatingReviews.findAllByBookId(bookId);
        List reviewRatings = ratings.collect{reviewrating ->
            return [reviewDate: (""+reviewrating.dateCreated), review:reviewrating.review!=null?reviewrating.review:"",
                    rating: reviewrating.rating, reviewer:(User.findByUsername(reviewrating.username)).name, ratingId:reviewrating.id,averageRating:averageRating,totalRatings:totalRatings]

        }
        Gson gson = new Gson();
        String element = gson.toJson(reviewRatings,new TypeToken<List>() {}.getType())
        redisService.("ratings_"+bookId) = element
    }

    def getAuthorsForPublishers(Long publisherId){
        List authors = Authors.findAllByPublisherId(publisherId, [sort: "name"])
        List authorsList = authors.collect{author ->
            return [name: author.name, id:author.id]
        }
        Gson gson = new Gson();
        String element = gson.toJson(authorsList,new TypeToken<List>() {}.getType())
        redisService.("authorsforpublishers_"+publisherId) = element
    }

    def getAuthorsForBook(Long bookId){
        List selectedAuthors = BooksAuthorDtl.findAllByBookId(new Long(bookId))
        List authorsList = selectedAuthors.collect{author ->
            return [authorId:author.authorId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(authorsList,new TypeToken<List>() {}.getType())
        redisService.("authorsforbook_"+bookId) = element
    }

    def getBookTagsForBook(Long bookId){
        List booksTagDtl = BooksTagDtl.findAllByBookId(new Long(bookId))
        List booksTag = booksTagDtl.collect{tag ->
            return [level:tag.level, syllabus: tag.syllabus, grade: tag.grade, subject: tag.subject,id:tag.id]
        }
        Gson gson = new Gson();
        String element = gson.toJson(booksTag,new TypeToken<List>() {}.getType())
        redisService.("tagsforbooks_"+bookId) = element
    }

    def getBooksTagList(String level ) {
        List booksTagDtl = BooksTagDtl.findAllByLevel(level)

        List booksTag = booksTagDtl.collect { tag ->
            return [level:tag.level, syllabus:tag.syllabus, grade:tag.grade, subject:tag.subject, bookId:tag.bookId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(booksTag,new TypeToken<List>() {}.getType())
        redisService.("bookstag_" + level.replaceAll("\\s+","")) = element
    }

    def getBooksTagListWithSiteId(String level,String siteId ) {
        String sql = "select"
        List booksTagDtl = BooksTagDtl.findAllByLevel(level)

        List booksTag = booksTagDtl.collect { tag ->
            return [level:tag.level, syllabus:tag.syllabus, grade:tag.grade, subject:tag.subject, bookId:tag.bookId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(booksTag,new TypeToken<List>() {}.getType())
        redisService.("bookstag_" + level.replaceAll("\\s+","")) = element
    }


    def getDisplayCategories(){
        def sql = "SELECT dc.book_id,dc.category,bm.cover_image,bm.title from display_categories dc, books_mst bm where bm.id=dc.book_id order by dc.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List booksList = results.collect { book ->
            return [bookId: book[0], category: book[1],coverImage:""+book[2],title:book[3]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(booksList,new TypeToken<List>() {}.getType())
        redisService.("displayCategories") = element
    }

    def getCompetitiveExams(){
        def sql = "SELECT distinct(sgd.grade) FROM level_syllabus ls,syllabus_grade_dtl sgd where ls.level=\"Competitive Exams\" and sgd.syllabus=ls.syllabus order by sgd.grade;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List competitiveExams = results.collect { ce ->
            return [grade: ce[0]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(competitiveExams,new TypeToken<List>() {}.getType())
        redisService.("competitiveExams") = element
    }

    def getCompetitiveExamSubjects(){
        def sql = "SELECT sgd.grade,ss.subject FROM syllabus_subject ss,level_syllabus ls,syllabus_grade_dtl sgd  where ls.level=\"Competitive Exams\" and ss.syllabus=ls.syllabus and sgd.syllabus=ls.syllabus order by sgd.grade;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List competitiveExamSubjects = results.collect { ce ->
            return [grade: ce[0], subject: ce[1]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(competitiveExamSubjects,new TypeToken<List>() {}.getType())
        redisService.("competitiveExamSubjects") = element
    }

    @Transactional
    def getPublishers(Integer siteId){
        List publishers
        if(siteId.intValue()==1||siteId.intValue()==25){
            publishers  = Publishers.findAllByPublisherTypeNotEqualOrPublisherTypeIsNull("systemCreated",[sort: "name"]);
        } else {
            publishers  = Publishers.findAllBySiteId(siteId,[sort: "name"]);
        }

        List publishersList = publishers.collect { publisher ->
            return [id: publisher.id, name: publisher.name]
        }
        Gson gson = new Gson();
        String element = gson.toJson(publishersList,new TypeToken<List>() {}.getType())
        redisService.("publishers_"+siteId) = element
    }

    @Transactional
    def getPublisher(Long publisherId){
        Publishers publisher  = redisService.memoizeDomainObject(Publishers, "publisher_"+publisherId) {
            return Publishers.findById(publisherId);
        }

        return publisher
    }



    def getBookHeader(Long bookId){
        HeaderMst headerMst = HeaderMst.findByTypeIdAndHeaderFor(bookId,"book")
        if(headerMst==null) redisService.("bookHeader_"+bookId)="notadded"
        else redisService.("bookHeader_"+bookId) = headerMst.header
    }

    def updateSearchMap(Integer siteId,String siteIdList){
        //to control what to search first, create a list of hashmaps along with main HashMap
        HashMap books = new HashMap()
        HashMap searchMainMap = new HashMap()
        HashMap publishers = new HashMap()
        HashMap authors = new HashMap()
        HashMap syllabus = new HashMap()
        HashMap grade = new HashMap()
        HashMap syllabusGrade = new HashMap()
        HashMap rest = new HashMap()
        servletContext.setAttribute("searchMainMap_"+siteId,searchMainMap);
        String tempBooksIds
        //first start with syllabus
        String sql  = "select distinct level,syllabus" +
                " from wsshop.books_tag_dtl btd,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and bm.id=btd.book_id" +
                " and bm.status='published'"
        " group by level,syllabus "
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("syllabus",book[1])
            value.put("level",book[0])
            searchMainMap.put(book[1]+" books",value)
            syllabus.put(book[1]+" books","syllabus")
        }

        //syllabus level for competitive exams
        sql  = "select distinct level,syllabus,grade" +
                " from wsshop.books_tag_dtl btd,books_mst bm" +
                " where level='Competitive Exams'" +
                " and bm.site_id in ("+siteIdList+")" +
                " and bm.id=btd.book_id" +
                " and bm.status='published'"
        " group by level,syllabus,grade "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("grade",book[2])
            value.put("level",book[0])
            value.put("grade",book[1])
            searchMainMap.put(book[2]+" books",value)
            grade.put(book[2]+" books","grade")
        }

        //syllabus and grade for School and Colleges
        String className;
        sql  = "select distinct level,syllabus,grade" +
                " from wsshop.books_tag_dtl btd,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and bm.id=btd.book_id" +
                " and level not in ('Competitive Exams')" +
                " and bm.status='published'" +
                " group by level,syllabus,grade "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("syllabus",book[1])
            value.put("grade",book[2])
            value.put("level",book[0])
            if("School".equals(book[0])) className="Class "+book[2]
            else className=book[2]
            searchMainMap.put(book[1]+" "+className+" books",value)
            syllabusGrade.put(book[1]+" "+className+" books","gradesyllabus")

        }

        //grade and subject for Competitive exams
        sql  = "select distinct level,syllabus,grade,subject" +
                " from wsshop.books_tag_dtl btd,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and bm.id=btd.book_id" +
                " and level  in ('Competitive Exams')" +
                " and bm.status='published'" +
                " group by level,syllabus,grade,subject "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("level",book[0])
            value.put("syllabus",book[1])
            value.put("grade",book[2])
            value.put("subject",book[3])
            searchMainMap.put(book[2]+" "+book[3]+" books",value)
            rest.put(book[2]+" "+book[3]+" books","rest")
        }

        //syllabus,grade and subject for School and College
        sql  = "select distinct level,syllabus,grade,subject" +
                " from wsshop.books_tag_dtl btd,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and bm.id=btd.book_id" +
                " and level  not in ('Competitive Exams')" +
                " and bm.status='published'" +
                " group by level,syllabus,grade,subject "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("level",book[0])
            value.put("syllabus",book[1])
            value.put("grade",book[2])
            value.put("subject",book[3])
            searchMainMap.put(book[1]+" "+book[2]+" "+book[3]+" books",value)
            rest.put(book[1]+" "+book[2]+" "+book[3]+" books","rest")
        }

        //authors
        sql  = "select distinct a.name" +
                " from authors a, books_author_dtl bad,books_mst bm" +
                " where bm.status='published'" +
                " and bad.book_id=bm.id" +
                " and a.id=bad.author_id" +
                " and bm.site_id in ("+siteIdList+")"
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql);

        results.each { book ->
            HashMap value = new HashMap()
            value.put("author",book[0])
            searchMainMap.put("Books by "+book[0],value)
            authors.put("Books by "+book[0],value)
        }

        //publishers
        sql  = "select distinct p.name,p.id " +
                "from publishers p, books_mst bm" +
                " where bm.status='published'" +
                " and p.id=bm.publisher_id" +
                " and bm.site_id in ("+siteIdList+") group by p.name,p.id "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("publisher",book[0])
            value.put("publisherId",book[1])
            searchMainMap.put(book[0]+" books",value)
            publishers.put(book[0]+" books",value)
        }

        //titles
        sql  = "select id,title,language" +
                " from books_mst bm where status='published' and bm.site_id in ("+siteIdList+") "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("bookId",""+book[0])
            value.put("language",book[2])
            if(books.get(book[1])!=null)
            {
                tempBooksIds = books.get(book[1]).get("bookId")
                value.put("bookId",tempBooksIds+","+book[0])
            }

            searchMainMap.put(book[1],value)
            books.put(book[1],value)
        }

        //ISBN
        sql  = "select id,COALESCE(isbn,'0'),language" +
                " from books_mst bm where status='published' and bm.site_id in ("+siteIdList+") and bm.isbn is not null and bm.isbn <>'0' and bm.isbn <>''"
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("bookId",book[0])
            value.put("language",book[2])
            searchMainMap.put(book[1],value)
            rest.put(book[1],value)
        }

        //keyword
        sql  = "select bm.id,ik.isbn,ik.keyword,language" +
                " from isbn_keyword ik,books_mst bm where  ik.isbn=bm.isbn and status='published' and bm.site_id in ("+siteIdList+") "
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("bookId",book[0])
            value.put("language",book[3])
            searchMainMap.put(book[2],value)
            rest.put(book[2],value)

        }
        List sortedHashMaps = [syllabus,grade,syllabusGrade,books,publishers,authors,rest]
        servletContext.setAttribute("searchMainMap_"+siteId,searchMainMap);
        servletContext.setAttribute("searchMainMapList_"+siteId,sortedHashMaps);
        redisService.("searchMainMap_" + siteId)=null
    }

    def getActiveCourses(Integer siteId){
        List courses = BooksMst.findAllBySiteIdAndBookType(siteId,"course");
        List courseList = courses.collect { course ->
            return [id: course.id, title: course.title]
        }
        Gson gson = new Gson();
        String element = gson.toJson(courseList,new TypeToken<List>() {}.getType())
        redisService.("getActiveCourses_"+siteId) = element
    }

    def getChapterResources(Long chapterId,String sortOrder){
        List  resources = ResourceDtl.findAllByChapterId(chapterId,[sort: sortOrder, order: "asc"])
        List resourcesList = resources.collect { resource ->
            return [id: resource.id, resType: resource.resType, resLink: resource.resLink!=null?resource.resLink.replace("http://","").replace("https://",""):"", filename: resource.filename,
                    resourceName: resource.resourceName, examSyllabus: resource.examSyllabus, description: resource.description!=null?resource.description.replace(':',' ').replace(',',' '):"", sharing: resource.sharing]
        }
        Gson gson = new Gson();
        String element = gson.toJson(resourcesList,new TypeToken<List>() {}.getType())
        redisService.("resources_"+chapterId) = element
    }

    def getChapterDefaultResources(Long chapterId){
        List  resources = ResourceDtl.findAllByChapterIdAndSharingIsNullAndGptResourceTypeIsNull(chapterId)
        List resourcesList = resources.collect { resource ->
            return [id: resource.id]
        }
        Gson gson = new Gson();
        String element = gson.toJson(resourcesList,new TypeToken<List>() {}.getType())
        redisService.("defaultResources_"+chapterId) = element
        ChaptersMst chaptersMst = getChaptersMst(chapterId)

        if(chaptersMst!=null && chaptersMst.bookId!=null ) {
            getAllChapterDetails(chaptersMst.bookId)
            getResIdsOfBook(chaptersMst.bookId)
        }
    }

    def resourceCacheUpdate(resId){
        ResourceDtl resourceDtl = getResourceDtl(resId)
        if(resourceDtl.chapterId!=null&&resourceDtl.chapterId.intValue()!=-999) {
            getChapterDefaultResources(resourceDtl.chapterId)
            getChapterDefaultResourcesAsString(resourceDtl.chapterId)
            getSingleResourceDetails(""+resId)
        }
    }

    def getChapterDefaultResourcesAsString(Long chapterId){
        List  resources = ResourceDtl.findAllByChapterIdAndSharingIsNull(chapterId, [sort: "id", order: "asc"])
        String resourceIds=""
        resources.each{resource->
            resourceIds +=resource.id+","
        }

        if(resourceIds.length()>0) resourceIds=resourceIds.substring(0,(resourceIds.length()-1))
        redisService.("defaultResourceIDs_"+chapterId) = resourceIds
    }

    def getChapterResourcesForUser(Long chapterId,String username){

        List  resources = ResourceDtl.findAllByChapterIdAndCreatedBy(chapterId,username)
        List resourcesList = resources.collect { resource ->
            return [id: resource.id]
        }
        Gson gson = new Gson();
        String element = gson.toJson(resourcesList,new TypeToken<List>() {}.getType())
        redisService.("userResources_"+username+"_"+chapterId) = element
    }

    def getUserBatchesAsStudent(String username){
        List userBatchesList  = BatchUserDtl.findAllByUsernameAndInstructorIsNull(username)
        String userListString
        List batchList = userBatchesList.collect { userBatch ->
            return [batchId: userBatch.batchId]
        }

        String[] userBatchesArray = new String[userBatchesList.size()]
        for(int i=0;i<userBatchesList.size();i++){
            userBatchesArray[i] = userBatchesList.get(i).batchId
        }

        if(userBatchesList.size()>0) userListString = String.join(",", userBatchesArray);
        else userListString = ""

        redisService.("userStudentBatchString_"+username) = userListString
        Gson gson = new Gson();
        String element = gson.toJson(batchList,new TypeToken<List>() {}.getType())
        redisService.("userStudentBatch_"+username) = element
    }
    def getUserBatchesIds(String username){
        List userBatchesList  = BatchUserDtl.findAllByUsername(username)
        String userListString

        String[] userBatchesArray = new String[userBatchesList.size()]
        for(int i=0;i<userBatchesList.size();i++){
            userBatchesArray[i] = userBatchesList.get(i).batchId
        }

        if(userBatchesList.size()>0) userListString = String.join(",", userBatchesArray);
        else userListString = ""

        redisService.("userBatchIds_"+username) = userListString
    }

    def getBatchResourcesForChapter(Long chapterId,Long batchId){
        List batchResources = BatchResourcesDtl.findAllByChapterIdAndBatchId(chapterId, batchId)
        List resourcesList = batchResources.collect { resource ->
            return [id: resource.resId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(resourcesList,new TypeToken<List>() {}.getType())
        redisService.("getBatchRes_"+chapterId+"_"+batchId) = element
    }

    def getChaptersMst(Long chapterId){
        ChaptersMst chapter  = redisService.memoizeDomainObject(ChaptersMst, "chaptersMst_"+chapterId) {
            return ChaptersMst.findById(chapterId)
        }

        return chapter
    }

    def getResourceDtl(Long resId){
        ResourceDtl resourceDtl  = redisService.memoizeDomainObject(ResourceDtl, "resourceDtl_"+resId) {
            return ResourceDtl.findById(resId)
        }

        return resourceDtl
    }

    def getAssessmentBookId(){
        AssessmentBookMst assessmentBookMst = AssessmentBookMst.findById(new Long(1))
        redisService.("assessmentBookId") = ""+assessmentBookMst.bookId
    }

    def getLatestBooksList(siteIdList,siteId,level){
        String sql
        String additionalCondition = " limit 30"
        if("21".equals(""+siteId)) additionalCondition = ""
        if("all".equals(level)){
            sql = " select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                    "(select name from publishers where id=bk.publisher_id) name," +
                    "bk.publisher_id publisherId,bk.book_type,'all',bk.test_start_date,bk.test_end_date,bk.show_discount " +
                    " from books_mst bk "+
                    " where bk.site_id in("+siteIdList+") and bk.status in ('free','published')   " +
                    " group by bk.id  order by date_published desc "+additionalCondition
        }else if("allLevel".equals(level)){
            sql = " ( select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                    "(select name from publishers where id=bk.publisher_id) name," +
                    " bk.publisher_id  publisherId,bk.book_type,'School',bk.test_start_date,bk.test_end_date,bk.show_discount " +
                    " from books_mst bk, wsshop.books_tag_dtl btd"+
                    " where bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published') and btd.level='School'   " +
                    " group by bk.id  order by date_published desc "+additionalCondition+" )"+
                    " union "+
                    " ( select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                    "(select name from publishers where id=bk.publisher_id) name," +
                    " bk.publisher_id  publisherId,bk.book_type,'Competitive Exams',bk.test_start_date,bk.test_end_date,bk.show_discount " +
                    " from books_mst bk, wsshop.books_tag_dtl btd "+
                    " where bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published') and btd.level='Competitive Exams' " +
                    " group by bk.id  order by date_published desc "+additionalCondition+" )"+
                    " union "+
                    " ( select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                    "(select name from publishers where id=bk.publisher_id) name," +
                    " bk.publisher_id  publisherId,bk.book_type,'College',bk.test_start_date,bk.test_end_date,bk.show_discount " +
                    " from books_mst bk, wsshop.books_tag_dtl btd "+
                    " where bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published') and btd.level='College'" +
                    " group by bk.id  order by date_published desc "+additionalCondition+" )"
        }
        else{
            sql = " select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price," +
                    "(select name from publishers where id=bk.publisher_id) name," +
                    " bk.publisher_id  publisherId,bk.book_type,'"+level+"',bk.test_start_date,bk.test_end_date,bk.show_discount " +
                    " from books_mst bk, wsshop.books_tag_dtl btd "+
                    " where bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published') and btd.level='"+level+"' " +
                    " group by bk.id  order by date_published desc "+additionalCondition
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        def totalMarks,noOfQuestions,language1,language2,resourceId
        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""
            resourceId=""

            if("test".equals(book[9])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]));
                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                    resourceId = testDetails[0].resourceID
                }
            }

            return [id: book[0], title: (book[1]),coverImage:book[2]!=null?book[2]:"",subjectyear:book[3],'listPrice':book[4],'rating':book[5],'offerPrice':book[6], 'publisher':book[7],'publisherId':book[8],
                    'bookType': book[9],level:book[10],'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2,
                    'testStartDate':book[11]!=null?(""+book[11]):"",'testEndDate':book[12]!=null?(""+book[12]):"",showDiscount:book[13]!=null?book[13]:""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("latestbooksList_"+level.replaceAll("\\s+","") +"_"+ siteId) = element

    }

    def updateUsageList(Long chapterId){
        String seenResIds=",";
        //sending only the attempted quiz info
        /**  if(redisService.("defaultResourceIDs_"+chapterId)==null) getChapterDefaultResourcesAsString(chapterId)
         if(redisService.("defaultResourceIDs_"+chapterId)!=null&&!"".equals(redisService.("defaultResourceIDs_"+chapterId))) {
         String sql = "SELECT distinct(quizid) FROM wslog.quizrecorder qr " +
         " where qr.username='" + springSecurityService.currentUser.username + "' " +
         "and  qr.quizid in (" + getChapterDefaultResourcesAsString(chapterId) + ")"
         def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
         def sql1 = new SafeSql(dataSource)
         def results = sql1.rows(sql);
         results.each { resource ->
         seenResIds += resource.quizid + ","
         }
         }*/
        redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+chapterId) = seenResIds
    }

    def quizPresent(Long bookId){
        String sql = "SELECT rd.id  FROM chapters_mst cm,resource_dtl rd\n" +
                "           where cm.book_id="+bookId+" and  rd.chapter_id=cm.id\n" +
                "  and rd.res_type in ('Match the answers','Opposites','Fill in the blanks','True or False','Multiple Choice Questions')" +
                " and rd.test_start_date is null"


        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        if(results.size()>0) redisService.("quizPresent_"+bookId)="Present"
        else {
            BooksMst booksMst=BooksMst.findById(new Long(bookId))
            booksMst.hasQuiz=null
            booksMst.save(failOnError: true, flush: true)
            BooksMst.wsuser.executeUpdate("update BooksMst set hasQuiz=null where id="+booksMst.id)
            BooksMst.wsshop.executeUpdate("update BooksMst set hasQuiz=null where id="+booksMst.id)
            redisService.("quizPresent_"+bookId)="Not Present"
        }
    }

    def getUserMst(String username){
        User user  = redisService.memoizeDomainObject(User, "user_"+username) {
            return User.findByUsername(username);
        }

        return user
    }

    def getTestSeriesDtl(String bookId){
        String sql = "select rd.id,em.total_marks,no_of_questions,rd.language1,rd.language2,rd.res_link from" +
                " resource_dtl rd, exam_mst em, chapters_mst cm where cm.book_id="+bookId+
                " and rd.chapter_id=cm.id and em.id=rd.exam_id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List testSeries = results.collect { test ->
            return [resourceID: test[0], totalMarks: test[1], noOfQuestions:test[2],language1:test[3],language2:test[4],quizId:test[5]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(testSeries,new TypeToken<List>() {}.getType())
        redisService.("test_series_"+bookId) = element
    }

    def getLatestTestSeries(siteIdList,siteId){
        String sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,p.name,p.id publisherId," +
                " null,null,bk.test_type_book,bk.book_type,date_published,bk.test_start_date,bk.test_end_date" +
                " from books_mst bk, wsshop.books_tag_dtl btd,publishers p "+
                " where bk.book_type='test' and test_end_date > now() and " +
                "bk.id=btd.book_id and bk.site_id in("+siteIdList+") and bk.status in ('free','published')" +
                " and p.id= bk.publisher_id and  bk.sell_chapter_wise is null" +
                " group by bk.id  order by test_start_date asc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        def totalMarks,noOfQuestions,language1,language2
        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""
            if("test".equals(book[12])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]));
                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                }
            }

            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],
                    'listPrice':book[4],'rating':book[5],'offerPrice':book[6], 'publisher':book[7],'publisherId':book[8],
                    'chapterDisplayPrice':book[9],'chapterSellPrice':book[10],'chapterType':book[11], 'bookType':book[12],
                    'testStartDate':book[14]!=null?(""+book[14]).replace(':','~'):"",'testEndDate':book[15]!=null?(""+book[15]).replace(':','~'):""
                    ,'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("testSeriesList_"+siteId) = element
    }

    def getTestRanks(String resourceId){
        String sql = "select qr.username,qr.rank,qr.score,qr.timetaken,qr.total_questions,qr.wrong_answers,qr.correct_answers,qr.skipped from quizrecorder qr where qr.quizid= " +resourceId+
                " and qr.rank is not null  order by qr.rank asc,qr.timetaken*1 asc limit 10"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        List ranks = results.collect { rank ->
            User user = getUserMst(rank[0])
            return [name: user.name, rank: (rank[1]),userid:user.id,profilepic:user.profilepic,marks:rank[2],timetaken:rank[3],mobile:user.mobile,totalquestions:rank[4],wronganswers:rank[5],correctanswers:rank[6],skipped:rank[7]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(ranks,new TypeToken<List>() {}.getType())
        redisService.("testRanks_"+resourceId) = element
    }

    def bookInBatch(Long bookId){
        BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBookId(bookId)
        if(booksBatchDtl!=null) redisService.("bookInBatch_"+bookId) = "true"
        else redisService.("bookInBatch_"+bookId) = "false"
    }

    def getLatestAppVersion(String appType,Long siteId) {
        AppVersion appVersion = redisService.memoizeDomainObject(AppVersion, "AppVersion_" + appType+"_"+siteId) {
            return AppVersion.findByAppTypeAndSiteId(appType,siteId)
        }

        return appVersion
    }

    def defaultChapterResourceDtls(Long chapterId,BooksTagDtl booksTagDtl,chapterName,chapterDesc,bookId){
        List  resources = ResourceDtl.findAllByChapterIdAndSharingIsNull(chapterId)
        def jsonChapter =  getResourceDetails(resources,booksTagDtl,chapterName,chapterDesc,bookId)

        redisService.("defaultChapterDetail_"+chapterId) = jsonChapter

    }


    def getResourceDetails(List resources,BooksTagDtl booksTagDtl,chapterName,chapterDesc,bookId){
        List jsonChapter = resources.collect { comp ->

            String fileType = "";

            if (comp.resLink != null && ("" + comp.resLink).lastIndexOf('.') != -1) {
                fileType = ("" + comp.resLink).substring(("" + comp.resLink).lastIndexOf('.') + 1)
            };
            boolean ebook=false
            if(comp.filename!=null&&(comp.filename.indexOf(".pdf")!=-1||comp.filename.indexOf(".zip")!=-1)) ebook=true
            def testStartDate="",testEndDate="",testResultDate=""
            if(comp.testStartDate!=null) testStartDate = utilService.convertDate(comp.testStartDate,"UTC","IST")
            if(comp.testEndDate!=null) testEndDate = utilService.convertDate(comp.testEndDate,"UTC","IST")
            if(comp.testResultDate!=null) testResultDate = utilService.convertDate(comp.testResultDate,"UTC","IST")

            return [id                 : comp.id, topicId: comp.chapterId, resType: comp.resType, resLink:comp.resLink!=null?URLEncoder.encode( comp.resLink, "UTF-8" ):null,
                    resName: (comp.resourceName!=null&&!"".equals(comp.resourceName))?URLEncoder.encode( comp.resourceName, "UTF-8" ):"",
                    dateCreated: (""+comp.dateCreated),
                    topicName          : chapterName, syllabus: (booksTagDtl!=null)?booksTagDtl.syllabus: 'syllabus', grade: (booksTagDtl!=null)?booksTagDtl.grade:'grade',
                    subject:(booksTagDtl!=null)?booksTagDtl.subject: 'subject',
                    sharing: comp.sharing,
                    downloadlink1:  comp.downloadlink1!=null?comp.downloadlink1:"",
                    downloadlink2:   comp.downloadlink2!=null?comp.downloadlink2:"",
                    downloadlink3:  comp.downloadlink3!=null?comp.downloadlink3:"",
                    chapterDesc        : chapterDesc!=null?URLEncoder.encode(chapterDesc,"UTF-8"):null,bookId: bookId, fileType: fileType, fileSize: comp.fileSize,
                    level: (booksTagDtl!=null)?booksTagDtl.level: 'level',
                    eBook : ebook,filename:comp.filename!=null?URLEncoder.encode( comp.filename,"UTF-8"):null,
                    testStartDate: (""+testStartDate),testEndDate: (""+testEndDate),
                    videoPlayer:comp.videoPlayer==null?"youtube":comp.videoPlayer,
                    allowComments:comp.allowComments, displayComments:comp.displayComments,
                    testResultDate: (""+testResultDate)]

        }
        Gson gson = new Gson();
        String element = gson.toJson(jsonChapter,new TypeToken<List>() {}.getType())
        return addDoubleQuotes(element)
    }

    def getSingleResourceDetails(resId){
        ExamMst examMst = null
        def examDtl = null
        ResourceDtl resourceDtl = getResourceDtl(new Long(resId))
        List resources = []
        resources << resourceDtl
        List jsonChapter = resources.collect { comp ->

            ChaptersMst chaptersMst = getChaptersMst(comp.chapterId)
            BooksMst booksMst = getBooksMst(chaptersMst.bookId)
            BooksTagDtl booksTagDtl = getBooksTagDtl(booksMst.id)

            String fileType = "";

            if (comp.resLink != null && ("" + comp.resLink).lastIndexOf('.') != -1) {
                fileType = ("" + comp.resLink).substring(("" + comp.resLink).lastIndexOf('.') + 1)
            };
            boolean ebook = false
            if (comp.filename != null && (comp.filename.indexOf(".pdf") != -1 || comp.filename.indexOf(".zip") != -1)) ebook = true
            def testStartDate = "", testEndDate = "", testResultDate = ""
            if (comp.testStartDate != null) testStartDate = utilService.convertDate(comp.testStartDate, "UTC", "IST")
            if (comp.testEndDate != null) testEndDate = utilService.convertDate(comp.testEndDate, "UTC", "IST")
            if (comp.testResultDate != null) testResultDate = utilService.convertDate(comp.testResultDate, "UTC", "IST")


            if (comp.examId != null) {
                examMst = getExamMst(comp.examId)
                if (examMst != null) {
                    if(redisService.("examDtl_" +examMst.id)==null) getExamDtls(examMst.id)
                    examDtl = redisService.("examDtl_" +examMst.id)
                }
            }


            return [id                 : comp.id, topicId: comp.chapterId, resType: comp.resType, resLink: comp.resLink != null && !"blank".equals(comp.resLink) ? URLEncoder.encode(comp.resLink, "UTF-8") : "",
                    resName            : (comp.resourceName != null && !"".equals(comp.resourceName)) ? URLEncoder.encode(comp.resourceName, "UTF-8") : "",
                    dateCreated        : ("" + comp.dateCreated),
                    topicName          : chaptersMst.name, syllabus: (booksTagDtl != null) ? booksTagDtl.syllabus : 'syllabus', grade: (booksTagDtl != null) ? booksTagDtl.grade : 'grade',
                    subject            : (booksTagDtl != null) ? booksTagDtl.subject : 'subject', creatorname: '',
                    wonderSlateEmployee: '', noOfViews: comp.noOfViews, noOfLikes: comp.noOfLikes, noOfFavourites: comp.noOfFavourites, sharing: comp.sharing,
                    downloadlink1:  comp.downloadlink1!=null?comp.downloadlink1:"",
                    downloadlink2:   comp.downloadlink2!=null?comp.downloadlink2:"",
                    downloadlink3:  comp.downloadlink3!=null?comp.downloadlink3:"",
                    language1   : (comp.language1 != null) ? comp.language1 : "",
                    language2  : (comp.language2 != null) ? comp.language2 : "",
                    examDtl    : examDtl,
                    totalMarks : examMst?examMst.totalMarks:"",
                    totalQuestion : examMst?examMst.noOfQuestions:"",
                    totalTime :examMst?examMst.totalTime:"",
                    allowReAttempt:comp.allowReAttempt!=null?comp.allowReAttempt:"",
                    quizMode           : comp.quizMode,
                    canDelete          : false,
                    canEdit            : false,
                    canShare           : false,
                    canPublish         : false,
                    canApprove         : false, hasAlreadyLiked: false,
                    hasAlreadyFav      : false,
                    chapterDesc        : chaptersMst.chapterDesc != null ? URLEncoder.encode(chaptersMst.chapterDesc, "UTF-8") : null,
                    noOfPages          : comp.noOfPages, bookId: booksMst.id, fileType: fileType, fileSize: comp.fileSize, revisionPresent: true,
                    level              : (booksTagDtl != null) ? booksTagDtl.level : 'level',
                    eBook              : ebook, filename: comp.filename != null ? URLEncoder.encode(comp.filename, "UTF-8") : null,
                    testStartDate      : ("" + testStartDate), testEndDate: ("" + testEndDate),
                    videoPlayer        : comp.videoPlayer == null ? "youtube" : comp.videoPlayer,
                    allowComments      : comp.allowComments, displayComments: comp.displayComments,
                    testResultDate     : ("" + testResultDate)]

        }
        Gson gson = new Gson();
        String element = gson.toJson(jsonChapter,new TypeToken<List>() {}.getType())
        redisService.("deepLinkResourceDtl_" + resourceDtl.id) = element
    }

    def booksInBatch(Long batchId){
        List booksBatchDtl = BooksBatchDtl.findAllByBatchId(batchId)

        if(booksBatchDtl.size()>0) {
            List books = booksBatchDtl.collect { book ->
                BooksMst booksMst = getBooksMst(book.bookId)
                return [bookId: booksMst.id, title: booksMst.title.replaceAll("[^a-zA-Z0-9\\s]", " ")]
            }
            Gson gson = new Gson();
            String element = gson.toJson(books,new TypeToken<List>() {}.getType())
            redisService.("booksInBatch_" + batchId) = element
        } else redisService.("booksInBatch_" + batchId)=null
    }

    def getLibraryBooks(instituteId,siteId){

        int noOfBooksPerPage = 10
        String booksList=","
        String sql = " select bk.id,bk.title,bk.cover_image, " +
                "bk.language,bk.authors" +
                " from books_mst bk, wsuser.books_batch_dtl bbd, wsuser.course_batches_dtl cbd  " +
                " where 1=1 " +
                " and  bk.sell_chapter_wise is null " +
                " and cbd.conducted_by="+instituteId+" and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and bk.site_id=" +siteId+" and bbd.batch_id=cbd.id and bk.id=bbd.book_id  order by bk.book_type, date_published desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            BooksTagDtl booksTagDtl = getBooksTagDtl(book[0])
            return [id        : book[0], title: (book[1]), coverImage:""+ book[2],
                    'language': book[3],
                    'authors' : ("" + book[4]),level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                    grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:""]
        }
        InstituteMst instituteMst = InstituteMst.findById(new Long(""+instituteId))
        List books1 = new ArrayList();
        if(siteId==25) noOfBooksPerPage=books.size();
        if (books != null && books.size() >0) {

            int totalNumberOfPages  =  Math.floor(books.size()/noOfBooksPerPage);
            if(books.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<books.size()){
                        tempBooks.add(books.get(booksIndex))
                        booksList +=books[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("librarybooklist_"+ instituteId) = element
                redisService.("librarybooklist_"+ instituteId+"_page_"+(i+1)) = element
            }

            int booksLength=10;
            if(booksLength>books.size()) booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(books.size <= i) break;

                books1.add(books.get(i));
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("startingLibraryBooksList_"+instituteMst.siteId+"_" + instituteId) = element
        redisService.("librarybooklist_"+ instituteId+"_totalBooks") = ""+books.size()
        redisService.("librarybookidlist_"+ instituteId) = booksList

    }

    def getLibraryBooksForUser(instituteId){
        String sql
        String username=springSecurityService.currentUser.username
        sql = " select bk.id,bk.title,bk.cover_image," +
                "bk.language,bk.authors" +
                " from books_mst bk, wsuser.books_batch_dtl bbd, wsuser.course_batches_dtl cbd,wsuser.books_permission bp  " +
                " where 1=1 " +
                " and  bk.sell_chapter_wise is null " +
                " and cbd.conducted_by="+instituteId+" and bbd.batch_id=cbd.id and bk.id=bbd.book_id" +
                " and bp.username='"+username+"' and bp.book_id=bk.id  order by bk.book_type,date_published desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        String bookIds=","
        List books = results.collect { book ->
            bookIds +=book[0]+","
            return [id                   : book[0], title: (book[1]), coverImage:""+ book[2],
                    'language': book[3],
                    'authors'            : (""+book[4])
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("librarybooklistforuser_"+ springSecurityService.currentUser.username) = element
        redisService.("librarybookIdslistforuser_"+ springSecurityService.currentUser.username) = bookIds
    }

    def getBooksTagListForInstitute(instituteId ) {
        String sql = " select bk.id " +
                " from wsuser.books_mst bk, wsuser.books_batch_dtl bbd, wsuser.course_batches_dtl cbd,wsuser.institute_ip_address ip " +
                " where  bk.status in ('free','published')" +
                " and ip.institute_id="+instituteId+" and cbd.conducted_by=ip.institute_id and bbd.batch_id=cbd.id and bk.id=bbd.book_id" +
                " order by book_type,date_published desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List booksTag = results.collect { tag ->
            BooksTagDtl booksTagDtl = getBooksTagDtl(tag.id);
            if (booksTagDtl!=null) {
                return [level: booksTagDtl.level, syllabus: booksTagDtl.syllabus, grade: booksTagDtl.grade, subject: booksTagDtl.subject, bookId: booksTagDtl.bookId]
            }
        }

        Gson gson = new Gson();
        String element = gson.toJson(booksTag,new TypeToken<List>() {}.getType())
        redisService.("bookstag_institute_" + instituteId) = element
    }

    def getLatestVideos(siteIdList,siteId){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title, " +
                "(select grade from books_tag_dtl where book_id=bm.id limit 1) grade, " +
                "(select subject from books_tag_dtl where book_id=bm.id limit 1) syllabus, " +
                "(select level from books_tag_dtl where book_id=bm.id limit 1) level " +
                "from resource_dtl rd,chapters_mst cm, books_mst bm " +
                "where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") " +
                "and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  " +
                "and rd.sharing is null and rd.res_type='Reference Videos' " +
                "order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestVideos = results.collect { video ->
            return [resId: video.id, name: (" "+video.resource_name),
                    link: video.res_link, title: (""+video.title),
                    grade:video.grade,syllabus: video.syllabus,level:video.level]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestVideos,new TypeToken<List>() {}.getType())
        redisService.("latestVideos_" + siteId) = element
    }

    def getLatestQuizzes(siteIdList,siteId){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title, " +
                "(select count(*) from objective_mst where  quiz_id=rd.res_link) noOfQuestions, " +
                "(select grade from books_tag_dtl where book_id=bm.id limit 1) grade, " +
                "(select subject from books_tag_dtl where book_id=bm.id limit 1) syllabus, " +
                "(select level from books_tag_dtl where book_id=bm.id limit 1) level " +
                "from resource_dtl rd,chapters_mst cm, books_mst bm " +
                "where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") " +
                "and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  " +
                "and rd.sharing is null and rd.res_type='Multiple Choice Questions' " +
                "order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestVideos = results.collect { video ->
            return [resId: video.id, name: (" "+video.resource_name),
                    link: video.res_link, title: (""+video.title),
                    noOfQuestions:video.noOfQuestions,
                    grade:video.grade,syllabus: video.syllabus,level:video.level]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestVideos,new TypeToken<List>() {}.getType())
        redisService.("latestQuizzes_" + siteId) = element
    }
    def getLatestQuizzesForGrade(siteIdList,siteId,level,syllabus,grade,keyName){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title, " +
                "(select count(*) from objective_mst where  quiz_id=rd.res_link) noOfQuestions,btd.subject " +
                "from resource_dtl rd,chapters_mst cm, books_mst bm,books_tag_dtl btd " +
                "where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") " +
                "and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  " +
                "and btd.level='"+level+"' and btd.syllabus='"+syllabus+"' and btd.grade='"+grade+"'  and bm.id=btd.book_id "+
                "and rd.sharing is null and rd.res_type='Multiple Choice Questions' " +
                "order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestQuizzes = results.collect { quiz ->
            return [resId: quiz.id, name: (" "+quiz.resource_name),
                    link: quiz.res_link, title: (""+quiz.title),
                    noOfQuestions:quiz.noOfQuestions,subject:quiz.subject]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestQuizzes,new TypeToken<List>() {}.getType())

        redisService.("latestQuizzes_" +keyName+"_"+siteId) = element
    }
    def getLatestVideosForGrade(siteIdList,siteId,level,syllabus,grade,keyName){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title, " +
                "btd.subject " +
                "from resource_dtl rd,chapters_mst cm, books_mst bm,books_tag_dtl btd " +
                "where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") " +
                "and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  " +
                "and btd.level='"+level+"' and btd.syllabus='"+syllabus+"' and btd.grade='"+grade+"' and bm.id=btd.book_id "+
                "and rd.sharing is null and rd.res_type='Reference Videos' " +
                "order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestVideos = results.collect { video ->
            return [resId: video.id, name: (" "+video.resource_name),
                    link: (""+video.res_link),
                    title: (""+video.title),
                    subject:video.subject]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestVideos,new TypeToken<List>() {}.getType())
        redisService.("latestVideos_" +keyName+"_"+siteId) = element
    }

    def getInstituteMst(Long instituteId){
        InstituteMst instituteMst  = redisService.memoizeDomainObject(InstituteMst, "instituteMst_"+instituteId) {
            return InstituteMst.findById(instituteId);
        }

        return instituteMst
    }

    def getInstituteMstByUrl(Integer siteId,String urlName){
        InstituteMst instituteMst  = redisService.memoizeDomainObject(InstituteMst, "instituteMst_"+urlName) {
            return InstituteMst.findBySiteIdAndUrlname(siteId,urlName);
        }

        return instituteMst
    }

    def getVideoMst(String videoId){
        VideoMst videoMst  = redisService.memoizeDomainObject(VideoMst, "videoMst_"+videoId) {
            return VideoMst.findByVideoId(videoId)
        }

        return videoMst
    }

    def getRhymeLanguages(){
        String sql = "select distinct(language) from video_mst order by language";

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List languages = results.collect { language ->
            return [language: language.language]
        }
        Gson gson = new Gson();
        String element = gson.toJson(languages,new TypeToken<List>() {}.getType())
        redisService.("rhymelanguages") = element
    }

    def getVideos(String language){
        String sql = "select vm.id,vm.language,vm.title,vm.video_id,vm.video_type,cm.name channelName from video_mst vm,wsshop.channel_mst cm where vm.language='"+language+"' and cm.channel_id=vm.channel_id";

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List videos = results.collect { video ->
            return [language: video.language,title:video.title ,
                    id:video.id,videoId:video.video_id, videoType:video.video_type,channelName:video.channelName]
        }
        Gson gson = new Gson();
        String element = gson.toJson(videos,new TypeToken<List>() {}.getType())
        redisService.("languagevideos_"+language) = element
    }

    def getChannelVideos(String channelId){
        String sql = "select vm.id,vm.language,vm.title,vm.video_id,vm.video_type from video_mst vm where vm.channel_id='"+channelId+"' order by id desc limit 100";

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List videos = results.collect { video ->
            return [language: video.language,title:video.title ,
                    id:video.id,videoId:video.video_id, videoType:video.video_type]
        }
        Gson gson = new Gson();
        String element = gson.toJson(videos,new TypeToken<List>() {}.getType())
        redisService.("channelVideos_"+channelId) = element
    }

    def refreshCacheForPublishUnpublish(bookId,siteId){
        getWSUnpublishedMyBooks();
        getWSPublishedMyBooks();
        Date date = new Date()
        redisService.("timeOfLastUpdate") = ""+date.getTime()
    }

    def getSupportBookId(){
        KeyValueMst keyValueMst = redisService.memoizeDomainObject(KeyValueMst, "supportBookId") {
            return KeyValueMst.findByKeyName("supportBook")
        }

        return keyValueMst.keyValue
    }

    def getLatestNotifications(siteId){
        String sql = "select id,title,body,image_url,message_type,link,send_to,status,date_created from wscomm.notification_dtl where date_created >= DATE_ADD(sysdate(), INTERVAL -2 DAY) \n" +
                " and date_created <= sysdate() and site_id="+siteId+" order by id desc ";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List notifications = results.collect { notification ->
            return [notificationId:notification.id,title:(notification.title!=null?replaceJSONTroublers(notification.title):""),
                    body:(notification.body!=null?replaceJSONTroublers(notification.body):""),imageUrl:(notification.image_url!=null?replaceJSONTroublers(notification.image_url):""),
                    link:(notification.link!=null?replaceJSONTroublers(notification.link):""),
                    sendTo:(notification.send_to!=null?replaceJSONTroublers(notification.send_to):""),messageType:notification.message_type,status:notification.status,
                    sentTime:(notification.date_created!=null?replaceJSONTroublers(""+notification.date_created):"")]
        }
        if(notifications.size()>0) {
            redisService.("latestNotificationId_" + siteId) = "" + notifications[0].notificationId
            notifications = notifications.reverse()
            Gson gson = new Gson();
            String element = gson.toJson(notifications,new TypeToken<List>() {}.getType())
            redisService.("newNotifications_"+siteId) = element
        }else{
            sql = "select max(id) notificationId from wscomm.notification_dtl"
            dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
            sql1 = new SafeSql(dataSource)
            results = sql1.rows(sql);
            redisService.("latestNotificationId_" + siteId) = "" + results[0].notificationId
        }


    }

    def getMainCategories(siteId,level) {
        List categories = null
        String siteIdList = siteId.toString()
        List syllabusGrades = SyllabusGradeDtl.findAllBySiteId(siteId)
        //if the site id is 1 or if there is no syllabus specific syllabus/grade info
        if (siteId.intValue() == 1 || syllabusGrades.size()==0) {
            if (redisService.("siteIdList_" + siteId) == null) {
                getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_" + siteId)
            if(siteIdList==null)siteIdList=siteId;

            String additionalCondition = ""
            if (level != null) additionalCondition = " and btd.level='" + level + "' ";

            String sql = "select level,syllabus,grade from wsshop.books_tag_dtl btd,books_mst bm where btd.book_id=bm.id and bm.status='published' and bm.site_id in (" + siteIdList + ") " + additionalCondition + " group by level,syllabus,grade";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);

            syllabusGrades = SyllabusGradeDtl.findAll()
            def gradeId = null
            categories = results.collect { tag ->
                gradeId = null
                for (int i = 0; i < syllabusGrades.size(); i++) {
                    if (("" + syllabusGrades[i].syllabus).equals(tag.syllabus) && ("" + syllabusGrades[i].grade).equals(tag.grade)) {
                        gradeId = syllabusGrades[i].id
                        break
                    }
                }
                if (gradeId != null) return [level: tag.level, syllabus: tag.syllabus, grade: tag.grade, gradeId: gradeId]
            }
        }else{


            categories = syllabusGrades.collect { tag ->
                LevelSyllabus levelSyllabus = LevelSyllabus.findBySyllabusAndSiteId(tag.syllabus,tag.siteId)
                return [level: levelSyllabus!=null?levelSyllabus.level:"", syllabus: tag.syllabus, grade: tag.grade, gradeId: tag.id]
            }
        }
        while (categories.remove(null));
        Gson gson = new Gson();
        String element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        if(level==null) redisService.("maincategories_" + siteId) = element
        else redisService.("maincategories_" + siteId+"_"+level.replaceAll("\\s+","")) = element
    }

    def getFeaturedCategories(siteId){
        String sql = "select sgd.id gradeId,grade,sgd.syllabus,ls.id syllabusId from syllabus_grade_dtl sgd,level_syllabus ls,featured_grade_dtl fgd where ls.syllabus=sgd.syllabus \n" +
                "and sgd.id=fgd.grade_id and fgd.site_id="+siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List categories = results.collect { tag ->
            return [gradeId:tag.gradeId, syllabus:tag.syllabus, grade:tag.grade,syllabusId:tag.syllabusId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(categories,new TypeToken<List>() {}.getType())
        redisService.("featuredcategories_" + siteId) = element
    }

    def getLastReadBooks(username){
        def bookIds="";
        String queryappend="";
        String sql = "select max(date_created) max_date ,book_id\n" +
                "from books_view_dtl \n" +
                "where view_type='library' and  username='"+username+"'\n" +
                "group by book_id\n" +
                "order by max_date desc limit 10"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List bookslist;
        def instituteId
        results.collect { book ->
            bookIds += book.book_id+",";
        }
        if(bookIds!=null && bookIds!="" ) {
            queryappend = bookIds.substring(0, bookIds.length() - 1);
            String sqls = "select bm.id,bm.title,bm.cover_image,bm.publisher_id,bm.book_type,bp.batch_id \n" +
                    "from books_mst bm,books_permission bp \n" +
                    "where bp.book_id=bm.id and bp.book_id IN ("+queryappend+") and bp.username='"+username+"' order by bp.date_created desc";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql11 = new Sql(dataSource1)
            def results1 = sql11.rows(sqls);
            String publisherName
            Publishers publishers = null
            bookslist = results1.collect { books ->
                publisherName = ''
                if(books.publisher_id!=null && books.publisher_id!= ""){
                    publishers = dataProviderService.getPublisher(new Long(books.publisher_id))
                    if (publishers!=null) publisherName = publishers.name
                }
                return [bookId: books.id, bookTitle: books.title, title: books.title,coverImage: books.cover_image != null ? books.cover_image : "",bookType:(books.book_type!=null)?books.book_type:"",
                        publisher: publisherName,
                        batchId:books.batch_id!=null?books.batch_id:""]
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(bookslist,new TypeToken<List>() {}.getType())
        redisService.("lastReadBooks_" + username) = element
    }



    String replaceJSONTroublers(inputString){
        String outputString

        //replace : with ~, comma with ~~
        outputString = inputString.replaceAll(":","~").replaceAll(",","~~").replace("[","(").replace("]",")").replace("{","(").replace("}",")")
        return outputString
    }

    def getIntroVideos(){
        String sql = "select key_name,key_value from key_value_mst where key_name in ('youtube_intro_hindi','youtube_intro_english')";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List videos = results.collect { video ->
            return [language: video.key_name,link:video.key_value]
        }
        Gson gson = new Gson();
        String element = gson.toJson(videos,new TypeToken<List>() {}.getType())
        redisService.("introVideos") = element
    }

    def getPublisherBooksList(siteIdList,publisherId){

        String sql =
                "select bk.id,bk.title,bk.cover_image,bk.language,\n" +
                        "bk.book_type,btd.subject,bk.buylink1,bk.listprice,\n" +
                        "bk.price,btd.level,btd.syllabus,btd.grade \n" +
                        "from books_mst bk, (select book_id, min(grade) grade, min(level) level, min(syllabus) syllabus, min(subject) subject from wsshop.books_tag_dtl btd group by book_id) btd  \n" +
                        "where bk.id=btd.book_id \n" +
                        "and bk.site_id in ("+siteIdList+") and bk.status in ('free','published') " + " and bk.publisher_id="+publisherId+
                        " group by bk.id order by book_type,date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List books = results.collect { book ->
            return [id: book[0], title: book[1],
                    language:book[3],bookType: book[4],subject:book[5], buylink1:book[6]!=null?book[6]:"",listPrice:book[7],offerPrice: book[8],
                    level:book[9],syllabus: book[10],grade:book[11]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("publishersBooksList_"+publisherId) = element
    }

    def getPublisherByUrlname(String urlName){
        Publishers publisher  = redisService.memoizeDomainObject(Publishers, "publisherurlname_"+urlName) {
            return Publishers.findByUrlname(urlName)
        }

        return publisher
    }
    def getLatestQuizzesForPublisher(siteIdList,siteId,publisherId){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title, " +
                " (select count(*) from objective_mst where  quiz_id=rd.res_link) noOfQuestions " +
                " from resource_dtl rd,chapters_mst cm, books_mst bm " +
                " where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") " +
                " and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  and bm.publisher_id=" +publisherId+
                " and rd.sharing is null and rd.res_type='Multiple Choice Questions' " +
                " order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestQuizzes = results.collect { quiz ->
            return [resId: quiz.id, name: (" "+quiz.resource_name),
                    link: quiz.res_link, title: (""+quiz.title),
                    noOfQuestions:quiz.noOfQuestions]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestQuizzes,new TypeToken<List>() {}.getType())
        redisService.("latestQuizzesPublisher_"+publisherId+"_"+siteId) = element
    }

    def getLatestVideosForPublisher(siteIdList,siteId,publisherId){
        String sql = "select rd.id,rd.resource_name,res_link,bm.title " +
                " from resource_dtl rd,chapters_mst cm, books_mst bm " +
                " where rd.chapter_id=cm.id and cm.book_id=bm.id and bm.site_id in ("+siteIdList+") and bm.publisher_id=" +publisherId+
                " and bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true')  " +
                " and rd.sharing is null and rd.res_type='Reference Videos' " +
                " order by rd.id desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List latestVideos = results.collect { video ->
            return [resId: video.id, name: (" "+video.resource_name),
                    link: (""+video.res_link),
                    title: (""+video.title)
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(latestVideos,new TypeToken<List>() {}.getType())
        redisService.("latestVideosPublisher_" +publisherId+"_"+siteId) = element
    }

    def getAllChapterDetails(bookId){

        Long queryBookId = bookId

        BooksDtl booksDtl = dataProviderService.getBooksDtl(bookId)
        if(booksDtl!=null&&booksDtl.masterBookId!=null) queryBookId = booksDtl.masterBookId

        String sql = "select rd.id,rd.resource_name,res_link,cm.name,cm.id chapterId,rd.res_type,cm.preview_chapter,cm.sort_order " +
                " from resource_dtl rd,chapters_mst cm " +
                " where rd.chapter_id=cm.id and cm.book_id="+queryBookId +
                " and rd.sharing is null" +
                " order by cm.sort_order,rd.id asc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List chapterDetails = results.collect { resource ->
            return [resId: resource.id, name: (" "+resource.resource_name),
                    link: (""+resource.res_link),
                    chapterName: (""+resource.name),
                    chapterId:resource.chapterId,resType:resource.res_type,bookId:bookId,previewChapter:resource.preview_chapter,
                    sortOrder:resource.sort_order
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(chapterDetails,new TypeToken<List>() {}.getType())
        redisService.("allChapterDetails_" +bookId) = element
    }

    def getBookDetails(bookId){
        List books = []
        books << getBooksMst(new Long(bookId))

        List jsonBook = books.collect { booksMst ->
            if (redisService.("authors_" + bookId) == null) {
                getAuthors(new Long(bookId))
            }

            def authors = redisService.("authors_" + bookId)
            Publishers publisher = getPublisher(booksMst.publisherId)

            List reviewRatings = []
            return [
                    bookDesc   :booksMst.description!=null?booksMst.description:null,
                    price      :booksMst.price!=null?booksMst.price:null, authors:authors, rating:'',
                    isbn       :booksMst.isbn!=null?booksMst.isbn:null,
                    coverImage :booksMst.coverImage!=null?booksMst.coverImage:null,
                    publisher  :publisher!=null && publisher.name!=null?publisher.name:null,
                    publisherId:publisher!=null?publisher.id:null, bookId:booksMst.id,
                    title      :booksMst.title,
                    listPrice  :booksMst.listprice, testTypeBook:booksMst.testTypeBook, reviewratings:reviewRatings.size()>0?reviewRatings:null,
                    buylink1   :booksMst.buylink1!=null?booksMst.buylink1:null,
                    buylink2   :booksMst.buylink2!=null?booksMst.buylink2:null,
                    rating1    :booksMst.reviewLink1, rating2:booksMst.reviewLink2, status:booksMst.status,showDiscount:booksMst.showDiscount!=null?booksMst.showDiscount:""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(jsonBook,new TypeToken<List>() {}.getType())
        redisService.("deepLinkChapterDetails_" +bookId) = element
    }

    def getSyllabusGradeDtl(Long gradeId){
        SyllabusGradeDtl syllabusGradeDtl  = redisService.memoizeDomainObject(SyllabusGradeDtl, "syllabusGradeDtl_"+gradeId) {
            return SyllabusGradeDtl.findById(gradeId)
        }

        return syllabusGradeDtl
    }


    def getLiveVideos(siteId){

        def siteIdList = ""+siteId
        if((""+siteId).equals("1")){
            siteIdList = getSiteIdList(new Integer("1"))
        }
        String sql = "SELECT rd.id,rd.res_link,rd.resource_name,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time, " +
                " DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time, " +
                " cm.name chapter_name, bm.title," +
                "rd.allow_comments,rd.display_comments,rd.video_player,bm.id bookId \n" +
                "FROM resource_dtl rd, chapters_mst cm, books_mst bm\n" +
                "where res_type='Reference Videos' and sharing is null \n" +
                "and rd.test_start_date is not null and rd.test_start_date > date_add(sysdate(), INTERVAL -24 hour)\n" +
                "and rd.chapter_id =cm.id and cm.book_id =bm.id and bm.site_id in (" +siteIdList+")"+
                " order by start_time desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List videoDetails = results.collect { resource ->
            return [resId: resource.id, resLink:resource.res_link,startTime:(""+resource.start_time),endTime:resource.end_time!=null?(""+resource.end_time):null,
                    name: (" "+resource.resource_name),
                    chapterName: (""+resource.chapter_name),
                    bookTitle: (""+resource.title),
                    allowComments:resource.allow_comments,displayComments: resource.display_comments,videoPlayer: resource.video_player,bookId:resource.bookId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(videoDetails,new TypeToken<List>() {}.getType())
        redisService.("liveVideos_" +siteId) = element

        return;
    }

    def getLiveTests(siteId){
        def siteIdList = ""+siteId
        if((""+siteId).equals("1")){
            siteIdList = getSiteIdList(new Integer("1"))
        }
        String sql = "SELECT rd.id,rd.res_link,rd.resource_name,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time, " +
                " DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time, " +
                " DATE_ADD( rd.test_result_date, INTERVAL '5:30' HOUR_MINUTE) result_time, " +
                " cm.name chapter_name, bm.title," +
                " bm.id bookId \n" +
                "FROM resource_dtl rd, chapters_mst cm, books_mst bm\n" +
                "where res_type='Multiple Choice Questions' and sharing is null \n" +
                "and rd.test_start_date is not null and rd.test_start_date > date_add(sysdate(), INTERVAL -744 hour)\n" +
                "and rd.chapter_id =cm.id and cm.book_id =bm.id and bm.site_id in (" +siteIdList+")"+
                " order by start_time desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List videoDetails = results.collect { resource ->
            return [resId: resource.id, resLink:resource.res_link,startTime:(""+resource.start_time),endTime:resource.end_time!=null?(""+resource.end_time):null,
                    resultTime:resource.result_time!=null?(""+resource.result_time):null,name: (" "+resource.resource_name),
                    chapterName: (""+resource.chapter_name),
                    bookTitle: (""+resource.title),
                    bookId:resource.bookId]
        }
        Gson gson = new Gson();
        String element = gson.toJson(videoDetails,new TypeToken<List>() {}.getType())
        redisService.("liveTests_" +siteId) = element
        return;
    }

    def getPurchaseOrder(Long poNo){
        PurchaseOrder purchaseOrder  = redisService.memoizeDomainObject(PurchaseOrder, "purchaseOrder_"+poNo) {
            return PurchaseOrder.findById(poNo)
        }

        return purchaseOrder
    }

    def getLastPaymentIdRecord(siteId){
        KeyValueMst keyValueMst = redisService.memoizeDomainObject(KeyValueMst, "lastPaymentId_"+siteId) {
            return KeyValueMst.findByKeyNameAndSiteId("lastPaymentId",siteId)
        }

        return keyValueMst
    }

    def isResourceLoggingEnabled(){
        KeyValueMst keyValueMst = redisService.memoizeDomainObject(KeyValueMst, "resourceLogEnabled") {
            return KeyValueMst.findByKeyName("resourceLogEnabled")
        }

        return keyValueMst!=null?keyValueMst.keyValue:""
    }

    def isGoogleAnayticsEnabled(){
        KeyValueMst keyValueMst = redisService.memoizeDomainObject(KeyValueMst, "googleLogEnabled") {
            return KeyValueMst.findByKeyName("googleLogEnabled")
        }

        return keyValueMst!=null?keyValueMst.keyValue:""
    }

    def getChatMessages(resId,chatMessageBucketSize,Integer currentChatNo){
        int offset = 0;
        int quotient = currentChatNo / chatMessageBucketSize
        offset = quotient * chatMessageBucketSize

        String sql = "select id,question,name,res_id,date_created,username,admin_user,chat_no,status,to_user from questions where res_id="+
                resId+" order by chat_no limit "+offset+","+chatMessageBucketSize
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)


        List chatMessages = results.collect { comment ->
            return [messageId: comment.id, sender: (comment.name!=null?replaceJSONTroublers(comment.name):""),
                    message:(comment.question!=null?replaceJSONTroublers(comment.question):""),
                    resId:comment.res_id,
                    messageType:'all messages',
                    dateCreated:(comment.date_created!=null?replaceJSONTroublers(""+comment.date_created):""),
                    username: comment.username,
                    adminUser: comment.admin_user,
                    chatMessageNo:comment.chat_no,
                    status:comment.status,
                    toUser:comment.to_user!=null&&!"".equals(comment.to_user)?userManagementService.encrypt(comment.to_user):""
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(chatMessages,new TypeToken<List>() {}.getType())
        redisService.("chatMessages_" +resId+"_"+quotient) = element
    }

    def getUserFolders(username){
        String sql = "SELECT folder_name folderName, date_created dateCreated,id folderId from folder_mst where username='"+username+"' order by folder_name"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List userFolders = results.collect { folder ->
            return [folderName: folder.folderName, dateCreated: folder.dateCreated,
                    folderId  : folder.folderId
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(userFolders,new TypeToken<List>() {}.getType())
        redisService.("folders_" +username) = element
    }

    def getFolderContents(folderId){
        String sql = "SELECT rd.res_type resType, rd.date_created dateCreated,rd.resource_name resName,rd.id resId,rd.res_link resLink from folder_dtl fd," +
                " resource_dtl rd where fd.folder_id='"+folderId+"' and rd.id=fd.res_id order by fd.id desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List folderContents = results.collect { contents ->
            return [resType: contents.resType, dateCreated: contents.dateCreated,
                    resName: contents.resName,resId: contents.resId,resLink:contents.resLink
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(folderContents,new TypeToken<List>() {}.getType())
        redisService.("folderDtl_" +folderId) = element
    }
    def getBookResources(bookId){
        /** String regex = "[0-9]+";
         if((""+bookId).matches(regex)) {
         String bookIds = bookId
         BooksMst booksMst = BooksMst.findById(new Integer(bookIds))
         if(booksMst.packageBookIds!=null && !"".equals(booksMst.packageBookIds)) bookIds= booksMst.packageBookIds
         String sql = "select res_type,count(*) res_count" +
         " from resource_dtl rd, chapters_mst cm" +
         " where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.book_id in (" + bookIds +") "+
         " group by res_type" +
         " order by res_type ; "

         def dataSource = grailsApplication.mainContext.getBean('dataSource')
         def sql1 = new SafeSql(dataSource)
         def results = sql1.rows(sql);
         Gson gson = new Gson();
         String element = gson.toJson(results, new TypeToken<List>() {}.getType())
         redisService.("bookResources_" + bookId) = element
         }*/
        def results = new ArrayList<>();
        Gson gson = new Gson();
        String element = gson.toJson(results, new TypeToken<List>() {}.getType())
        redisService.("bookResources_" + bookId) = element

    }

    def getChapterResources(chapterId){
        String regex = "[0-9]+";
        if((""+chapterId).matches(regex)) {
            String sql = "select res_type,count(*) res_count" +
                    " from resource_dtl rd, chapters_mst cm" +
                    " where rd.chapter_id=cm.id and rd.sharing is null and rd.id>0 and cm.id=" + chapterId +
                    " group by res_type" +
                    " order by res_type ; "

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);
            Gson gson = new Gson();
            String element = gson.toJson(results, new TypeToken<List>() {}.getType())
            redisService.("chapterResources_" + chapterId) = element
        }

    }

    def getDefaultFlashCards(siteId){
        KeyValueMst keyValueMst = redisService.memoizeDomainObject(KeyValueMst, "defaultFlashCards_"+siteId) {
            return KeyValueMst.findByKeyNameAndSiteId("flashCardsDefault",siteId)
        }

        return keyValueMst
    }

    def getUserFavResIds(){
        String sql = "SELECT GROUP_CONCAT(res_id)\n" +
                " FROM  favourite_resources  WHERE  username ='"+springSecurityService.currentUser.username+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String resIds
        if(results!=null||results.size()>0) {
            resIds = results[0][0]
        }

        redisService.(springSecurityService.currentUser.username+"_favResIds")=resIds
    }

    def getResIdsOfBook(bookId){
        if((""+bookId).isNumber()) {
            String sql = "SELECT GROUP_CONCAT(rd.id)\n" +
                    " FROM  resource_dtl rd,chapters_mst cm  WHERE  cm.book_id =" + bookId + " and cm.id=rd.chapter_id and rd.sharing is null order by cm.id"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            SafeSql sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)

            String resIds
            if (results != null || results.size() > 0) {
                resIds = results[0][0]
            }

            redisService.("BookResIds_" + bookId) = resIds
        }

    }

    def getExamMst(Long examId){
        ExamMst examMst  = redisService.memoizeDomainObject(ExamMst, "examMst_"+examId) {
            return ExamMst.findById(examId)
        }

        return examMst
    }

    def getExamDtls(Long examId){
        String sql = "select  exam_id examId,no_of_questions noOfQuestions,total_marks totalMarks,right_answer_marks rightAnswerMarks,wrong_answer_marks wrongAnswerMarks,subject,total_time totalTime from exam_dtl where exam_id="+examId
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("examDtl_" +examId) = element
    }

    def getBooksCountForPublisher(siteId,siteIdList,publisherId){
        String additionalCondition=""
        if(publisherId!=null) additionalCondition = " and bk.publisher_id="+publisherId
        String sql = " select count(bk.id)" +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id " ;
        if(siteId!=3) {
            sql += " and bk.site_id in (" + siteIdList + ")";
        }
        sql +=   ""+additionalCondition+"" +
                " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status is null ";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)


        if(publisherId==null) redisService.("unpublishedCount_"+siteId)=""+results[0][0]
        else redisService.("unpublishedCount_"+siteId+"_pub_"+publisherId)=""+results[0][0]

        sql = " select count(bk.id)" +
                " from books_mst bk, publishers p  where bk.publisher_id=p.id  " ;
        if(siteId!=3) {
            sql +=   " and  bk.site_id in (" + siteIdList + ")";
        }
        sql +=   ""+additionalCondition+"" +
                " and (bk.book_type not in ('print') or bk.book_type is null)  and bk.status='published' ";

        dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)


        if(publisherId==null) redisService.("publishedCount_"+siteId)=""+results[0][0]
        else redisService.("publishedCount_"+siteId+"_pub_"+publisherId)=""+results[0][0]


    }

    def quizDetails(ResourceDtl resourceDtl){
        String directions,section,sort
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
        if(objectiveMst != null) sort = "quizSort"
        else sort = "id"
        List answers = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort])

        // logic to remove expired questions
        for (Iterator<String> iter = answers.listIterator(); iter.hasNext();) {
            ObjectiveMst question = iter.next();
            if (question.expiryDate != null && question.expiryDate.compareTo(new Date()) < 0) {
                iter.remove()
            }
        }
        DirectionsMst directionsMst

        List jsonAnswers = answers.collect { quiz ->

            int noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
            if(quiz.directionId!=null){
                directionsMst = getDirectionsMst(quiz.directionId)
                directions = directionsMst.directions
            }
            else directions = null

            section = quiz.section


            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType          : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions       : directions, section: quiz.section,
                    answerDescription: quiz.answerDescription, answer: quiz.answer, subject: quiz.subject,
                    chapterId        : ""+resourceDtl.chapterId, quizId: quiz.quizId, marks: quiz.marks, negativeMarks: quiz.negativeMarks, explainLink: quiz.explainLink, startTime: quiz.startTime,
                    endTime: quiz.endTime,difficultyLevel:quiz.difficultylevel, isValidAnswerKey: quiz.isValidAnswerKey]
        }

        Gson gson = new Gson();
        String element = gson.toJson(jsonAnswers,new TypeToken<List>() {}.getType())
        redisService.("quiz_"+resourceDtl.id) = element
    }

    def getLatestFlashCards(){

        String sql = "select rd.id resId,resource_name title,chapter_id,cm.name description,privacy_level privacyLevel,sharing,rd.created_by from resource_dtl rd, " +
                "chapters_mst cm,books_mst bm where bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true') and bm.id=cm.book_id" +
                " and res_type='KeyValues' and rd.chapter_id is not null and cm.id=rd.chapter_id and (rd.sharing is null or privacy_level='public') union " +
                "select id resId,resource_name title,chapter_id,description,privacy_level privacyLevel,sharing,created_by from resource_dtl  where " +
                "  res_type='KeyValues' and chapter_id is null  and (sharing is null or privacy_level='public')" +
                " order by resId desc limit 20"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List flashCards = results.collect { flashcard ->
            return [resId               : flashcard.resId, title: flashcard.title, chapter_id: flashcard.chapter_id, description: flashcard.description,
                    privacyLevel: flashcard.privacyLevel, sharing: flashcard.sharing, createdBy: flashcard.sharing!=null?getUserMst(flashcard.created_by).name:"",
                    createdByUsername:flashcard.sharing!=null?flashcard.created_by:"",
                    profilePic: flashcard.sharing!=null?getUserMst(flashcard.created_by).profilepic:""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(flashCards,new TypeToken<List>() {}.getType())
        redisService.("latestFlashCards") = element
    }

    def getLatestResources(int pageNo=0,resType,siteId){
        int noOfItems = 20
        int startingIndex = noOfItems*pageNo
        String optionalString =""
        String optionalString1=""
        if(redisService."nextExamPublisherId"==null) getNextExamPublisherId()
        if(redisService."nextExamPublisherId"!=null) optionalString1 = " and bm.publisher_id not in (" +redisService."nextExamPublisherId"+") "
        if(!"all".equals(resType)) optionalString = " and res_type='"+resType+"'"
        String sql = "select rd.id resId,resource_name title,chapter_id,cm.name description,privacy_level privacyLevel,sharing,rd.created_by,rd.res_type,rd.res_link from resource_dtl rd, " +
                "chapters_mst cm,books_mst bm where bm.status='published' and (bm.price=0 or bm.price=null or cm.preview_chapter='true') and bm.id=cm.book_id "+optionalString1+
                optionalString+" and rd.chapter_id is not null and  rd.site_id = "+siteId+" and cm.id=rd.chapter_id and (rd.sharing is null or privacy_level='public') and rd.res_link not like ('%.%') and rd.site_id = "+siteId+" union " +
                "select id resId,resource_name title,chapter_id,description,privacy_level privacyLevel,sharing,created_by,res_type,res_link from resource_dtl  where " +
                "chapter_id is null  and  site_id = "+siteId+" and (sharing is null or privacy_level='public')" +optionalString+
                " order by resId desc limit "+startingIndex+","+noOfItems

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List flashCards = results.collect { flashcard ->

            return [resId : flashcard.resId, title: flashcard.title, chapter_id: flashcard.chapter_id, description: flashcard.description,
                    privacyLevel: flashcard.privacyLevel, sharing: flashcard.sharing, createdBy: flashcard.sharing!=null?getUserMst(flashcard.created_by).name:"",
                    createdByUsername:flashcard.sharing!=null?flashcard.created_by:"",
                    "canEdit"  : "false",
                    profilePic: flashcard.sharing!=null?getUserMst(flashcard.created_by).profilepic:"",resType:flashcard.res_type, resLink:flashcard.res_link]
        }
        Gson gson = new Gson();
        String element = gson.toJson(flashCards,new TypeToken<List>() {}.getType())
        redisService.("latestResources_"+resType.replaceAll("\\s", "")+"_List_"+pageNo+"_"+siteId) = element
    }

    def getUserResources(String resourceType,String username){
        // starting as the db read now. Next step is to move them cache with pagination.
        def results = null
        String optionalString =""
        List flashCards = null
        if(!"all".equals(resourceType)) optionalString = " and res_type='"+resourceType+"'"
        optionalString += "  "
        String sql = ""

        if(springSecurityService.currentUser!=null) {
            // for users having publishing desk access, do not show the items created for the book. So the
            if(!userManagementService.hasRole("ROLE_BOOK_CREATOR")){
                sql = "select rd.id resId,resource_name title,chapter_id,cm.name description,privacy_level privacyLevel,res_type,res_link,rd.created_by from resource_dtl rd, chapters_mst cm where rd.created_by='" + username + "'" +
                        optionalString+
                        "  and rd.chapter_id is not null and cm.id=rd.chapter_id union "
            }
            sql += "select id resId,resource_name title,chapter_id,description,privacy_level privacyLevel,res_type,res_link,created_by from resource_dtl  where created_by='" +username + "'" +
                    optionalString+"  and chapter_id is null" +
                    " and id not in (select res_id from folder_dtl fd,folder_mst fm where fm.username='"+username+"' and fd.folder_id=fm.id) "+
                    " order by resId desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            results = sql1.rows(sql)
            flashCards = results.collect { flashcard ->
                return [resId: flashcard.resId, title: flashcard.title, chapter_id: flashcard.chapter_id, description: flashcard.description,
                        privacyLevel: flashcard.privacyLevel,resType:flashcard.res_type,resLink:flashcard.res_link,
                        "canEdit"  : (username != null && username.equals(flashcard.created_by)) ? "true" : "false"]
            }


        }
        Gson gson = new Gson();
        String element = gson.toJson(flashCards,new TypeToken<List>() {}.getType())
        redisService.(username+"_userResources_"+resourceType.replaceAll("\\s", "")) = element
    }

    def getNextExamPublisherId(){
        Publishers publishers = Publishers.findByUrlname("nextexam")
        if(publishers!=null) redisService."nextExamPublisherId"=""+publishers.id

    }
    def getLastReadBooksForInstitute(username,batchId){
        def bookIds="";def queryappend="";
        String sql = "select max(date_created) max_date ,book_id \n" +
                "from wslog.books_view_dtl \n" +
                "where username='"+username+"' and institute_id is not null \n" +
                "group by book_id\n" +
                "order by max_date desc limit 8"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List bookslist;
        def instituteId
        results.collect { book ->
            bookIds += book.book_id+",";
        }
        if(bookIds!=null && bookIds!="" ) {
            queryappend = bookIds.substring(0, bookIds.length() - 1);
            String sqls = "select bm.id,bm.title,bm.cover_image,bp.expiry_date,bm.publisher_id,bm.book_type,bm.external_link \n" +
                    "from wsuser.books_mst bm, wsuser.books_permission bp \n" +
                    "where bp.book_id=bm.id and bp.book_id IN ("+queryappend+") and bp.po_type='ADDEDFROMINSTITUTE' and bp.batch_id="+batchId+" and bp.username='"+username+"'  order by bp.date_created desc";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql11 = new Sql(dataSource1)
            def results1 = sql11.rows(sqls);
            bookslist = results1.collect { books ->
                instituteId =InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).id;
                BooksTagDtl booksTagDtl = getBooksTagDtl(books.id)
                return [bookId: books.id, bookTitle: books.title, coverImage: books.cover_image != null ? books.cover_image : "", level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",bookType:(books.book_type!=null)?books.book_type:"",
                        publisher: (books.publisher_id!=null && books.publisher_id!= "")? getPublisher(new Long(books.publisher_id)).name:'',
                        grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",batchId:batchId,instituteId:instituteId,
                        expiryDate:books.expiry_date!=null?(""+books.expiry_date).replace(':','~'):"",externalLink:books.external_link]
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(bookslist,new TypeToken<List>() {}.getType())
        redisService.("lastReadBooksIns_"+batchId+"_"+ username) = element
    }

    def nextExamBooks(){
        if(redisService.("nextExamPublisherId")==null) getNextExamPublisherId()
        String sql = "SELECT monthname(date_published) publishedMonth ,year(date_published) publishedYear, id bookId from books_mst where date_published is not null " +
                " and status='published' and publisher_id="+redisService."nextExamPublisherId"+" and DATE(date_published) < DATE(DATE_ADD(SYSDATE(), INTERVAL '-10' DAY)) " +
                " order by date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        List nextExamBooks = results.collect{ book->

            return [month:book.publishedMonth,year:book.publishedYear,bookId:book.bookId]

        }

        Gson gson = new Gson();
        String element = gson.toJson(nextExamBooks,new TypeToken<List>() {}.getType())
        redisService.("nextexamMonthlyBooksList") = element
    }

    def getCommaSeperatedChaptersList(def bookId ){
        String sql = "SELECT GROUP_CONCAT(id)\n" +
                " FROM  chapters_mst  WHERE  book_id ="+bookId
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        println("the sql is "+sql)
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        String chapterIds
        if(results!=null||results.size()>0) {
            println("entered it man entered it "+results[0][0])
            chapterIds = results[0][0]
        }

        redisService.("commaSeperatedChapterIds_"+bookId)=chapterIds

    }

    def getDefaultTestGeneratorBookIds(siteId){
        KeyValueMst keyValueMst =  KeyValueMst.findByKeyNameAndSiteId("testGeneratorBooks",siteId)

        redisService.("testGeneratorBookIds_"+siteId) = keyValueMst.keyValue

    }

    def getBookDetailsForBookIds(String bookIds,Integer siteId){
        //adding the site id condition here. This is a quick fix. Correct fix should get the site information and then decide.
        def sql = "select bm.id,bm.title," +
                " bm.cover_image " +
                " from books_mst bm" +
                " where bm.id in ("+bookIds+")" +
                " order by title desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->

            return [id: book.id,title:(book.title),
                    'coverImage':book.cover_image!=null?book.cover_image:""]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("getBookDetailsForBookIds"+"_"+siteId) = element
    }

    def getSubjectsList(){
        def sql = "SELECT distinct(name),sort_order FROM wsshop.subject_mst order by sort_order desc,name"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List subjectMstList = results.collect { subject ->
            return [name: subject.name]
        }


        Gson gson = new Gson();
        String element = gson.toJson(subjectMstList,new TypeToken<List>() {}.getType())
        redisService.("subjectsList") = element

    }
    def getSecurityKeys(siteId){
        String sql = "select   security_key from site_mst where id="+siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        redisService.("securityKeys_"+siteId) = results[0].security_key
    }

    def getInstitutesForUser(){
        def sql = "SELECT im.id, im.name, bud.instructor,cbd.id batchId FROM institute_mst im, course_batches_dtl cbd, batch_user_dtl bud " +
                " where bud.username='"+springSecurityService.currentUser.username+"' and cbd.id=bud.batch_id "+
                " and cbd.name='Default' and im.id=cbd.conducted_by and cbd.status='active' order by name"

        String instructor=null
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String instituteIds=""
        List instituteList = results.collect { institute ->
            instituteIds += institute.id+","
            if("true".equals(""+institute.instructor))
                instructor = "true"

            return [instituteId: institute.id,instituteName:institute.name,instructor:institute.instructor,batchId:institute.batchId]
        }
        //remove comma at the end
        if(instituteIds.length()>0) instituteIds = instituteIds.substring(0,instituteIds.length()-1)
        String[] instituteIdList = instituteIds ? instituteIds.split(",") : []
        if(instituteIdList.length>0){
            for(int i=0;i<instituteIdList.length;i++){
                InstituteMst instituteMst = dataProviderService.getInstituteMst(new Integer(instituteIdList[i]))
                if(instituteMst!=null&&instituteMst.associatedLibraries!=null){
                    String[] associatedLibraries = instituteMst.associatedLibraries.split(",")
                    //for each associated library, need to add one record to the instituteDetails
                    for(int j=0;j<associatedLibraries.length;j++){
                        InstituteMst associatedInstituteMst = dataProviderService.getInstituteMst(new Integer(associatedLibraries[j]))
                        if(associatedInstituteMst!=null){
                            //get default batch for the associated library
                            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndNameAndStatus(associatedInstituteMst.id,"Default","active")
                            instituteList.add([instituteId: associatedInstituteMst.id,instituteName:associatedInstituteMst.name,instructor:instructor,batchId:courseBatchesDtl.id])
                        }
                    }

                }
            }
        }

        Gson gson = new Gson();
        String element = gson.toJson(instituteList,new TypeToken<List>() {}.getType())
        redisService.("institutesList_"+springSecurityService.currentUser.username) = element
    }

    def getBatchesForUser(String username,Integer instituteId){
        String sql = "select cbd.name,cbd.id,cbd.conducted_by,bud.instructor from course_batches_dtl cbd,batch_user_dtl bud where bud.batch_id=cbd.id and bud.username='"+username+"'" +
                "and cbd.conducted_by="+instituteId+" and cbd.status='active'  and cbd.name not in ('Default') order by cbd.name "

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List batchesList = results.collect { batch ->
            return [instituteId: batch.conducted_by,batchName:batch.name,instructor:batch.instructor,batchId:batch.id]
        }

        Gson gson = new Gson();
        String element = gson.toJson(batchesList,new TypeToken<List>() {}.getType())
        redisService.("batchesList_"+springSecurityService.currentUser.username) = element
    }

    def getCourseBatchesDtl(Integer batchId){
        CourseBatchesDtl courseBatchesDtl  = redisService.memoizeDomainObject(CourseBatchesDtl, "cbd_"+batchId) {
            return CourseBatchesDtl.findById(batchId);
        }

        return courseBatchesDtl
    }

    def getDefaultCourseBatchesDtl(Long instituteId){
        CourseBatchesDtl courseBatchesDtl  = redisService.memoizeDomainObject(CourseBatchesDtl, "defaultCBD_"+instituteId) {
            return CourseBatchesDtl.findByConductedByAndName(instituteId,"Default");
        }

        return courseBatchesDtl
    }


    def getGroupMembersCount(Long groupId){
        List selectedAuthors = GroupsMembersDtl.findAllByGroupId(new Long(groupId))
        redisService.("getGroupMembersCount_"+groupId) = selectedAuthors.size()+""
    }
    def getCommentsBlockedUsers(resId){
        String sql = "select   name,username from user where comments_blocked_res_id="+resId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("blockedUsers_" +resId) = element
    }



    def getPollCorrectAnswer(Integer pollId){
        List pollsDetailsList = PollsDetails.findAllByPollsId(pollId, [sort: "id", order: "asc"])
        def correctPollOption=null
        boolean poll = true
        for(int i=0;i<pollsDetailsList.size();i++){
            redisService.("PollDetailsId_"+pollId+"_"+(i+1)) = ""+pollsDetailsList[i].id
            if(pollsDetailsList[i].correctOption) {
                poll = false
                correctPollOption = pollsDetailsList[i].id
            }

        }


        if(poll) redisService.("CorrectAnswerPollDetails_"+pollId) = "poll"

        else {
            redisService.("CorrectAnswerPollDetails_"+pollId) = "quiz"
            redisService.("CorrectAnswerPollDetailsId_"+pollId) = ""+correctPollOption
        }
    }

    def getPollRanks(Integer pollId){
        String sql = "select  pr.rank,pr.user_name userName,pua.duration  from poll_ranks pr,polls_user_answers pua where pr.poll_id=pua.polls_id and pr.user_name = pua.user_name and poll_id="+pollId+" order by  rank asc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);
        List rankUsers = results.collect { pollRank ->
            User  user = getUserMst(pollRank.userName)
            return [rank:pollRank.rank, name:user.name, mobile:user.mobile,timetaken: pollRank.duration]
        }

        Gson gson = new Gson();
        String element = gson.toJson(rankUsers,new TypeToken<List>() {}.getType())
        redisService.("pollRank_"+pollId) = element
    }

    def getAllPollingByResId(Integer resId){
        List<Polls> polls = Polls.findAllByResId(resId)
        List pollsList = polls.collect { poll ->
            List<PollsDetails> details = PollsDetails.findAllByPollsId(poll.id)
            return [poll:poll, details:details]
        }
        Gson gson = new Gson();
        String element = gson.toJson(pollsList,new TypeToken<List>() {}.getType())
        redisService.("allPollsForResId_"+resId) = element
    }


    def getPollResults(def pollId){

        String sql = "select  poll_id pollId,option1Count,option2Count,option3Count,option4Count,total_count from polls_results where poll_id="+pollId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("pollResults_" +pollId) = element

    }

    def getConsolidatedPollRanks(Integer resId){
        String sql = "select user_name username,count(correct_answer) correctAnswers,sum(pua.duration) timetaken from polls_user_answers pua,polls p " +
                "where p.res_id="+resId+" and p.status in ('resultsComputed','published') and pua.polls_id=p.id and correct_answer='true' " +
                "group by user_name order by correctAnswers desc,timetaken asc limit 10"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        int i=0;
        List rankUsers = results.collect { pollRank ->
            i++;
            User  user = getUserMst(pollRank.userName)
            return [rank:i, name:user.name, mobile:user.mobile,correctAnswers: pollRank.correctAnswers]
        }

        Gson gson = new Gson();
        String element = gson.toJson(rankUsers,new TypeToken<List>() {}.getType())
        redisService.("consolidatedPollRank_"+resId) = element
    }

    def getGoogleUniversalAnalytics(siteId){
        SiteMst siteMst = getSiteMst(new Long(""+siteId))
        if(siteMst.googleUniversalAnalyticsKey==null) siteMst = getSiteMst(new Long(1))
        redisService.("googleUAId_"+siteId) = siteMst.googleUniversalAnalyticsKey
    }

    def getValidityExtensionDtls(Integer bookId){
        String sql = "select validity_months validityMonths,price,id from validity_extension_mst where book_id=" +bookId

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("validityExtensionDtls_"+bookId) = element
    }

    def getBanners(String siteId){
        def sql = "select bm.id,bm.image_name,bm.image_path,COALESCE(bm.book_id,''),bm.image_path_mobile,COALESCE(bms.title,''),bm.action" +
                "  from  banners_mst bm LEFT JOIN  books_mst bms  ON  bm.book_id=bms.id where" +
                "  bm.publisher_id is null and  bm.institute_id is  null and bm.site_id='" + siteId + "'  "
        sql += " order by bm.id Desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List banners = results.collect { banner ->
            return [id: banner[0], imageName: banner[1], imagePath: banner[2]!=null?banner[2]:"", bookId: banner[3],imagePathMobile:banner[4]!=null?banner[4]:"",bookTitle:banner[5],action:banner[6]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(banners,new TypeToken<List>() {}.getType())
        redisService.("bannerList_"+siteId) = element
    }

    def getAllPrivatelabelsList() {
        try {
            def sql = "SELECT sd.id, sd.site_id, sd.theme_color, sm.client_name FROM site_dtl sd, site_mst sm where sd.site_id = sm.id order by id desc"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            List websitesList = results.collect { pl ->
                return [id: pl[0], siteId: pl[1], themeColor: pl[2], clientName: pl[3]]
            }
            return websitesList
        } catch (Exception e) {
            println(e)
        }
    }


    def getPrivateLabelDetails(Long siteId){
        List siteDetails = SiteDtl.findAllBySiteId(siteId)
        if(siteDetails.size()>0) {
            List sites = siteDetails.collect { site ->
                return [
                        id: site.id,
                        siteId: site.siteId,
                        themeColor: site.themeColor,
                        favicon: site.favicon != null ? site.favicon : null,
                        logo: site.logo != null ? site.logo : null,
                        logoIcon: site.logoIcon ? site.logoIcon : null,
                        bannerImage: site.bannerImage != null ? site.bannerImage : null,
                        mobileNumber: site.mobileNumber != null ? site.mobileNumber : null,
                        emailAddress: site.emailAddress != null ? site.emailAddress : null,
                        facebookLink: site.facebookLink != null ? site.facebookLink : null,
                        twitterLink: site.twitterLink != null ? site.twitterLink : null,
                        instagramLink: site.instagramLink != null ? site.instagramLink : null,
                        linkedinLink: site.linkedinLink != null ? site.linkedinLink : null,
                        youtubeLink: site.youtubeLink != null ? site.youtubeLink : null,
                        privacyPolicy: site.privacyPolicy != null ? site.privacyPolicy : null,
                        termsCondition: site.termsCondition != null ? site.termsCondition : null,
                        playStore: site.playStore != null ? site.playStore : null,
                        appStore: site.appStore != null ? site.appStore : null
                ]
            }
            Gson gson = new Gson();
            String element = gson.toJson(sites,new TypeToken<List>() {}.getType())
            redisService.("privatelabelDtl_" + siteId) = element
        } else redisService.("privatelabelDtl_" + siteId) = []
    }

    def getBannersForPublishers(String siteId,String publisherId){
        def sql = "select bm.id,bm.image_name,bm.image_path,COALESCE(bm.book_id,''),bm.image_path_mobile,bm.action" +
                "  from  banners_mst bm  where" +
                "  bm.publisher_id="+publisherId+" and  bm.institute_id is  null and bm.site_id=" + siteId + "  "
        sql += " order by bm.id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String bookTitle
        BooksMst booksMst
        List banners = results.collect { banner ->
            bookTitle=null
            if(!"".equals(banner[3])) {
                booksMst = dataProviderService.getBooksMst(banner[3])
                bookTitle = booksMst.title
            }
            return [id: banner[0], imageName: banner[1], imagePath: banner[2]!=null?banner[2]:"", bookId: banner[3],imagePathMobile:banner[4]!=null?banner[4]:"",bookTitle:bookTitle,action:banner[5]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(banners,new TypeToken<List>() {}.getType())
        redisService.("publisherBannerList_"+siteId+"_"+publisherId) = element
    }
    def getBannersForInstitute(Long instituteId){
        def sql = "select bm.id,bm.image_name,bm.image_path,COALESCE(bm.book_id,''),bm.image_path_mobile,COALESCE(bms.title,''),bm.action" +
                "  from  banners_mst bm LEFT JOIN  books_mst bms  ON  bm.book_id=bms.id where" +
                "  bm.institute_id='" + instituteId + "'  "
        sql += " order by bm.id Desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List banners = results.collect { banner ->
            return [id: banner[0], imageName: banner[1], imagePath: banner[2]!=null?banner[2]:"", bookId: banner[3],imagePathMobile:banner[4]!=null?banner[4]:"",bookTitle:banner[5],action:banner[6]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(banners,new TypeToken<List>() {}.getType())
        redisService.("bannerListInstitute_"+instituteId) = element
    }

    def getInstituteGalleryImages(Long instituteId){
        def sql = "select igd.id,igd.institute_id,igd.image_name" +
                "  from institute_gallery_dtl igd  where" +
                "  igd.institute_id='" + instituteId + "'  "
        sql += " order by igd.id Desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List banners = results.collect { gallery ->
            return [id: gallery[0], institute_id: gallery[1], imageName: gallery[2]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(banners,new TypeToken<List>() {}.getType())
        redisService.("instituteGalleryImages_"+instituteId) = element
    }

    def totalBookMcqCount(bookId){
        redisService.("totalBookMcqCount_"+bookId)
        /** String regex = "[0-9]+";
         if (("" + bookId).matches(regex)) {
         String bookIds = bookId
         BooksMst booksMst = BooksMst.findById(new Integer(bookIds))
         if(booksMst.packageBookIds!=null &&  !"".equals(booksMst.packageBookIds)) bookIds= booksMst.packageBookIds
         String sql="select count(*) as mcqcount" +
         " from resource_dtl rd, chapters_mst cm,objective_mst ob" +
         " where rd.chapter_id=cm.id and  ob.quiz_id=rd.res_link and rd.sharing is null and rd.id>0 and cm.book_id in (" + bookIds +") "
         " and rd.res_type='Multiple Choice Questions';"
         def dataSource = grailsApplication.mainContext.getBean('dataSource')
         def sql1 = new SafeSql(dataSource)
         def results = sql1.rows(sql);
         redisService.("totalBookMcqCount_"+bookId) =""+results[0][0]
         }*/
    }

    def totalChapterMcqCount(chapterId){
        String regex = "[0-9]+";
        if (("" + chapterId).matches(regex)) {
            String sql="select count(*) as mcqcount" +
                    " from resource_dtl rd, chapters_mst cm,objective_mst ob" +
                    " where rd.chapter_id=cm.id and  ob.quiz_id=rd.res_link and rd.sharing is null and rd.id>0 and cm.id in (" + chapterId +") "
            " and rd.res_type='Multiple Choice Questions';"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);
            redisService.("totalChapterMcqCount_"+chapterId) =""+results[0][0]
        }
    }

    def  getDailyTestsDtl(Long id){
        DailyTestsDtl dailyTestsDtl  = redisService.memoizeDomainObject(DailyTestsDtl, "dailyTestsDtl_"+id) {
            return DailyTestsDtl.findById(id)
        }

        return dailyTestsDtl
    }
    def  getDailyTestsMst(Long id){
        DailyTestsMst dailyTestsMst  = redisService.memoizeDomainObject(DailyTestsMst, "dailyTestsMst_"+id) {
            return DailyTestsMst.findById(id)
        }

        return dailyTestsMst
    }

    def getInstituteChapterDownloadCount(Long instituteId){
        List chaptersDownloadDtl = ChaptersDownloadDtl.findAllByInstituteId(new Long(instituteId))
        redisService.("getInstituteChapterDownloadCount_"+instituteId) = chaptersDownloadDtl.size()+""
    }


    def packageBooksByMainBookId(Long bookId){
        BooksMst book = getBooksMst(bookId)
        List packageBookList
        if(book!=null && book.packageBookIds!=null &&  book.packageBookIds!="") {
            String sql = "select bm.id,bm.title," +
                    "                bm.cover_image,bm.show_in_library" +
                    "                  from books_mst bm" +
                    "                 where bm.id IN (" + book.packageBookIds + ")"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            packageBookList = results.collect { books ->
                BooksTagDtl booksTagDtl = getBooksTagDtl(book.id)
                return [id   : books.id, title: books.title, coverImage: books.cover_image ? books.cover_image : "", level: (booksTagDtl != null) ? booksTagDtl.level : "", syllabus: (booksTagDtl != null) ? booksTagDtl.syllabus : "",
                        grade: (booksTagDtl != null) ? booksTagDtl.grade : "", subject: (booksTagDtl != null) ? booksTagDtl.subject : "", showInLibrary: books.show_in_library != null ? books.show_in_library : ""]
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(packageBookList,new TypeToken<List>() {}.getType())
        redisService.("packageBooksByMainBookId_"+bookId) = element

    }

    def usersCartBooksDetailsByUsername(username,siteId){
        Map<String,List> usersMyLibraryBooks = new HashMap<String,List>()
        def sql = "select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type," +
                "bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id" +
                "  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and " +
                "   scad.username='" + username + "' and scad.site_id='" + siteId + "' order by scad.id Desc "
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results1 = sql1.rows(sql)
        BookPriceDtl bookPriceDtl
        if(results1!=null) {
            List bList = new ArrayList()
            results1.collect { comp ->
                bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+comp.bookId),"subscription".equals(comp.book_type)?"eBook":comp.book_type)
                if(bookPriceDtl!=null) {
                    if ("subscription".equals(comp.book_type)) {
                        bList.add([bookId      : comp.bookId, bookImg: comp.cover_image != null ? comp.cover_image : "", bookTitle: comp.title, publisherName: comp.publisherName, bookPrice: wsshopService.getSubscriptionCost(comp.subscription_id, comp.subs_duration),
                                   subsDuration: comp.subs_duration, subsStartingBookId: comp.subs_starting_book_id, subscriptionId: comp.subscription_id, bookType: comp.book_type != null ? comp.book_type : "",baseBookType:comp.baseBookType,publisherId: comp.publisher_id])
                    } else
                        bList.add([bookId: comp.bookId, bookImg: comp.cover_image != null ? comp.cover_image : "", bookTitle: comp.title, publisherName: comp.publisherName, bookPrice: bookPriceDtl.sellPrice, bookType: comp.book_type != null ? comp.book_type : "",
                                   listPrice: bookPriceDtl.listPrice,baseBookType:comp.baseBookType,publisherId: comp.publisher_id])

                }

            }
            usersMyLibraryBooks.put("" + username, bList)
        }

        Gson gson = new Gson();
        String element = gson.toJson(usersMyLibraryBooks,new TypeToken<HashMap<String,List>>() {}.getType())
        if(results1 != null) redisService.("usersCartBooksDetails_"+username) = element
        redisService.("usersCartBooksDetailsCount_"+username) = results1.size()+""
    }

    String toSingleQuotes(String source){
        String[] parts = source.split(",")
        return Stream.of(parts).collect(Collectors.joining("','", "'", "'"));
    }


    def isInstituteAdmin(username,Long instituteId){
        String isadmin="false"
        InstituteUserDtl instituteUserDtl = InstituteUserDtl.findByInstituteIdAndUsername(new Long(instituteId),username)
        if(instituteUserDtl!=null)isadmin="true"
        redisService.("isInstituteAdmin_"+username+"_"+instituteId) = isadmin
    }

    def getUserGrades(username){
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        UserGradesDtl userGradesDtl = UserGradesDtl.findByUserName(username)
        if(userGradesDtl!=null) {
            String element = gson.toJson(userGradesDtl.contents)
            redisService.("getUserGrades_" + username) = element
        }
    }

    def getInstituteMembersCount(Long instituteId) {
        List membersCount
        def sql = "SELECT " +
                "   (SELECT " +
                "            COUNT(id)" +
                "        FROM" +
                "            wsuser.course_batches_dtl cbd" +
                "        WHERE" +
                "            cbd.conducted_by = "+instituteId+" AND name != 'Default') classesCount," +
                "    (SELECT " +
                "            COUNT(bud.id)" +
                "        FROM" +
                "            wsuser.course_batches_dtl cbd ,wsuser.batch_user_dtl bud" +
                "        WHERE cbd.id=bud.batch_Id AND bud.instructor is null AND " +
                "            cbd.conducted_by = "+instituteId+") studentCount," +
                "            (SELECT " +
                "            COUNT(bud.id)" +
                "        FROM" +
                "            wsuser.course_batches_dtl cbd ,wsuser.batch_user_dtl bud" +
                "        WHERE cbd.id=bud.batch_Id AND bud.instructor is not null AND " +
                "            cbd.conducted_by = "+instituteId+") instructorCount " +
                " FROM " +
                "    wsuser.course_batches_dtl cbd where  cbd.conducted_by = 38 limit 1"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results1 = sql1.rows(sql)
        if (results1 != null) {
            membersCount = results1.collect { institutes ->
                return [classesCount: institutes.classesCount, studentCount: institutes.studentCount, instructorCount: institutes.instructorCount]
            }
        }
        redisService.("getInstituteMembersCount_" + instituteId) = membersCount
    }

    def getSyllabusList(level){
        String siteIdList
        if(redisService.("siteIdList_1")==null) {
            getSiteIdList(new Integer(1))
        }

        siteIdList = redisService.("siteIdList_1")
        String sql = "select distinct(btd.syllabus) " +
                " from books_mst bk, books_tag_dtl btd " +
                " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and btd.level='"+level+"'"+
                " order by btd.syllabus asc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("levelSyllabus_"+level.replaceAll("\\s+", "")) = element

    }
    def getGradesList(level,syllabus){
        String siteIdList
        if(redisService.("siteIdList_1")==null) {
            getSiteIdList(new Integer(1))
        }

        siteIdList = redisService.("siteIdList_1")
        String sql = "select distinct(btd.grade) " +
                " from books_mst bk, books_tag_dtl btd " +
                " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and btd.level='"+level+"' and btd.syllabus='"+syllabus+"' "+
                " order by grade*1,grade asc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("levelSyllabusGrade_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", "")) = element

    }
    def getSubjectsList(level,syllabus,grade){
        String siteIdList
        if(redisService.("siteIdList_1")==null) {
            getSiteIdList(new Integer(1))
        }

        siteIdList = redisService.("siteIdList_1")
        String sql = "select distinct(btd.subject) " +
                " from books_mst bk, books_tag_dtl btd " +
                " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') and" +
                " btd.level='"+level+"' and btd.syllabus='"+syllabus+"' and grade='"+grade+"' "
        " order by grade*1,grade asc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("levelSyllabusGradeSubject_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", "")+"_"+grade.replaceAll("\\s+", "")) = element

    }

    def getDefaultCategoryStructure(siteId,siteIdList,subject) {
        String sql = "select level,syllabus,grade from books_tag_dtl btd, books_mst bm where bm.site_id in ("+siteIdList+") and bm.status='published' and btd.book_id=bm.id  \n" +
                "group by btd.level,btd.syllabus,btd.grade order by level,syllabus,grade"
        if(subject!=null)
            sql = "select level,syllabus,grade,subject from books_tag_dtl btd, books_mst bm where bm.site_id in ("+siteIdList+") and bm.status='published' and btd.book_id=bm.id  \n" +
                    "group by btd.level,btd.syllabus,btd.grade,btd.subject order by level,syllabus,grade,subject"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        if(subject!=null) {
            redisService.("catalogStructure_subject_" + siteId) = element
        }
        else {
            redisService.("catalogStructure_" + siteId) = element
        }
    }

    def getTeachersNomineeDetails(siteId){
        String sql=" SELECT  COUNT(nvd.id) votes, tnd.id ,tnd.user_name,tnd.user_city,tnd.user_state,tnd.teacher_name,tnd.teacher_image,tnd.school_name " +
                " FROM teacher_nominee_dtl tnd left join wscomm.nominee_voting_dtl nvd ON  tnd.id = nvd.nominee_id and tnd.status is not null " +
                " group by tnd.id ORDER BY votes DESC,tnd.date_created LIMIT 10";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        List teachersList = results.collect{it ->
            return [id:it.id, userName:it.user_name,userCity:it.user_city,userState:it.user_state,teacherName:it.teacher_name,teacherImage:it.teacher_image,voteCount:it.votes,schoolName:it.school_name,voteCount:it.votes]
        }
        Gson gson = new Gson();
        String element = gson.toJson(teachersList,new TypeToken<List>() {}.getType())
        redisService.("teachersNomineeForSite_"+siteId) = element
    }


    def getAllModeratedTeachersNomineeDetails(siteId){
        List teachers = TeacherNomineeDtl.findAllBySiteIdAndStatusIsNotNull(siteId, [sort: "id", order: "desc"])
        List teachersList = teachers.collect{it ->
            if( redisService.("getNomineeIdVoteCount_"+it.id) ==null){
                dataProviderService.getNomineeIdVoteCount(new Long(it.id))
            }
            return [id:it.id,teacherName:it.teacherName,teacherImage:it.teacherImage,teacherSchool:it.schoolName,voteCount:redisService.("getNomineeIdVoteCount_"+it.id)]
        }
        Gson gson = new Gson();
        String element = gson.toJson(teachersList,new TypeToken<List>() {}.getType())
        redisService.("allModeratedTeachersNomineeDetails_"+siteId) = element
    }

    def getTeacherNomineeDtl(Long nomineeId){
        TeacherNomineeDtl teacherNomineeDtl  = redisService.memoizeDomainObject(TeacherNomineeDtl, "teacherNomineeDtl_"+nomineeId) {
            return TeacherNomineeDtl.findById(nomineeId)
        }
        return teacherNomineeDtl
    }

    def getNomineeIdVoteCount(Long nomineeId){
        List nomineeCount=  NomineeVotingDtl.findAllByNomineeId(nomineeId)
        redisService.("getNomineeIdVoteCount_"+nomineeId) = nomineeCount.size()+""
    }

    def getPackageBooksDetail(bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(bookId)
        String sql = " select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher, null name,COALESCE(bk.isbn,' ')" +
                " from books_mst bk where  id in ("+booksMst.packageBookIds+")"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]),coverImage:""+book[2],subjectyear:book[3],'listPrice':book[4],'rating':book[5],
                    'offerPrice':book[6],'status':book[7], 'publisher':book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",'creator':book[9],isbn:book[10]]
        }
        Gson gson = new Gson();
        String element = gson.toJson(books,new TypeToken<List>() {}.getType())
        redisService.("packageBooksDetail_"+bookId) = element
    }

    def getRelatedVideosFromDB(String chapterId){
        List relatedVideos = RelatedVideosNew.findAllByChapterId(new Integer(chapterId))

        List videosList = relatedVideos.collect{relatedVideo ->
            return [id:relatedVideo.id,videoId: relatedVideo.videoId, videoTitle: relatedVideo.videoTitle, chapterId: relatedVideo.chapterId, username: relatedVideo.username]
        }

        //some related videos are present in ResourceDtl also. Let us get them add to the list. First get list of ResourceDtl for chapter and resType 'Reference Videos'
        List resourceVideosRes = ResourceDtl.findAllByChapterIdAndResType(new Integer(chapterId),"Reference Videos")
        //add these to videosList
        resourceVideosRes.each{resourceVideo ->
            videosList.add([id:resourceVideo.id,videoId: resourceVideo.resLink, videoTitle: resourceVideo.resourceName, chapterId: resourceVideo.chapterId, username: ""])
        }

        Gson gson = new Gson();
        String element = gson.toJson(videosList,new TypeToken<List>() {}.getType())
        redisService.("suggestedVideos_"+chapterId) = element

    }

    def getSiteDtl(Long siteId){
        SiteDtl site  = redisService.memoizeDomainObject(SiteDtl, "siteDtl_"+siteId) {
            return SiteDtl.findBySiteId(siteId)
        }
        return site
    }

    def checkAndSetPreviewChapter(String bookId){
        List chapters = ChaptersMst.findAllByBookId(new Integer(bookId), [sort: "sortOrder", order: "asc"])
        boolean hasPreviewChapter
        chapters.each { chaptersMst ->
            if("true".equals(chaptersMst.previewChapter)) hasPreviewChapter=true
        }
        if(!hasPreviewChapter&&chapters.size()>1){
            ChaptersMst chaptersMst = chapters[0]
            chaptersMst.previewChapter="true"
            chaptersMst.save(failOnError: true, flush: true)
            getChaptersList(chaptersMst.bookId,"id")
        }
    }

}





