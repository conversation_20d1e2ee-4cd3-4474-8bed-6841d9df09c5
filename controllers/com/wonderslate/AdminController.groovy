package com.wonderslate

import com.ccavenue.security.AesCryptUtil
import com.google.gson.Gson
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.admin.AdminService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.CurrentAffairsMst
import com.wonderslate.data.EmailMst
import com.wonderslate.data.Jobs
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.PromptService
import com.wonderslate.data.Prompts
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.groups.GroupsService
import com.wonderslate.information.InformationMst
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.log.NotificationDtl
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.LogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import com.wonderslate.shop.BookProductTypes
import com.wonderslate.shop.DiscountMst
import com.wonderslate.shop.DiscountPriceDtl
import com.wonderslate.shop.ShoppingCartOrdersMst
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.UserRole
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import java.sql.SQLException
import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import org.apache.poi.xssf.usermodel.XSSFCell
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import pl.touk.excel.export.WebXlsxExporter


class AdminController {
    def redisService
    DataProviderService dataProviderService
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    AdminService adminService
    UtilService utilService
    DataNotificationService dataNotificationService
    GroupsService groupsService
    WsLibraryCacheService wsLibraryCacheService
    AsyncLogsService asyncLogsService
    BookPriceService bookPriceService
    PromptService promptService

    @Secured(['ROLE_JOBSADMIN'])
    def insertjob() {

    }

    @Transactional
    def save() {
        def job = new Jobs(params)
        job.save();
        redirect(action: "list")
    }

    @Transactional
    def list() {
        def jobs = Jobs.list()
        [jobs: jobs]
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def managePublishers() {
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        List publishers
        if(session["userdetails"].publisherId==null) {
            if (redisService.("publishers_" + getSiteId(request)) == null) {
                dataProviderService.getPublishers(getSiteId(request))
            }
            publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
        }else{
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        }
        [publishers: publishers,sitesList:sitesList,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    @Transactional
    def updateUserRole() {
        List userRoles;
        Integer siteId = getSiteId(request)
        User user = User.findByEmailAndSiteId(params.user.trim().toLowerCase(), siteId)
        if (user == null) user = User.findByMobileAndSiteId(params.user.trim().toLowerCase(), siteId)
        String status = "Content upload access granted to the user";
        if (user != null) {

            if ("admin".equals(params.permissionType)) {
                // for publisher content uploaders
                userRoles = Role.findAllByAuthorityInList(["ROLE_BOOK_CREATOR", "ROLE_PUBLISHER", "ROLE_WS_CONTENT_ADMIN"])

            } else {
                userRoles = Role.findAllByAuthorityInList(["ROLE_BOOK_CREATOR", "ROLE_PUBLISHER"])
            }

            userRoles.each { role ->

                UserRole.create(user, role, true)
            }
            user.publisherId = new Integer(params.publisherId)
            user.save(failOnError: true, flush: true)
        } else {
            status = "error";
        }

        def json = ["status": status]

        render json as JSON

    }

    @Transactional
    def updateSalesViewRole() {
        List userRoles;
        Integer siteId = getSiteId(request)
        User user = User.findByEmailAndSiteId(params.user.trim().toLowerCase(), siteId)
        if (user == null) user = User.findByMobileAndSiteId(params.user.trim().toLowerCase(), siteId)
        String status = "Content upload access granted to the user";
        if (user != null) {
            if (params.publisherId != null) {
                // for publisher content uploaders

                userRoles = Role.findAllByAuthorityInList(["ROLE_FINANCE"])
                user.publisherId = new Integer(params.publisherId)
                user.save(failOnError: true, flush: true)

            }
            userRoles.each { role ->

                UserRole.create(user, role, true)
            }
        } else {
            status = "error";
        }

        def json = ["status": status]

        render json as JSON

    }

    @Transactional
    def removeSalesViewRole() {
        User user = User.findById(new Long(params.userId))
        List userRoles = Role.findAllByAuthorityInList(["ROLE_FINANCE"])
        userRoles.each { role ->
            UserRole.remove(user, role, true)
        }
        if (user.publisherId != null) {
            String sql = "delete BooksPermission where username='" + user.username + "' and bookId in (select id from BooksMst where publisherId=" + user.publisherId + ")"
            BooksPermission.executeUpdate(sql)
            dataProviderService.getBooksListForUser(user.username)
            user.publisherId = null
            user.save(failOnError: true, flush: true)
        }
        def json = ["status": "removed"]

        render json as JSON

    }

    @Transactional
    def removePublisherRole() {
        User user = User.findById(new Long(params.userId))
        List userRoles = Role.findAllByAuthorityInList(["ROLE_BOOK_CREATOR", "ROLE_PUBLISHER", "ROLE_WS_CONTENT_ADMIN"])
        userRoles.each { role ->
            UserRole.remove(user, role, true)
        }
        if (user.publisherId != null) {
            String sql = "delete BooksPermission where username='" + user.username + "' and bookId in (select id from BooksMst where publisherId=" + user.publisherId + ")"
            BooksPermission.executeUpdate(sql)
            dataProviderService.getBooksListForUser(user.username)
            user.publisherId = null
            user.save(failOnError: true, flush: true)
        }
        def json = ["status": "removed"]

        render json as JSON

    }


    @Transactional
    def publisherUploaders() {
        List publisherUploaders;
        String additionalCondition=""
        if("admin".equals(params.permissionType)) additionalCondition = " and r.authority='ROLE_WS_CONTENT_ADMIN' "
        else additionalCondition = " and r.authority='ROLE_BOOK_CREATOR' "

            String sql = "select u.name,u.email,u.id from wsuser.user u,wsuser.user_role ur,wsuser.role r" +
                    " where  u.publisher_id="+params.publisherId+" and ur.role_id=r.id and ur.user_id=u.id and u.site_id=" + getSiteId(request)+additionalCondition
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);

            publisherUploaders = results.collect { user ->
                return [name: user[0], email: user[1], id: user[2]]
            }


        def json = [status: publisherUploaders ? "OK" : "not found", users: publisherUploaders]
        render json as JSON
    }

    def publisherSalesViewers() {
        List publisherSalesViewers;


        String sql = "select u.name,u.email,u.id from wsuser.user u,wsuser.user_role ur,wsuser.role r" +
                " where r.authority='ROLE_FINANCE' and u.publisher_id=" + params.publisherId + " and ur.role_id=r.id and ur.user_id=u.id and u.site_id=" + getSiteId(request)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        publisherSalesViewers = results.collect { user ->
            return [name: user[0], email: user[1], userId: user[2]]
        }


        def json = [status: publisherSalesViewers ? "OK" : "not found", users: publisherSalesViewers]
        render json as JSON
    }

    def Integer getSiteId(request) {
        Integer siteId = new Integer(1);
        if (session["siteId"] != null) {
            siteId = (Integer) session["siteId"]
        } else {
            def jsonObj = request.JSON
            if (jsonObj.siteId != null) siteId = new Integer(jsonObj.siteId);
            else if (params.siteId != null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminAllUnpublishedBooks() {
        def search = params."search[value]";
        def start = params.start;
        def length = params.length;
        Integer siteId = getSiteId(request)
        if(session["userdetails"].publisherId!=null  && (siteId.intValue()==1||siteId.intValue()==25)) {
            def count = dataProviderService.getUnpublishedBooksCountPublisher(getSiteId(request), search,session["userdetails"].publisherId);
            List booksList = dataProviderService.getUnpublishedBooksForPublisherAdmin(getSiteId(request), start, length, search,session["userdetails"].publisherId);
            def json = [data: booksList, "recordsTotal": count, "draw": params.draw, "recordsFiltered": count]
            render json as JSON
        }else{
            def count = dataProviderService.getUnpublishedBooksCount(getSiteId(request), search);
            List booksList = dataProviderService.getUnpublishedBooksForAdmin(getSiteId(request), start, length, search);
            def json = [data: booksList, "recordsTotal": count, "draw": params.draw, "recordsFiltered": count]
            render json as JSON
        }

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def getAdminAllPublishedBooks() {
        def search = params."search[value]";
        def start = params.start;
        def length = params.length;
        Integer siteId = getSiteId(request)
        if(session["userdetails"].publisherId!=null &&   (siteId.intValue()==1||siteId.intValue()==25)) {
            def count = dataProviderService.getPublishedBooksCountPublisher(getSiteId(request), search,session["userdetails"].publisherId);
            List booksList = dataProviderService.getPublishedBooksListForPublisherAdmin(getSiteId(request), start, length, search,session["userdetails"].publisherId);
            def json = [data: booksList, "recordsTotal": count, "draw": params.draw, "recordsFiltered": count]
            render json as JSON
        }else{
            def count = dataProviderService.getPublishedBooksCount(getSiteId(request), search);
            List booksList = dataProviderService.getPublishedBooksListForAdmin(getSiteId(request), start, length, search);
            def json = [data: booksList, "recordsTotal": count, "draw": params.draw, "recordsFiltered": count]
            render json as JSON
        }
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def getWSUnpublishedMyBooks() {
        if (redisService.("getWSUnpublishedMyBooks_" + springSecurityService.currentUser.username) == null) {
            dataProviderService.getWSUnpublishedMyBooks();
        }

        List booksList = new JsonSlurper().parseText(redisService.("getWSUnpublishedMyBooks_" + springSecurityService.currentUser.username))
        def json = [booksList: booksList]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def getWSPublishedMyBooks() {
        if (redisService.("getWSPublishedMyBooks_" + springSecurityService.currentUser.username) == null || redisService.("getWSPublishedMyBooks_" + springSecurityService.currentUser.username) == "null") {
            dataProviderService.getWSPublishedMyBooks();
        }

        List booksList = new JsonSlurper().parseText(redisService.("getWSPublishedMyBooks_" + springSecurityService.currentUser.username))
        def json = [booksList: booksList]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def addBookToUser() {
        double discountAmount=0
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        Integer siteId = new Integer(1);
        if(params.siteId!="" && params.siteId!=null && !"null".equals(params.siteId)) {
             siteId = Integer.parseInt(params.siteId)
        }else{
            siteId = getSiteId(request)
        }
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        User user = User.findByUsername(""+siteId+"_"+params.emailId)

        Integer poNo
        String status = "error"
        PurchaseOrder po = new PurchaseOrder()
        int noOfTokens = 0;

        if (redisService.("bookPriceDetails_" + booksMst.id) == null) bookPriceService.getBookPrices(new Integer("" + booksMst.id))
        List bookPriceDtls = new JsonSlurper().parseText(redisService.("bookPriceDetails_" + booksMst.id));
        Double eBookPrice = new Double(0.0)
        bookPriceDtls.each { bookPrice ->
            if(bookPrice.freeChatTokens!=null) noOfTokens = Integer.parseInt(""+bookPrice.freeChatTokens)
            if ("eBook".equals(bookPrice.bookType)) {
                eBookPrice = bookPrice.sellPrice

            }
        }
        if(session["userdetails"].publisherId==null||(booksMst.publisherId.intValue()==session["userdetails"].publisherId.intValue())) {
            if (params.paymentId != null && !"".equals(params.paymentId) && booksMst != null && user != null) {
                po.dateCreated = new Date()
                po.itemCode = booksMst.id
                po.currency = "INR"
                po.status = 'Active'
                po.username = user.username
                po.siteId = siteId
                po.paymentId = params.paymentId
                po.poFor = "Book"
                po.bookType= params.bookType


                if(booksMst.isbn!=null && booksMst.isbn!="") po.gstPercentage=5
                else po.gstPercentage=18
                if(params.discountId!=null && !"".equals(params.discountId)){
                    DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(new Integer(params.discountId));
                    if(discountPriceDtl !=null){
                        if(discountPriceDtl.discountValue!=null){
                            discountAmount=discountPriceDtl.discountValue
                        }else if(discountPriceDtl.discountPercentage!=null){
                            //calcalute the amount by percentage
                            Integer perc= discountPriceDtl.discountPercentage;
                            discountAmount=((eBookPrice * perc) / 100)
                            String pAchualPrice = String.format("%.2f",discountAmount)
                            discountAmount = Double.parseDouble(pAchualPrice)
                        }
                    }
                    po.bookPrice = eBookPrice
                    po.amount = eBookPrice-discountAmount
                    po.discountAmount=discountAmount
                    po.discountId= new Integer(params.discountId)
                }else{
                    po.amount = eBookPrice
                }

                //now find the appropriate cart id
                ShoppingCartOrdersMst shoppingCartOrdersMst = ShoppingCartOrdersMst.findByUsernameAndTotalPrice(user.username, po.amount,[sort:"id",order:"desc"])
                if(shoppingCartOrdersMst!=null)
                    po.cartMstId=shoppingCartOrdersMst.id
                po.save(failOnError: true, flush: true)
                poNo = po.id
            }

            if (booksMst != null && user != null) {
                BooksPermission booksPermission = new BooksPermission()
                booksPermission.bookId = booksMst.id
                booksPermission.username = user.username
                if("testSeries".equals(params.bookType)) booksPermission.testsPurchased="true"
                if (params.paymentId != null && !"".equals(params.paymentId)) {
                    booksPermission.poNo = poNo
                    booksPermission.poType = 'PURCHASE'
                } else {
                    booksPermission.poType = 'ADDEDFORFREE'
                }
                if(noOfTokens>0) {
                    if(booksMst.packageBookIds!=null){

                            if(user.chatTokensBalance==null) user.chatTokensBalance=noOfTokens
                            else  user.chatTokensBalance = new Integer(user.chatTokensBalance.intValue() + noOfTokens)
                            user.save(failOnError: true, flush: true)
                    }
                    else booksPermission.chatTokensBalance = noOfTokens

                }
                else if("ebookwithai".equals(booksMst.bookType)||"bookgpt".equals(booksMst.bookType)) booksPermission.chatTokensBalance = 10

                booksPermission.addedBy = springSecurityService.currentUser.username
                if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                    Calendar c = Calendar.getInstance()
                    c.add(Calendar.DATE, booksMst.validityDays)
                    booksPermission.expiryDate = c.getTime()
                }
                booksPermission.bookType= params.bookType
                booksPermission.save(failOnError: true, flush: true)
                if (siteId.intValue() == 1 && params.paymentId != null && !"".equals(params.paymentId)) {
                    if (userManagementService.validateEmail(user.email, siteId)) {
                        String loginId = user.username
                        try {
                            userManagementService.userBookPurchase(booksMst.price + "", user.email,
                                    user.name, po.paymentId, loginId.split(siteId + "_")[1], booksMst.title)
                        } catch (Exception e) {
                            println "Purchase user  email failed " + e.toString()
                        }
                    }
                    if (user.mobile != null && user.mobile != "") {
                        try {
                            def message = "Dear ${user.name}, Thank you for purchasing the Book on Wonderslate. Your Book is available in My Books section.-- Wonderslate Technologies Pvt Ltd"
                            utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                        }catch (Exception e) {
                            println "Purchase user  sms failed " + e.toString()
                        }
                    }
                }
                //code to add package books
                if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                if(siteMst.allBooksLibrary=="true" || getSiteId(request)==25) dataProviderService.getBooksListForUser(user.username)
                else redisService.("userShelfBooks_"+user.username)=null
                status = "Ok"
            }
        }
        def json = ["status": status]
        render json as JSON
    }


    def getSiteMst() {
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(params.siteId))

        def json = ["siteInfo": siteMst]
        render json as JSON
    }

    def deleteuser() {

    }

    @Secured(['ROLE_DELETE_USER'])
    @Transactional
    def deleteuserdetails() {
        String status = deleteuserdetailsMethod(params.user)
        def json = [status: status]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def deleteUserByUser() {
        String username = springSecurityService.currentUser.username
        println("Calling deleteUserByUser and the incoming username is "+username)
        String status = deleteuserdetailsMethod(username)
        println("return status is "+status)
        def json = [status: status]
        render json as JSON
    }

    @Transactional
    def deleteuserdetailsMethod(username) {
        String status = "error"
        User user = User.findByUsername(username)
        if (user != null) {
            def id = user.id
            deleteData("annotator_mst", username, "dataSource_wsuser")
            deleteData("purchase_order", username, "dataSource_wsshop")
            deleteData("resource_group_dtl", username, "dataSource")
            deleteData("answers", username, "dataSource")
            deleteData("wscomm.questions", username, "dataSource_wscomm")
            deleteData("votes", username, "dataSource")
            deleteData("wsuser.batch_user_dtl", username, "dataSource_wsuser")
            deleteData("forms_mst", username, "dataSource")
            deleteData("wsuser.institute_user_dtl", username, "dataSource_wsuser")
            deleteData("wsuser.app_version", username, "dataSource_wsuser")
            deleteData("wslog.books_view_dtl", username, "dataSource_wslog")
            deleteData("wscomm.device_information", username, "dataSource_wscomm")
            deleteData("wslog.key_value_recorder", username, "dataSource_wslog")
            deleteData("wslog.quiz_issues", username, "dataSource_wslog")
            deleteData("wslog.quizrecorder", username, "dataSource_wslog")
            deleteData("wslog.resource_view", username, "dataSource_wslog")
            deleteData("search", username, "dataSource")
            deleteData("wsuser.user_chapter_dtl", username, "dataSource_wsuser")
            deleteData("wsuser.user_resource_dtl", username, "dataSource_wsuser")
            deleteData("book_rating_reviews", username, "dataSource_wsshop")
            deleteData("wsuser.books_permission", username, "dataSource_wsuser")
            deleteData("wslog.chapter_access", username, "dataSource_wslog")
            deleteData("wsuser.authentication_token", username, "dataSource_wsuser")
            deleteData("friend_invite", username, "dataSource")
            deleteData("groups_dtl", username, "dataSource")
            deleteData("wslog.points_dtl", username, "dataSource_wslog")
            deleteData("wslog.user_log", username, "dataSource_wslog")
            deleteUserroleData("wsuser.user_role", id)
            deleteData("wsuser.user", username, "dataSource_wsuser")

            status = "OK"
        }
        return status
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def deleteData(String tableName, String userid, String schema) {
        def dataSource = grailsApplication.mainContext.getBean(schema)
        new Sql(dataSource).execute("delete from " + tableName + " where username='" + userid + "'")
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def deleteUserroleData(String tableName, Long id) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        new Sql(dataSource).execute("delete from " + tableName + " where user_id='" + id + "'")
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def priceList() {
        println("params.bookType="+params.bookTypeUpdate)
        if ("submit".equals(params.mode)) {
            def file = request.getFile('excelFile')
            if (!file.empty) {
                def sheetheader = []
                def values = []
                def workbook = new XSSFWorkbook(file.getInputStream())
                def sheet = workbook.getSheetAt(0)

                for (cell in sheet.getRow(0).cellIterator()) {
                    sheetheader << cell.stringCellValue
                }

                def headerFlag = true
                for (row in sheet.rowIterator()) {
                    if (headerFlag) {
                        headerFlag = false
                        continue
                    }
                    def value = ''
                    def map = [:]
                    for (cell in row.cellIterator()) {
                        switch (cell.cellType) {
                            case 1:
                                value = cell.stringCellValue
                                map["${sheetheader[cell.columnIndex]}"] = value
                                break
                            case 0:
                                value = cell.numericCellValue
                                map["${sheetheader[cell.columnIndex]}"] = value
                                break
                            default:
                                value = ''
                        }
                    }
                    values.add(map)
                }
                def updateBooks = []
                values.each { v ->
                    double data
                    int value
                    def isbn
                    BooksMst booksMst
                    if(v.Id!=null) {
                        data = v.Id;
                        value = (int) data;
                        booksMst = dataProviderService.getBooksMst(value);
                    }else if(v.ISBN!=null){
                        isbn =(BigInteger) new Double(v.ISBN)
                        booksMst=dataProviderService.getBooksMstByIsbn(isbn+"")
                    }
                    if (booksMst != null) {
                        if(session["userdetails"].publisherId==null||session["userdetails"].publisherId.intValue()==booksMst.publisherId.intValue()) {

                            BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+booksMst.id),params.bookTypeUpdate)
                            if(bookPriceDtl==null){
                                bookPriceDtl = new BookPriceDtl(bookId: new Integer(""+booksMst.id),bookType: params.bookTypeUpdate,currencyCd:"INR",
                                        sellPrice: v.Price,listPrice: v.ListPrice)
                                bookPriceDtl.save(failOnError: true, flush: true)
                                updateBooks += booksMst;
                            }
                            else if(Double.compare(bookPriceDtl.sellPrice,v.Price)!=0||Double.compare(bookPriceDtl.listPrice,v.ListPrice)!=0)
                            {
                                try {
                                    bookPriceDtl.sellPrice = v.Price
                                    bookPriceDtl.listPrice = v.ListPrice
                                    bookPriceDtl.save(failOnError: true, flush: true)
                                }catch (Exception e){
                                    println("The price update exception happened for bookId="+booksMst.id)
                                    println("The exception is "+e.toString())
                                }
                                updateBooks += booksMst;
                            }


                        }

                    }

                }
                List publishers

                if(session["userdetails"].publisherId!=null) {
                    publishers = Publishers.findAllById(session["userdetails"].publisherId)
                } else {
                    if(redisService.("publishers_" + getSiteId(request))==null)
                        dataProviderService.getPublishers(getSiteId(request))

                    publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
                }
                List bookTypes = BookProductTypes.findAll()

                [updateBooks: updateBooks,publishers:publishers,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),bookTypes:bookTypes];

            }
        }else{
            List publishers

            if(session["userdetails"].publisherId!=null) {
                publishers = Publishers.findAllById(session["userdetails"].publisherId)
            } else {
                if(redisService.("publishers_" + getSiteId(request))==null)
                    dataProviderService.getPublishers(getSiteId(request))

                publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
            }
            List bookTypes = BookProductTypes.findAll()

            [publishers:publishers,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),bookTypes:bookTypes]
        }
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def videoExplanationUpdate() {
        if ("submit".equals(params.mode)) {
            def file = request.getFile('excelFile')
            if (!file.empty) {
                def sheetheader = []
                def values = []
                def workbook = new XSSFWorkbook(file.getInputStream())
                def sheet = workbook.getSheetAt(0)

                for (cell in sheet.getRow(0).cellIterator()) {
                    sheetheader << cell.stringCellValue
                }

                def headerFlag = true
                for (row in sheet.rowIterator()) {
                    if (headerFlag) {
                        headerFlag = false
                        continue
                    }
                    def value = ''
                    def map = [:]
                    for (cell in row.cellIterator()) {
                        switch (cell.cellType) {
                            case 1:
                                value = cell.stringCellValue
                                map["${sheetheader[cell.columnIndex]}"] = value
                                break
                            case 0:
                                value = cell.numericCellValue
                                map["${sheetheader[cell.columnIndex]}"] = value
                                break
                            default:
                                value = ''
                        }
                    }
                    values.add(map)
                }

                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.quizId))
                List objectives = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: "id"])
                int n = 0;
                def status = "";
                for(int i=0;i< objectives.size();i++){
                    String startTime=null
                    String Endtime=null
                    ObjectiveMst objectiveMst = ObjectiveMst.findById(new Long(objectives[n].id))
                        if (values[i].Starttime != "" && values[i].Starttime != null) {
                            startTime = values[i].Starttime;
                            startTime = startTime.replace("T", "")

                        }
                        if (values[i].Endtime != "" && values[i].Endtime != null) {
                            Endtime = values[i].Endtime;
                            Endtime = Endtime.replace("T", "")
                        }
                        objectiveMst.startTime = startTime
                        objectiveMst.endTime = Endtime
                        objectiveMst.explainLink = values[i].Youtubelink != "" && values[i].Youtubelink != null ? getYoutubeVideoId(values[i].Youtubelink) : null
                        if (values[i].Difficultytype != "" && values[i].Difficultytype != null) {
                            objectiveMst.difficultylevel = values[i].Difficultytype
                        }else{
                            objectiveMst.difficultylevel = "Medium"
                        }

                        if (values[i].Subject != "" && values[i].Subject != null) {
                            objectiveMst.subject = values[i].Subject
                        }else{
                            objectiveMst.subject = null
                        }
                        objectiveMst.save(flush: true, failOnError: true)
                        status = "OK";
                       n++;
                    }

                [status: status];
            }
        }
    }

    def getYoutubeVideoId(String youtubeUrl) {
        String video_id = "";
        if (youtubeUrl.contains("youtu.be") || youtubeUrl.contains("youtube")) {
            if (youtubeUrl != null && youtubeUrl.trim().length() > 0 && youtubeUrl.startsWith("http")) {

                String expression = "^.*((youtu.be" + "\\/)" + "|(v\\/)|(\\/u\\/w\\/)|(embed\\/)|(watch\\?))\\??v?=?([^#\\&\\?]*).*";
                // var regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/;
                CharSequence input = youtubeUrl;
                Pattern pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(input);
                if (matcher.matches()) {
                    String groupIndex1 = matcher.group(7);
                    if (groupIndex1 != null && groupIndex1.length() == 11)
                        video_id = groupIndex1;
                }
            }
            return video_id;
        } else {
            return youtubeUrl;
        }
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def downloadPricelistData() {
        String sql = ""
        if(!"".equals(params.publisherId)){
             sql = "select bm.id,bm.title,bpd.sell_price price,bm.isbn,bpd.list_price listprice" +
                    " from books_mst bm,book_price_dtl bpd" +
                    " WHERE\n" +
                    " bm.status='published'" +
                    " AND   bpd.sell_price>0 AND bm.publisher_id="+params.publisherId+
                     " and bpd.book_id=bm.id and bpd.book_type='"+params.bookType+"'"
        } else {
            sql = "select bm.id,bm.title,bpd.sell_price price,bm.isbn,bpd.list_price listprice" +
                    " from books_mst bm,book_price_dtl bpd" +
                    " WHERE\n" +
                    " bm.status='published'" +
                    " AND   bpd.sell_price>0 AND bm.site_id="+getSiteId(request)+
                    " and bpd.book_id=bm.id and bpd.book_type='"+params.bookType+"'"
        }

        println("price list sql="+sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        if (results != null && results.size() > 0) {
            List data = results.collect { report ->
                return [id: report[0], title: report[1], price: report[2],isbn:report[3],listprice:report[4]
                ]
            }
            List headers = ["Id", "Title", "Price","ListPrice","ISBN"]
            List withProperties = ["id", "title", "price","listprice","isbn"]
            def fileName = "Price_List_Data_" + (new Random()).nextInt(9999999) +"_"+params.bookType+ ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }else{
             render "No books are present for this type. Please change book type and try <a href='/admin/priceList'>Back</a>"
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def publisherWebChatAccessUsers() {
        List publisherWebChatAccessUsers;
        String sql = ""
        if(params.publisherId!=null && params.publisherId!=""){
            sql = "select u.name,u.email,u.id,u.username from wsuser.user u,wsuser.user_role ur,wsuser.role r" +
                    " where r.authority='ROLE_WEB_CHAT' and ur.role_id=r.id and ur.user_id=u.id and u.site_id=" + getSiteId(request) + " and u.publisher_id=" + params.publisherId
        }else{
            sql = "select u.name,u.email,u.id,u.username from wsuser.user u,wsuser.user_role ur,wsuser.role r" +
                    " where r.authority='ROLE_WEB_CHAT' and ur.role_id=r.id and ur.user_id=u.id and u.site_id=" + getSiteId(request)
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        publisherWebChatAccessUsers = results.collect { user ->
            return [name: user[0], email: user[1], userId: user[2], username: user[3]]
        }


        def json = [status: publisherWebChatAccessUsers ? "OK" : "not found", users: publisherWebChatAccessUsers]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def updateWebChatUserRole() {
        List userRoles;
        User user = User.findByEmailAndSiteId(params.user.trim().toLowerCase(), siteId)
        if (user == null) user = User.findByMobileAndSiteId(params.user.trim().toLowerCase(), siteId)
        String status = "Web Chat access granted to the user";
        if (user != null) {
            userRoles = Role.findAllByAuthorityInList(["ROLE_WEB_CHAT"])
            userRoles.each { role ->
                UserRole.create(user, role, true)
            }
        } else {
            status = "error";
        }

        def json = ["status": status]

        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def removeWebChatUserRole() {
        User user = User.findById(new Long(params.userId))
        List userRoles = Role.findAllByAuthorityInList(["ROLE_WEB_CHAT"])
        userRoles.each { role ->
            UserRole.remove(user, role, true)
        }
        def json = ["status": "removed"]

        render json as JSON

    }

    def razorFix() {
        if ("submit".equals(params.mode)) {
            def file = request.getFile('excelFile')
            if (!file.empty) {
                def sheetheader = []
                def values = []
                def workbook = new XSSFWorkbook(file.getInputStream())
                def sheet = workbook.getSheetAt(0)


                for (row in sheet.rowIterator()) {
                    XSSFCell cell1 = row.getCell(0);
                    XSSFCell cell2 = row.getCell(1);
                    XSSFCell cell3 = row.getCell(2);

                    String bookId = "" + (int) cell2.numericCellValue
                    String razorPayId = "" + cell1.stringCellValue
                    String mobile = "" + (int) cell3.numericCellValue


                    URL url;

                    try {
                        // get URL content

                        String a = "https://publish.e-utkarsh.com/payment/" + razorPayId + "?mobile=" + mobile + "&bookId=" + bookId;
                        url = new URL(a);
                        URLConnection conn = url.openConnection();

                        // open the stream and put it into BufferedReader
                        BufferedReader br = new BufferedReader(
                                new InputStreamReader(conn.getInputStream()));

                        String inputLine;
                        while ((inputLine = br.readLine()) != null) {
                            println(inputLine);
                        }
                        br.close();


                    } catch (MalformedURLException e) {
                        e.printStackTrace();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }


                }


            }
            render "ok"

        }

    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def deleteBook() {
        String status = "error"
        Integer siteId = getSiteId(request)
        if (!"".equals(params.bookId)) {
            BooksMst.executeUpdate("update BooksMst set  siteId ='" + -siteId + "' where id='" + params.bookId + "' and status is null")
            BooksMst.wsuser.executeUpdate("update BooksMst set siteId='" + -siteId + "' where id='" + params.bookId + "' and status is null")
            BooksMst.wsshop.executeUpdate("update BooksMst set siteId='" + -siteId + "' where id='" + params.bookId + "' and status is null")
            status = "OK"
            dataProviderService.getWSUnpublishedMyBooks();
        }
        def json = ["status": status]
        render json as JSON
    }

    @Secured(['ROLE_DEBUG_DEVELOPER'])
    def dataRetriever() {

    }

    @Secured(['ROLE_DEBUG_DEVELOPER'])
    def getSqlData() {
        String dataSource = "_" + params.datasource
        String completeQuery = ""+ params.completeQuery
        String fieldsToSelect = "" + params.fields
        String fromDB = "" + params.fromdb
        String conditions = "" + params.conditions
        String aditionalFields = "" + params.adf
        def json
        try {
            String sql = "select " + fieldsToSelect.replace('o~o','%').replace('o*o','&') + " from " + fromDB.replace('o~o','%').replace('o*o','&')
            if (conditions.length() > 0 && !conditions.equals("") && !conditions.equals("null")) {
                sql += " where " + conditions.replace('o~o','%').replace('o*o','&')
            }
            if (aditionalFields.length() > 0 && !aditionalFields.equals("") && !aditionalFields.equals("null")) {
                sql +=  " "+aditionalFields.replace('o~o','%').replace('o*o','&')
            }
            if(completeQuery.trim().length()>0&& !completeQuery.equals("") && !completeQuery.equals("null")){
                sql=completeQuery.replace('o~o','%').replace('o*o','&')
            }
            if(dataSource.equals("_wscontent")){
                dataSource=""
            }
            def dataSource1 = grailsApplication.mainContext.getBean("dataSource" + dataSource)
            def sql2 = new Sql(dataSource1)
            def results1 = sql2.rows(sql);

            json = [
                    'result' : results1,
                    'success': "OK"
            ]
        } catch (SQLException e) {
            json = [
                    'result' : e.localizedMessage,
                    'success': "Failed"
            ]
        }
        render json as JSON
    }


	@Secured(['ROLE_WS_CONTENT_ADMIN'])
    def contentModeration(){

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def getAwaitingApprovalData(){
        if(params.resType!=null && params.resType!=""){
            List<ResourceDtl> dtlList = ResourceDtl.findAllByPrivacyLevelAndResType("awaiting_approval", params.resType)
            def json = [ status:dtlList.size()>0?"ok":"NothingPresent", awaitingApprovalList: dtlList]
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def moderateResource(){
        def sm = SiteMst.findById(getSiteId(request))
        Integer siteId = getSiteId(request)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        if(params.resIdList!=null&&params.resIdList!=""){
            List resIds = params.resIdList.split('-')
            for(int i=0;i<resIds.size();i++){
                ResourceDtl dtl = ResourceDtl.findById(new Long(resIds[i]))
                ResourceDtl.executeUpdate("update ResourceDtl set privacy_level='public' where id='" + resIds[i] + "'")
                def resType=""
                def resLink=""
                if(dtl.resType=='KeyValues'){
                    resType="Flash Card"
                    resLink="/resources/displayFlashCards?resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Notes'){
                    resType="Notes"
                    resLink="/resources/notesViewer?resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Multiple Choice Questions'){
                    resType="Multiple Choice Questions"
                    resLink="/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Reference Videos'){
                    resType="Reference Video"
                    resLink="/videos?fromEmail=true&resId="+dtl.id+"&name="+dtl.resourceName+"&resLink="+dtl.resLink
                }
                else if(dtl.resType=='Reference Web Links'){
                    resType="Reference Web Link"
                    resLink=dtl.resLink
                }
                User user = User.findByUsername(dtl.createdBy)
                if(user.email){
                    userManagementService.sendContentModerationEmail(user.email,user.name,"Your "+resType+" has been made public.",""+resType+" Approved", siteName, clientName,resLink, resType)
                }
                dataProviderService.getLatestFlashCards();
                dataProviderService.getLatestResources('all',siteId)
                dataProviderService.getLatestResources(dtl.resType,siteId)
                if(user.username!=null) {
                    String username = user.username
                    dataProviderService.getUserResources('all', username)
                    dataProviderService.getUserResources(dtl.resType, username)
                }
            }
            def json = [ status:"ok"]
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def rejectResourceModeration(){
        def sm = SiteMst.findById(getSiteId(request))
        Integer siteId = getSiteId(request)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        if(params.resIdList!=null&&params.resIdList!=""){
            List resIds = params.resIdList.split('-')
            for(int i=0;i<resIds.size();i++){
                ResourceDtl dtl = ResourceDtl.findById(new Long(resIds[i]))
                ResourceDtl.executeUpdate("update ResourceDtl set privacy_level='rejected' where id='" + resIds[i] + "'")
                def resType=""
                def resLink=""
                if(dtl.resType=='KeyValues'){
                    resType="Flash Card"
                    resLink="/resources/displayFlashCards?resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Notes'){
                    resType="Notes"
                    resLink="/resources/notesViewer?resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Multiple Choice Questions'){
                    resType="Multiple Choice Questions"
                    resLink="/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId="+dtl.id+"&name="+dtl.resourceName
                }
                else if(dtl.resType=='Reference Videos'){
                    resType="Reference Video"
                    resLink="/videos?fromEmail=true&resId="+dtl.id+"&name="+dtl.resourceName+"&resLink="+dtl.resLink
                }
                else if(dtl.resType=='Reference Web Links'){
                    resType="Reference Web Link"
                    resLink=dtl.resLink
                }

                User user = User.findByUsername(dtl.createdBy)
                if(user.email){
                    userManagementService.sendContentModerationEmail(user.email,user.name,"Your "+resType+" cannot be made public.",""+resType+" Rejected", siteName, clientName,resLink, resType)
                }
                dataProviderService.getLatestFlashCards();
                dataProviderService.getLatestResources('all',siteId)
                dataProviderService.getLatestResources(dtl.resType,siteId)
                if(user.username!=null) {
                    String username = user.username
                    dataProviderService.getUserResources('all', username)
                    dataProviderService.getUserResources(dtl.resType, username)
                }
            }
            def json = [ status:"ok"]
            render json as JSON
        }
    }


    @Secured(['ROLE_CUSTOMER_SUPPORT'])  @Transactional
    def externalOrders() {}

    @Secured(['ROLE_SEARCH_ACCESS'])@Transactional
    def searchDetails(){
        if(""+params.download.equals('true')){
            if(params.fromDate!=null && params.fromDate!="" && params.toDate!=null && params.toDate!=""){
                String sql =
                        " SELECT count(id) nos, search_string FROM search where site_id='"+getSiteId(request)+"' and "+
                                " DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.fromDate+"','%d-%m-%Y') AND " +
                                " DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.toDate+"','%d-%m-%Y') " +
                                " GROUP BY search_string"
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                List data = results.collect {
                    return [
                            nos: it.nos,
                            searchString: it.search_string,
                    ]
                }
                List headers = ["Search Text", "No. of Search" ]
                List withProperties = ["searchString", "nos"]
                def fileName = "Search_Details_Data_"+params.fromDate+"_"+params.toDate+"_"+(new Random()).nextInt(9999999)+".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response,fileName)
                    fillHeader(headers)
                    add(data, withProperties)
                    save(response.outputStream)
                }
            }
        }
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def discountManager(){
        boolean  instituteLibrary=false
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        if(siteMst.allBooksLibrary=="true")instituteLibrary=true
        if("true".equals(params.showList)) {
            def json =  adminService.listDiscounts(params,siteMst.id,session)
            render json as JSON
        }
        List publishers
        if(session["userdetails"].publisherId!=null) {
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        } else {
            if(redisService.("publishers_" + siteMst.id)==null)
                dataProviderService.getPublishers(getSiteId(request))

            publishers =new JsonSlurper().parseText(redisService.("publishers_"+siteMst.id))
        }
        [publishers:publishers,instituteLibrary:instituteLibrary]
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addDiscount(){
        Integer siteId = getSiteId(request)
        def json =  adminService.addDiscount(params,request,session,siteId)
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def closeDiscountById(){
        def json = adminService.closeDiscountById(params)
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getDiscountForUser(){
        Integer siteId = getSiteId(request)
        def json = adminService.getDiscountForUser(params, siteId)
        render json as JSON
    }

    def getBookDiscount(){
        Integer siteId = getSiteId(request)
        def json = adminService.getBookDiscount(params,siteId)
        render json as JSON
    }

    def checkManualCoupon(){
        Integer siteId = getSiteId(request)
        def json = adminService.checkManualCoupon(params,siteId)
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def checkUserReferralCode(){
        Integer siteId = getSiteId(request)
        String status="OK"
        PurchaseOrder purchaseOrder = PurchaseOrder.findByUsernameAndSiteId(siteId + "_"+params.username,siteId)
        if(purchaseOrder==null) status="error"
        def json =['status':status]
        render json as JSON
    }

    @Transactional
    def informationType(){
        def referenceLink=params.referenceLink;
        def videoLink=params.videoLink;
        def json
        InformationMst informationMst
        if("add".equals(params.action1)){
            informationMst=new InformationMst(
                    title: params.title,
                    description: params.description,
                    level: params.level,
                    syllabus: params.syllabus,
                    grade: params.grade,
                    subject: params.subject,
                    resourceType: params.resType,
                    answer: params.answer,
                    showAnswer: new Boolean(params.showAnswer),
                    showFullDetails: new Boolean(params.showFullDetails),
                    referenceLink:  referenceLink?referenceLink.replaceAll('~','&'):null,
                    videoLink:  videoLink?videoLink.replaceAll('~','&'):null,
                    createdBy: springSecurityService.currentUser.username,
                    lastUpdatedBy: springSecurityService.currentUser.username,
                    language:params.language,
                    plainDescription: params.plainDescription,
                    currentAffairsType: params.currentAffairsType,
                    siteId: new Integer(""+session["siteId"])

            )
            json = adminService.saveInformation(informationMst)
            render json as JSON
        }else if("edit".equals(params.action1)) {
            informationMst = InformationMst.findById(new Long(params.id))
            if(informationMst!=null){
                informationMst.title = params.title
                informationMst.description = params.description
                informationMst.resourceType = params.resType
                informationMst.level = params.level
                informationMst.syllabus = params.syllabus
                informationMst.grade = params.grade
                informationMst.subject = params.subject
                informationMst.answer=params.answer
                informationMst.showAnswer=new Boolean(params.showAnswer)
                informationMst.showFullDetails=new Boolean(params.showFullDetails)
                informationMst.referenceLink=referenceLink?referenceLink.replaceAll('~','&'):null
                informationMst.videoLink= videoLink?videoLink.replaceAll('~','&'):null
                informationMst.language = params.language
                informationMst.currentAffairsType = params.currentAffairsType
                informationMst.lastUpdatedBy=springSecurityService.currentUser.username
                informationMst.plainDescription = params.plainDescription
                informationMst.currentAffairsType = params.currentAffairsType
                json = adminService.saveInformation(informationMst)
                render json as JSON
            }

        }else{
            [levelsMstList: LevelsMst.listOrderBySortBy(), siteMst: dataProviderService.getSiteMst(utilService.getSiteId(request, session)),
             commonTemplate:"true",langMstList:com.wonderslate.data.LangMst.listOrderBySortBy(),
            currentAffairsType:CurrentAffairsMst.findAllBySiteId(new Integer(""+session["siteId"]))]
        }
    }

    @Transactional @Secured(['ROLE_INFORMATION_ADMIN'])
    def getInformationListPaginated(){
        def search = params."search[value]";
        //any condition added to query must be added to both sql and sqlCount
        String sql,sqlCount
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        Integer siteId = utilService.getSiteId(request,session)
        if(siteId.intValue()!=1){
            SiteMst siteMst = dataProviderService.getSiteMst(siteId)
            if(siteMst!=null&&"true".equals(siteMst.prepjoySite)) siteId = new Integer(1)
        }
        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                "COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                "COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),COALESCE( language,''),COALESCE( plain_description,''),COALESCE( current_affairs_type,'') " +
                "FROM information_mst im WHERE im.site_id="+siteId+" and "
        if(!"".equals(params.resType)) sql+= " resource_type= '"+ params.resType+"' and "
        if (params.startDate != null && params.startDate != "")
            sql += " date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y') and "

        if (params.endDate != null && params.endDate != "")
            sql += " date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y') and "

        if(search!=null && search!="") {
            sql +=  "  (id  LIKE '%"+search+"%' OR title  LIKE '%"+search+"%')"
        }

        if(sql.endsWith("and ")) sql=sql.substring(0,(sql.length()-4))
        if(sql.endsWith("WHERE ")) sql=sql.substring(0,(sql.length()-6))
        sql += " order by date_created desc  limit "+ params.start + " , " + params.length + ""

        def results = sql1.rows(sql).collect{info->
            return [

                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14]],language: info[15],plainDescription: info[16],
                    currentAffairsType: info[17]
            ]
        }

        //for data count
        sqlCount = "SELECT count(id) FROM information_mst im WHERE  im.site_id="+siteId+" and "
        if(!"".equals(params.resType)) sqlCount+= " resource_type= '"+ params.resType+"' and "
        if (params.startDate != null && params.startDate != "")
            sqlCount += " date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y') and "

        if (params.endDate != null && params.endDate != "")
            sqlCount += " date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y') and "
        if(search!=null && search!="") {
            sqlCount +=  " (id  LIKE '%"+search+"%' OR title  LIKE '%"+search+"%')"
        }

        if(sqlCount.endsWith("and ")) sqlCount=sqlCount.substring(0,(sqlCount.length()-4))
        if(sqlCount.endsWith("WHERE ")) sqlCount=sqlCount.substring(0,(sqlCount.length()-6))
        def count = sql1.rows(sqlCount)[0][0]
        def json = [ "recordsTotal": count,"data": results,draw: params.draw,recordsFiltered: count ]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_INFORMATION_ADMIN'])
    def updateInformationDeepLink(){
        if(params.id!=null && !"".equals(params.id) && params.deepLink!=null && !"".equals(params.deepLink)){
            InformationMst.executeUpdate("update InformationMst set deepLink='" + params.deepLink + "' where id=" + params.id)
        }
    }

    @Transactional @Secured(['ROLE_INFORMATION_ADMIN'])
    def uploadInformationImage(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("upload");
        String cdnPath = ""+grailsApplication.config.grails.cdn.path
        if (!file.empty) {
            File uploadDir = new File("supload/information/")
            if (!uploadDir.exists()) uploadDir.mkdirs()
            String filename = (new Random()).nextInt(9999999)+"-"+file.originalFilename.replaceAll("\\s+", "")
            //saving original image finally
            file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
            def json = [uploaded:1,filename: filename, url: cdnPath+"/supload/information/"+filename]
            render json as JSON
        }
    }

    @Transactional @Secured(['ROLE_INFORMATION_ADMIN'])
    def deleteInformationById(){
        InformationMst informationMst = InformationMst.findById(new Integer(params.id))
        String resourceType = informationMst.resourceType
        InformationMst.executeUpdate("delete InformationMst where id=" + params.id)
        adminService.resetCacheForResType(resourceType)
        def json = [status: "OK"]
        render json as JSON
    }

    @Transactional
    def informationPage(){
        boolean showDetails = true
        boolean showTitle = true
        boolean showAnswerOption = false
        if("jokes".equals(params.pageName)||"puzzles".equals(params.pageName)){
            showDetails = false
            showTitle = false
            if("puzzles".equals(params.pageName))
            showAnswerOption = true
        }
        [commonTemplate:"true",showDetails:showDetails,showTitle:showTitle,showAnswerOption:showAnswerOption,
        title:params.resType+" - Wonderslate"]
    }

    @Transactional
    def informationPageDtl(){
        InformationMst informationMst = InformationMst.findById(new Long(params.id))
        [commonTemplate:"true",informationMst:informationMst,
         title:informationMst.title+" - Wonderslate"]

    }



    @Transactional
    def getInformationList(){
        int pageNo=0
        String resType =  params.resType

        if(params.pageNo!=null) pageNo = Integer.parseInt(params.pageNo)
        if(redisService.("info_"+resType.replaceAll(" ","-")+"_List_"+pageNo)==null||"null".equals(redisService.("info_"+resType.replaceAll(" ","-")+"_List_"+pageNo))){
            adminService.getInformationList(pageNo,resType)
        }
        def json = [results:redisService.("info_"+resType.replaceAll(" ","-")+"_List_"+pageNo) ]
        render json as JSON
    }
    
    @Transactional
    
    def getInformationDetails(){
        Integer id = Integer.parseInt(params.id)
        if(redisService.("infoDetails_"+id)==null) adminService.getInformationDetails(id,params.resType)

        def json = [infoDetails:redisService.("infoDetails_"+id)]

        render json as JSON
    }

    @Transactional

    def getCurrentAffairsLatestAndStartDates(){
        String currentAffairsType = "Main"

        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType

        if(redisService.("currentAffairsLatestDate"+"_"+currentAffairsType)==null) adminService.getCurrentAffairsLatestAndStartDates(currentAffairsType)
        def json = ['latestDate':redisService.("currentAffairsLatestDate"+"_"+currentAffairsType),'startingDate':redisService.("currentAffairsStartingDate"+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional

    def getCurrentAffairsReadingMaterials(){
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType

        if(redisService.("currentAffairsRead_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsReadingMaterials(params.inputDate,currentAffairsType)
        def json = ['readingMaterials':redisService.("currentAffairsRead_"+params.inputDate+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional

    def getCurrentAffairsVideos(){
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType

        if(redisService.("currentAffairsVideos_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsVideos(params.inputDate,currentAffairsType)
        def json = ['videos':redisService.("currentAffairsVideos_"+params.inputDate+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional

    def getCurrentAffairsAudios(){
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType

        if(redisService.("currentAffairsAudios_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsAudios(params.inputDate,currentAffairsType)
        def json = ['audios':redisService.("currentAffairsAudios_"+params.inputDate+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional

    def getCurrentAffairsQuizId(){
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType
        if (redisService.("currentAffairsQuiz_" + params.inputDate+"_"+currentAffairsType) == null) adminService.getCurrentAffairsQuizId(params.inputDate,currentAffairsType)
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(redisService.("currentAffairsQuiz_" + params.inputDate+"_"+currentAffairsType)))
        def json = []
        if(resourceDtl!=null){
             json = [resId:resourceDtl.id, language1: resourceDtl.language1,language2: resourceDtl.language2]
        }
        render json as JSON
    }

    @Transactional

    def getCurrentAffairsForDate(){
        String currentAffairsType = "Main"
        if(params.currentAffairsType!=null) currentAffairsType = params.currentAffairsType


        if(redisService.("currentAffairsRead_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsReadingMaterials(params.inputDate,currentAffairsType)

        if(redisService.("currentAffairsVideos_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsVideos(params.inputDate,currentAffairsType)
        if (redisService.("currentAffairsQuiz_" + params.inputDate+"_"+currentAffairsType) == null) adminService.getCurrentAffairsQuizId(params.inputDate,currentAffairsType)
        if(redisService.("currentAffairsAudios_"+params.inputDate+"_"+currentAffairsType)==null) adminService.getCurrentAffairsAudios(params.inputDate,currentAffairsType)

        def json = ['readingMaterials':redisService.("currentAffairsRead_"+params.inputDate+"_"+currentAffairsType),
                    'videos':redisService.("currentAffairsVideos_"+params.inputDate+"_"+currentAffairsType),
                    quizResId:redisService.("currentAffairsQuiz_" + params.inputDate+"_"+currentAffairsType),
                    'audios':redisService.("currentAffairsAudios_"+params.inputDate+"_"+currentAffairsType)]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_BOOK_CREATOR'])
    def accessCodeUsage(){
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_BOOK_CREATOR'])
    def accessCodesForBook(){
        def results;
        def json;
        results= adminService.getUsedAccessCodes(params?.bookId,params?.inputFromDate,params?.inputToDate,new Integer(""+session['siteId']))
        if(results!=null){
            json= ["status": results ? "OK" : "norecords", "codeList": results]
        }
        else{
            json=["status":"failed"]
        }
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_BOOK_CREATOR'])
    def downloadAccessCodesForBook(){
        def inputFromDate=params?.inputFromDate
        def inputToDate=params?.inputToDate
            List data = adminService.getUsedAccessCodes(params?.bookId, params?.inputFromDate, params?.inputToDate, new Integer(""+session['siteId']))
            List headers = ["Book Id", "Title","Campaign", "Access Code", "User Name", "User Email", "User Mobile", "Date Redeemed"]
            List withProperties = ["bookId", "title","campaignName", "accessCode", "name", "email", "mobile", "dateRedeemed"]
            def fileName = "ScratchCardUsageReport_" + inputFromDate + "_" + inputToDate + "_" + (new Random()).nextInt(9999999) + ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
    }

    @Transactional @Secured(['ROLE_BOOK_CREATOR','ROLE_WS_CONTENT_ADMIN'])
    def deleteRelatedVideo(){
        def json
        if(params?.videoId!=null && params?.chapterId!=null){
            if(adminService.deleteRelatedVid(params?.chapterId,params?.videoId)){
                json=["status":"ok"]
            }
            else{
                json=["status":"failed"]
            }
        }
        else{
            json=["status":"failed"]
        }
        render json as JSON
    }

    @Transactional  @Secured(['ROLE_JWPLAYER_LIVE'])
    def jwplayerLive() {
        if("yes".equals(params.jwplayer)) {
            Integer siteId = getSiteId(request)
            def json = adminService.jwplayerLive(params,siteId)
            render json as JSON
        }
    }

    @Transactional @Secured(['ROLE_JWPLAYER_LIVE'])
    def createChannel(){
        Integer siteId = getSiteId(request)
        def json = adminService.createChannel(params,siteId)
        render json as JSON
    }


    @Transactional @Secured(['ROLE_JWPLAYER_LIVE'])
    def getChannelDetailsByChannelId() {
        Integer siteId = getSiteId(request)
        def json = adminService.getChannelDetailsByChannelId(params,siteId)
        render json as JSON
    }

    @Secured(['ROLE_CUSTOMER_SUPPORT'])  @Transactional
    def userBooks(){
        if("true".equals(params.filter)) {
            Integer siteId = getSiteId(request)
            def json = adminService.userBooks(params, siteId)
            render json as JSON
        }
        [commonTemplate:"true"]
    }


    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def manageBatchUsers(){
        if(session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }

        def instituteIds = []

        List instituteUserDtl = InstituteUserDtl.findAllByUsername(springSecurityService.currentUser.username)
        instituteUserDtl.each{ institute ->
            instituteIds << institute.instituteId

        }
        if(instituteIds.size()>0) {
            List batchesList = CourseBatchesDtl.findAllByStatusAndConductedForInList("active", instituteIds,[sort:"name" ])
            List institutes = InstituteMst.findAllByIdInList(instituteIds,[sort:"name" ])
            [batchesList: batchesList, institutes: institutes]
        }else [batchesList: [], institutes: []]
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def getBatchUsersByMonths(){
        def json = adminService.getBatchUsersByMonths(params)
         render json as JSON
    }


    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def deleteBatchUsers(){
        def json = adminService.deleteBatchUsers(params)
        render json as JSON
    }

    @Transactional
    def getNextexamLatestAndStartDates(){
        if(redisService.("nextexamLatestDate")==null) adminService.getNextexamLatestAndStartDates()
        def json = ['latestDate':redisService.("nextexamLatestDate"),'startingDate':redisService.("nextexamStartingDate")]
        render json as JSON
    }

    @Transactional
    def getNextexamMaterials(){
        if(redisService.("nextexamMaterials_"+params.inputDate)==null) adminService.getNextexamMaterials(params.inputDate)
        def json = ['nextexamMaterials':redisService.("nextexamMaterials_"+params.inputDate)]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def manageBookExpiry(){
        if("true".equals(params.filter)) {
            Integer siteId = getSiteId(request)
            def json = adminService.manageBookExpiry(params, siteId)
            render json as JSON
        }
        [commonTemplate:"true"]
    }


    @Transactional @Secured(['ROLE_NOTIFICATION'])
    def manageNotifications(){
    }

    @Transactional @Secured(['ROLE_NOTIFICATION'])
    def sendNotification(){
        def notificationType=params?.notification
        Integer siteId = getSiteId(request)
        def json = ['status':'ok']
        if(notificationType!=null)
        {
            if(notificationType.equals("groupwall"))   dataNotificationService.sendGroupPostNotification(new String(groupsService.getGroupWallIdForSite(siteId)),siteId,"channel")
            else if(notificationType.equals("silent"))     dataNotificationService.prepjoyContentNotification("Main")
            else if(notificationType.equals("bookcreate")) {
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                String sql = "select max(id) as latestBookId from books_mst where site_id=1"
                def result = sql1.rows(sql).collect{book->
                    return [
                            id:book.latestBookId
                    ]
                }
                dataNotificationService.latestBooksChanged((result[0].id)+"")
            }

        }
        else json=['status':'failed']
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def paymentDetails(){
        if("true".equals(params.filter)) {
            Integer siteId = getSiteId(request)
            def json = adminService.paymentDetails(params, siteId)
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addDiscountWS(){
        Integer siteId = getSiteId(request)
        def json =  adminService.addDiscountWS(params,request,session,siteId)
        render json as JSON
    }


    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def emailWhitelist(){
       if(params.userEmail!=null){
           Integer siteId = getSiteId(request)
           def json =  adminService.emailWhitelist(params,siteId)
           render json as JSON
        }
        [hideBanner:true,disciplines:getDisciplines()]
    }

    @Transactional
    def getDisciplines(){
        def sql="SELECT discipline,count(id) FROM books_mst where site_id=9 and discipline is not null and status='published' group by discipline";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List discipline = results.collect{ temp ->
            return [discipline:temp[0],noOfBooks:temp[1]];
        }
        return discipline;
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addBooksToInstituteByExcel(){
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndName(new Long(params.instituteId),"Default")
        def file = request.getFile('file')
        if (!file.empty) {
            def sheetheader = []
            def values = []
            def workbook = new XSSFWorkbook(file.getInputStream())
            def sheet = workbook.getSheetAt(0)

            for (cell in sheet.getRow(0).cellIterator()) {
                sheetheader << cell.stringCellValue
            }

            def headerFlag = true
            for (row in sheet.rowIterator()) {
                if (headerFlag) {
                    headerFlag = false
                    continue
                }
                def value = ''
                def map = [:]
                for (cell in row.cellIterator()) {
                    switch (cell.cellType) {
                        case 1:
                            value = cell.stringCellValue
                            map["${sheetheader[cell.columnIndex]}"] = value
                            break
                        case 0:
                            value = cell.numericCellValue
                            map["${sheetheader[cell.columnIndex]}"] = value
                            break
                        default:
                            value = ''
                    }
                }
                values.add(map)
            }
            def updateBooks = []
            values.each { v ->
                BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBatchIdAndBookId(courseBatchesDtl.id,v.BookId)
                if (booksBatchDtl == null) {
                    booksBatchDtl = new BooksBatchDtl(batchId:  courseBatchesDtl.id, bookId:  v.BookId)
                    booksBatchDtl.save(failOnError: true, flush: true)
                    if(siteId.intValue()==1 || siteMst.instituteLibrary=="true") {
                        redisService.("userMyLibraryInstituteBooks_" + courseBatchesDtl.id) = null
                        wsLibraryCacheService.getInstituteBooksPaginationNew(courseBatchesDtl.id)
                    }

                }

            }
        }

    }

    @Secured(['ROLE_CUSTOMER_SUPPORT'])  @Transactional
    def cartPurchase(){
        if(params.cartMstId!=null){
            Integer siteId = getSiteId(request)
            def json =  adminService.cartPurchase(params,siteId)
            render json as JSON
        }
    }

    @Secured(['ROLE_CAMPAIGN_ADMIN']) @Transactional
    def moderation(){
         if("yes".equals(params.showData)) {
            Integer siteId = getSiteId(request)
            def json = adminService.moderation(siteId,params)
            render json as JSON
        }
    }

    @Secured(['ROLE_CAMPAIGN_ADMIN']) @Transactional
    def moderatePostById(){
        Integer siteId = getSiteId(request)
        def json = adminService.moderatePostById(siteId,params)
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def publisherDsize(){
        List publisher=Publishers.findAll([sort:"id", order:"asc"])
        [publishers:publisher]
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getFolderSizeByPublisherId() {
        long bytes
        List books = BooksMst.findAllByPublisherId(new Integer(params.publisherId))
        books.each {
            File uploadDir = new File("upload/books/" + it.id)
            if (uploadDir.exists()) {
                bytes += FileUtils.sizeOfDirectory(uploadDir);
            }
        }
        String size= FileUtils.byteCountToDisplaySize(bytes)
        def json =[folderSize:size,publisherId:params.publisherId,publisherName:dataProviderService.getPublisher(new Long(params.publisherId)).name]
        render json as JSON
    }

    @Secured(['ROLE_FINANCE','ROLE_ACCOUNTS']) @Transactional
    def refundDetails(){
        List publishers
        List sales = null
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        if("submit".equals(params.mode)) {
            Integer siteId = getSiteId(request)
            def json = adminService.refundDetails(params,siteId,session)
            render json as JSON
        }else{
            if (session["userdetails"].publisherId != null) {
                publishers = Publishers.findAllById(session["userdetails"].publisherId)
            } else {
                if (redisService.("publishers_" + getSiteId(request)) == null)
                    dataProviderService.getPublishers(getSiteId(request))

                publishers = new JsonSlurper().parseText(redisService.("publishers_" + getSiteId(request)))
            }

            [sales    : sales, hideSearch: true, poStartDate: params.poStartDate,
             poEndDate: params.poEndDate, publishers: publishers, publisherId: params.publisherId, select: params.select,
             paymentId: params.paymentId,sitesList:sitesList,commonTemplate:"true"]
           }
        }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def printBooksDownloadPage(){

    }
    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def bookgptPromptAdmin(){
        boolean hasBookAccess = false
        def previewMode = "true".equals(params.preview) ? true : false

        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
        boolean eBookPriceZero = false
        Double upgradePrice = null
        Double eBookPrice = null
        bookPriceDtls.each { bookPrice ->
            if ("eBook".equals(bookPrice.bookType)) {
                if (bookPrice.sellPrice != null && bookPrice.sellPrice.doubleValue() == 0) eBookPriceZero = true
                eBookPrice = bookPrice.sellPrice
            } else if ("upgrade".equals(bookPrice.bookType)) {
                upgradePrice = bookPrice.sellPrice
            }
        }
        if (springSecurityService.currentUser == null || userManagementService.isValidSession(springSecurityService.currentUser.username, session.getId())) {
            hasBookAccess = userManagementService.hasAccessToBook(new Long(params.bookId), session, request, true,response)
            if (!hasBookAccess) {
                hasBookAccess = userManagementService.hasLibraryAccessToBook(new Long(params.bookId), true)
            }

            if (!hasBookAccess) {
                if (!eBookPriceZero && "published".equals(booksMst.status)) {
                    previewMode = true
                } else {
                    if ("true".equals(siteMst.commonWhiteLabel)) {
                        redirect(uri: "/sp/${siteMst.siteName}/store")
                        return
                    }else{
                        redirect(controller: siteMst.siteName, action: 'index')
                        return
                    }

                }
            }
        }
        if (hasBookAccess) previewMode = false

        if(redisService.("allChapterDetails_"+params.bookId)==null) {
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
        }
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }

        String workingKey = "9E74748742AAB8342432FCF15E225793"
        String pdfResp = "pdf" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000
        session.setAttribute('pdfKey', pdfResp)
        AesCryptUtil aesUtilPdf = new AesCryptUtil(workingKey)
        String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)
        String sessionEncPdfKey = session.getAttribute('pdfEncKey')
        if (sessionEncPdfKey != null && !sessionEncPdfKey.isEmpty()) session.removeAttribute('pdfEncKey')

        def basePromptArray = Prompts.list()
        def bookLevelPrompt = booksMst.basePrompt
        List promptList = basePromptArray.collect{it->
            return [
                    promptType:it.promptType,
                    promptLabel:it.promptLabel,
                    basePrompt:it.basePrompt,
            ]
        }
        if(previewMode&&request.getRequestURL().indexOf("libwonder.com")>-1){
            String redirectUrl = (""+request.getRequestURL()).replace("libwonder.com","wonderslate.com")+"?"+request.queryString
            redirect(url:redirectUrl)
        }
        else {
            [
                    encryptPdfKey    : encryptPdfStr,
                    hasBookAccess    : hasBookAccess,
                    chapters:redisService.("allChapterDetails_" +params.bookId),
                    basePromptArray:promptList as JSON,
                    bookLevelPrompt:bookLevelPrompt
            ]
        }
    }
    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def difficultyLevelMapping(){

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addDifficultyMapping() {
        try {

            def requestBody = request.JSON
            String type = requestBody.key
            String values = requestBody.values

            if (!type || !values) {
                def json = [status: 400, message: 'Both "type" and "values" are required.']
                render json as JSON
                return
            }

            if (!type.endsWith('_difficulty')) {
                type += '_difficulty'
            }

            def existingMapping = KeyValueMst.findByKeyName(type)

            if (existingMapping) {
                existingMapping.keyValue = values
                if (existingMapping.save(flush: true)) {
                    def json = [status: 200, message: 'Mapping updated successfully.']
                    render json as JSON
                } else {
                    def json = [status: 500, message: 'Failed to update mapping.']
                    render json as JSON
                }
            } else {
                def newMapping = new KeyValueMst(keyName: type, keyValue: values, siteId: -1)
                if (newMapping.save(flush: true)) {
                    def json = [status: 200, message: 'Mapping created successfully.']
                    render json as JSON
                } else {
                    def json = [status: 500, message: 'Failed to create mapping.']
                    render json as JSON
                }
            }
        } catch (Exception e) {
            println("Error in addDifficultyMapping: ${e.message}")
            def json = [status: 500, message: 'Failed to update mapping.']
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getDifficultLevels(){
        def json = [difficultylevels: promptService.getDifficultyMapping()]
        render json as JSON
    }

    @Secured(['ROLE_CUSTOMER_SUPPORT']) @Transactional
    def bookSupport(){
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        List publishers
        if(session["userdetails"].publisherId==null) {
            if (redisService.("publishers_" + getSiteId(request)) == null) {
                dataProviderService.getPublishers(getSiteId(request))
            }
            publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
        }else{
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        }
        [publishers: publishers,sitesList:sitesList,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
}
