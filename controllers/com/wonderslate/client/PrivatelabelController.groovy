package com.wonderslate.client

import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksCodeMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.SitemapService
import com.wonderslate.data.UtilService
import com.wonderslate.groups.GroupsService
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.PagesMst
import com.wonderslate.shop.PagesService
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.grails.web.util.WebUtils
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

import javax.imageio.ImageIO
import javax.servlet.http.Cookie
import java.awt.image.BufferedImage

class PrivatelabelController {

    def redisService
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    WsshopService wsshopService
    UtilService utilService
    PagesService pagesService
    SitemapService sitemapService
    AsyncLogsService asyncLogsService
    WsLibraryService wsLibraryService

    @Transactional
    def index() {
        if(params.scd!=null) {
            if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                session["scd"] = params.scd
                asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
            }
        }
        userManagementService.setUserSession(params.siteName, session, servletContext, response)
        if(session["customLandingPage"]!=null) redirect(uri: "${session["customLandingPage"]}")
        else {
            if("true".equals(""+session["showLandingPage"])) {
                Integer siteId = session['siteId']
                if (redisService.("bannerList_" + session['siteId']) == null) dataProviderService.getBanners("" + session['siteId'])
                if (redisService.("booksList_newlyReleasedEbook_" + "" + session['siteId']) == null) wsshopService.storeAdditionalInfo(siteId)
                def newlyReleasedEbook = redisService.("booksList_newlyReleasedEbook_" + "" + session['siteId'])
                def bannerList = redisService.("bannerList_" + session['siteId'])
                SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                [commonTemplate: "true", bannerList: bannerList, newlyReleasedEbook: newlyReleasedEbook, showLibrary: utilService.hasLibraryAccess(request, siteId),
                 productYoutubeLink:siteMst.productYoutubeLink,youtubeLinkTitle:siteMst.youtubeLinkTitle ]
            }else if("Yes".equals(session["disableStore"])) redirect(uri: "/privatelabel/loginPage")
            else{
                redirect(uri:"/sp/${params.siteName}/store")
            }
        }
    }

    @Transactional
    def store() {
        if(params.scd!=null) {
            if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                session["scd"] = params.scd
                asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
            }
        }

        if("88".equals(""+session["siteId"])) redirect(uri: "/sp/gptsir/aistore")
        else if("76".equals(""+session["siteId"]) &&session["customLandingPage"]!=null) redirect(uri: "${session["customLandingPage"]}")
        else if("Yes".equals(session["disableStore"])) redirect(uri: "/privatelabel/loginPage")
        else {
            userManagementService.setUserSession(params.siteName, session, servletContext, response)
            int pageNo = 0
            HashMap seo = sitemapService.getSEO(params, new Integer("" + session["siteId"]))
            params.put("level", seo.get("level"))
            params.put("syllabus", seo.get("syllabus"))
            params.put("grade", seo.get("grade"))
            if (params.pageNo != null && !"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
            HashMap booksAndPublishers = wsshopService.getBooksList(params, new Integer("" + session["siteId"]), pageNo)
            if ("true".equals("" + session['wileySite'])) {
                [commonTemplate: "true", booksList: booksAndPublishers, showLibrary: utilService.hasLibraryAccess(request, new Integer("" + session["siteId"])),
                 "title"       : seo.get("browserTitle"), seoDesc: seo.get("browserDescription"), booksList: booksAndPublishers, keywords: seo.get("browserKeywords"),
                 onPageTitle   : seo.get("onPageTitle"), onPageDescription: seo.get("onPageDescription"), storeUrl: "/sp/" + params.siteName + "/store"]
            } else {
                def bannerList = redisService.("bannerList_" + session['siteId'])
                [commonTemplate   : "true", booksList: booksAndPublishers, showLibrary: utilService.hasLibraryAccess(request, new Integer("" + session["siteId"])), bannerList: bannerList,
                 "title"          : seo.get("browserTitle"), seoDesc: seo.get("browserDescription"), booksList: booksAndPublishers, keywords: seo.get("browserKeywords"), onPageTitle: seo.get("onPageTitle"),
                 onPageDescription: seo.get("onPageDescription"), storeUrl: "/sp/" + params.siteName + "/store", publisherDescription: seo.get("publisherDescription") != null ? seo.get("publisherDescription") : null,
                 publisherName    : seo.get("publisherName"), aboutTitle: seo.get("aboutTitle"), aboutDescription: seo.get("aboutDescription")]
            }
        }
    }

    @Transactional
    def aiStore() {
        if(params.scd!=null) {
            if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                session["scd"] = params.scd
                asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
            }
        }

           userManagementService.setUserSession(params.siteName, session, servletContext, response)
            int pageNo = 0
            HashMap seo = sitemapService.getSEO(params, new Integer("" + session["siteId"]))
            params.put("level", seo.get("level"))
            params.put("syllabus", seo.get("syllabus"))
            params.put("grade", seo.get("grade"))
            if (params.pageNo != null && !"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
            HashMap booksAndPublishers = wsshopService.getBooksList(params, new Integer("" + session["siteId"]), pageNo)

                def bannerList = redisService.("bannerList_" + session['siteId'])
                [commonTemplate   : "true", booksList: booksAndPublishers, showLibrary: utilService.hasLibraryAccess(request, new Integer("" + session["siteId"])), bannerList: bannerList,
                 "title"          : seo.get("browserTitle"), seoDesc: seo.get("browserDescription"), booksList: booksAndPublishers, keywords: seo.get("browserKeywords"), onPageTitle: seo.get("onPageTitle"),
                 onPageDescription: seo.get("onPageDescription"), storeUrl: "/sp/" + params.siteName + "/aiStore", publisherDescription: seo.get("publisherDescription") != null ? seo.get("publisherDescription") : null,
                 publisherName    : seo.get("publisherName"), aboutTitle: seo.get("aboutTitle"), aboutDescription: seo.get("aboutDescription")]


    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def allWebsites() {
        [title:"All Privatelabel Websites - Wonderslate", commonTemplate:"true"]
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getAllPrivatelabels() {
        def json
        List privatelabels = dataProviderService.getAllPrivatelabelsList()
        json = [
                'status': privatelabels ? "OK" : "Nothing present",
                'privatelabels': privatelabels
        ]
        render json as JSON
    }

    @Transactional
    def getPrivatelabelDtl(long siteId) {
        def json
        if(redisService.("privatelabelDtl_"+siteId) == null) dataProviderService.getPrivateLabelDetails(new Long(siteId))
        List privatelabelDtl = new JsonSlurper().parseText(redisService.("privatelabelDtl_"+siteId))
        json = [
                'status': privatelabelDtl ? "OK" : "Nothing present",
                'privatelabelDtl': privatelabelDtl
        ]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def createNew() {
        String mode = params.mode

        if("edit".equals(mode) && params.siteId != null && !"".equals(params.siteId)) {
            SiteDtl siteDtl  = dataProviderService.getSiteDtl(new Long(params.siteId))
            [siteDtl : siteDtl, title:"Edit Privatelabel - Wonderslate", commonTemplate:"true"]
        } else {
            [title:"Create New Privatelabel - Wonderslate", commonTemplate:"true"]
        }
    }

    def showPrivatelabelImage(long siteId, String fileName, String imgType) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                String picFileName
                def file
                if ("webp".equals(imgType)) {
                    picFileName = fileName.substring(0, fileName.indexOf(".")) + '.webp'
                    fileName = picFileName
                } else {
                    picFileName = fileName
                }
                file = new File("upload/whitelabel/" + siteId + "/" + picFileName)
                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }
                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        } catch (Exception e) {
            println("Exception in showPrivatelabelImage " + e.toString())
            render "";
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def createPrivatelabel() {
        SiteDtl siteDtl
        def websiteId = new Long(params.websiteId)
        def themeColor = params.themeColor
        def facebookLink = params.facebookLink
        def twitterLink = params.twitterLink
        def instagramLink = params.instagramLink
        def linkedinLink = params.linkedinLink
        def youtubeLink = params.youtubeLink
        def mobileNumber = params.mobileNumber
        def emailAddress = params.emailAddress
        def addressLine1 = params.addressLine1
        def addressLine2 = params.addressLine2
        def gstNumber = params.gstNumber
        def websiteLink = params.websiteLink
        def privacyPolicy = params.privacyPolicy
        def termsCondition = params.termsCondition
        def playStore = params.playStore
        def appStore = params.appStore
        def jurisdictionPlace = params.jurisdictionPlace
        def jurisdictionState = params.jurisdictionState
        def customStyles = params.customStyles
        def companyName = params.companyName
        def enableContactus = params.enableContactus
        def enableOTPLogin = params.enableOTPLogin
        def siteTitle = params.siteTitle
        def siteDescription = params.siteDescription
        def keywords = params.keywords
        def showLandingPage = params.showLandingPage
        def whatsappLink = params.whatsappLink
        def telegramLink = params.telegramLink
        def disableScratchCode = params.disableScratchCode
        def disableStore = params.disableStore
        def canAddBooksFromAllSites = params.canAddBooksFromAllSites
        def showAnalytics = params.showAnalytics
        def customGptLoader = params.enableGptLoader
        def gptLoaderDisplayName = params.gptLoaderDisplayName

        boolean isValidImages = false

//        siteDtl = SiteDtl.findBySiteId(websiteId)
        siteDtl = dataProviderService.getSiteDtl(websiteId)

        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile logo = multiRequest.getFile("logo");
        MultipartFile logoIcon = multiRequest.getFile("logoIcon");
        MultipartFile favicon = multiRequest.getFile("favicon");
        MultipartFile banner = multiRequest.getFile("banner");
        MultipartFile gptLoader = multiRequest.getFile("gptLoaderPath");

        // Processing
        String logoName = logo.originalFilename.replaceAll("\\s+", "")
        String logoIconName = logoIcon.originalFilename.replaceAll("\\s+", "")
        String faviconName = favicon.originalFilename.replaceAll("\\s+", "")
        String bannerName = banner.originalFilename.replaceAll("\\s+", "")
        String gptLoaderName = gptLoader.originalFilename.replaceAll("\\s+", "")

        if (logo.empty && logoIcon.empty && favicon.empty && banner.empty) {
            flash.message = "Files cannot be empty"
        } else {
            isValidImages = true
            // Creating directory
            File uploadDir
            uploadDir = new File("upload/whitelabel/" + websiteId)
            if (!uploadDir.exists()) uploadDir.mkdirs()

            // Saving original images
            // Logo
            if(!logo.empty) {
                try {
                    logo.transferTo(new File(uploadDir.absolutePath + "/" + logoName))
                } catch (Exception e) {
                    println "Uploading logo image is failed " + e.toString()
                }
            }
            // Logo icon
            if(!logoIcon.empty) {
                try {
                    logoIcon.transferTo(new File(uploadDir.absolutePath + "/" + logoIconName))
                } catch (Exception e) {
                    println "Uploading logo icon image is failed " + e.toString()
                }
            }
            // Favicon
            if(!favicon.empty) {
                try {
                    favicon.transferTo(new File(uploadDir.absolutePath + "/" + faviconName))
                } catch (Exception e) {
                    println "Uploading favicon image is failed " + e.toString()
                }
            }
            // Banner image
            if(!banner.empty) {
                try {
                    // Creating webP format
                    BufferedImage bannerImage = ImageIO.read(banner.getInputStream())
                    banner.transferTo(new File(uploadDir.absolutePath + "/" + bannerName))
                    String webPImage = bannerName.substring(0, bannerName.indexOf("."))
                    ImageIO.write(bannerImage, "webp", new File(uploadDir.absolutePath + "/" + webPImage + ".webp"))
                } catch (Exception e) {
                    println "Uploading banner image is failed " + e.toString()
                }
            }
        }

        if (!gptLoader.empty){
            File logouploadDir
            logouploadDir = new File("upload/whitelabel/" + websiteId)
            if (!logouploadDir.exists()) logouploadDir.mkdirs()
            try {
                BufferedImage gptLoaderImage = ImageIO.read(gptLoader.getInputStream())
                gptLoader.transferTo(new File(logouploadDir.absolutePath + "/" + gptLoaderName))
                String webPImage = gptLoaderName.substring(0, gptLoaderName.indexOf("."))
                ImageIO.write(gptLoaderImage, "webp", new File(logouploadDir.absolutePath + "/" + webPImage + ".webp"))
            } catch (Exception e) {
                println "Uploading gpt loader image is failed " + e.toString()
            }
        }
        if(siteDtl != null && siteDtl.siteId == websiteId) {
            if (websiteId != "" && websiteId != null) {
                siteDtl.siteId = siteDtl.siteId
                siteDtl.themeColor = themeColor
                siteDtl.facebookLink = facebookLink
                siteDtl.twitterLink = twitterLink
                siteDtl.instagramLink = instagramLink
                siteDtl.linkedinLink = linkedinLink
                siteDtl.youtubeLink = youtubeLink
                siteDtl.addressLine1 = addressLine1
                siteDtl.addressLine2 = addressLine2
                siteDtl.gstNumber = gstNumber
                siteDtl.websiteLink = websiteLink
                siteDtl.mobileNumber = mobileNumber
                siteDtl.emailAddress = emailAddress
                siteDtl.privacyPolicy = privacyPolicy
                siteDtl.termsCondition = termsCondition
                siteDtl.playStore = playStore
                siteDtl.appStore = appStore
                siteDtl.jurisdictionPlace = jurisdictionPlace
                siteDtl.jurisdictionState = jurisdictionState
                siteDtl.customStyles = customStyles
                siteDtl.companyName = companyName
                siteDtl.enableContactus = enableContactus
                siteDtl.enableOTPLogin = enableOTPLogin
                siteDtl.siteTitle = siteTitle
                siteDtl.siteDescription = siteDescription
                siteDtl.keywords = keywords
                siteDtl.showLandingPage = showLandingPage
                siteDtl.telegramLink = telegramLink
                siteDtl.whatsappLink = whatsappLink
                siteDtl.disableScratchCode = disableScratchCode
                siteDtl.disableStore = disableStore
                siteDtl.canAddBooksFromAllSites = canAddBooksFromAllSites
                siteDtl.showAnalytics = showAnalytics
                siteDtl.customGptLoader = customGptLoader
                siteDtl.gptLoaderDisplayName = gptLoaderDisplayName
                siteDtl.save(failOnError: true, flush: true)

                if (isValidImages) {
                    // Updating logo name in DB
                    if (siteDtl.logo != null && params.logo == "") {
                        siteDtl.logo = siteDtl.logo
                    } else if (params.logo != null && params.logo.filename != "") {
                        siteDtl.logo = params.logo.filename.replaceAll("\\s+", "")
                    }

                    // Updating logo icon name in DB
                    if (siteDtl.logoIcon != null && params.logoIcon == "") {
                        siteDtl.logoIcon = siteDtl.logoIcon
                    } else if (params.logoIcon != null && params.logoIcon.filename != "") {
                        siteDtl.logoIcon = params.logoIcon.filename.replaceAll("\\s+", "")
                    }

                    // Updating favicon name in DB
                    if (siteDtl.favicon != null && params.favicon == "") {
                        siteDtl.favicon = siteDtl.favicon
                    } else if (params.favicon != null && params.favicon.filename != "") {
                        siteDtl.favicon = params.favicon.filename.replaceAll("\\s+", "")
                    }

                    // Updating banner name in DB
                    if (siteDtl.bannerImage != null && params.banner == "") {
                        siteDtl.bannerImage = siteDtl.bannerImage
                    } else if (params.banner != null && params.banner.filename != "") {
                        siteDtl.bannerImage = params.banner.filename.replaceAll("\\s+", "")
                    }
                    siteDtl.save(failOnError: true, flush: true)
                }
                if (siteDtl.gptLoaderPath != null && params.gptLoaderPath == "") {
                    siteDtl.gptLoaderPath = siteDtl.gptLoaderPath
                } else if (params.gptLoaderPath != null && params.gptLoaderPath.filename != "") {
                    siteDtl.gptLoaderPath = params.gptLoaderPath.filename.replaceAll("\\s+", "")
                }
                siteDtl.save(failOnError: true, flush: true)
            }
        } else {
            if (websiteId != "" && websiteId != null) {
                siteDtl = new SiteDtl(siteId: websiteId, themeColor: themeColor, facebookLink: facebookLink, twitterLink: twitterLink,
                        instagramLink: instagramLink, linkedinLink:  linkedinLink, youtubeLink: youtubeLink, addressLine1: addressLine1, addressLine2: addressLine2, gstNumber: gstNumber, websiteLink: websiteLink, mobileNumber: mobileNumber, emailAddress: emailAddress,
                        privacyPolicy: privacyPolicy, termsCondition: termsCondition, playStore: playStore, appStore: appStore, jurisdictionPlace: jurisdictionPlace, jurisdictionState: jurisdictionState,
                        customStyles: customStyles, companyName: companyName,enableContactus: enableContactus,enableOTPLogin:enableOTPLogin,siteTitle: siteTitle,siteDescription: siteDescription,
                        keywords: keywords,showLandingPage: showLandingPage,telegramLink: telegramLink,whatsappLink: whatsappLink,disableScratchCode: disableScratchCode,
                        disableStore: disableStore,canAddBooksFromAllSites: canAddBooksFromAllSites, customGptLoader: customGptLoader, gptLoaderDisplayName: gptLoaderDisplayName,showAnalytics: showAnalytics)
                siteDtl.save(failOnError: true, flush: true)

                // Inserting all the images name in DB
                if (isValidImages) {
                    SiteDtl.executeUpdate("update SiteDtl set logo = '" + logoName + "', logoIcon ='" + logoIconName + "', favicon = '" + faviconName + "', bannerImage = '" + bannerName + "', gptLoaderPath = '"+gptLoaderName+"'  where id=" + siteDtl.id)
                }

            }
        }

        redirect(controller: "privatelabel", action: "allWebsites")
    }

    def customPage(){
        userManagementService.setUserSession(params.siteName, session, servletContext, response)
        String pageTitle = pagesService.getPageTitle(params)
        [title: pageTitle, commonTemplate:"true"]
    }

    @Transactional
    def pageManager() {
//        userManagementService.setUserSession(params.siteName, session, servletContext, response)
        [commonTemplate:"true"]
    }

    @Secured(['ROLE_USER'])
    def customPageCreation(){
        [commonTemplate:"true"]
    }
    @Secured(['ROLE_USER']) @Transactional
    def admin(){
        if(userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId())) {
            User user = session["userdetails"]
            boolean showPublisherControls=false,hasSalesAccess=false,wsAdmin=false,instituteAdmin=false,showPublisherAdminControls=false,informationAdmin=false,
                    guestUser=false,customerSupport=false,salesSupport=false,libraryUserUploader=false,externalSalesAccess=false,libraryAdmin=false,masterLibraryAdmin=false,iBookGPTSiteAdmin=false,
            supportManager=false;

            // for publisher admin controlls
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {

                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) showPublisherAdminControls = true

            // for publisher content uploaders
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {
                it.authority == "ROLE_BOOK_CREATOR"
            }) showPublisherControls = true
            //for wonderslate content creators
            else if(session["userdetails"].publisherId==null &&user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) wsAdmin = true

            //support manager
            if(user.authorities.any {
                it.authority == "ROLE_SUPPORT_MANAGER"
            }) supportManager = true

            //iBookGPT site admin
            if(user.authorities.any {
                it.authority == "ROLE_IBOOKGPT_SITE_ADMIN"
            }) iBookGPTSiteAdmin = true
            //master library admin
            if(user.authorities.any {
                it.authority == "ROLE_MASTER_LIBRARY_ADMIN"
            }) masterLibraryAdmin = true

            //institutes for user
            List userInstitutes
            String instituteId = null
            if(session["userInstitutes"]!=null) userInstitutes = session["userInstitutes"]
            else    {
                userInstitutes=userManagementService.getInstitutesForUser(1,utilService.getIPAddressOfClient(request))
                if(userInstitutes.size()>1){
                    for(int i=0;i<userInstitutes.size();i++){
                        if(!("Default".equals(""+userInstitutes[i].batchName))) userInstitutes.remove(i--)
                    }
                }
                instituteId = userInstitutes.size()>0?""+userInstitutes[0].instituteId:null
                session["userInstitutes"] = userInstitutes
            };
            //sales access
            if(user.authorities.any {
                it.authority == "ROLE_FINANCE" || it.authority == "ROLE_AFFILIATION_SALES"
            }) {
                hasSalesAccess = true
            }

            //instituteAdmin users - eClass+
            if(user.authorities.any {
                it.authority == "ROLE_INSTITUTE_ADMIN"||it.authority == "ROLE_INSTITUTE_REPORT_MANAGER"
            }) {
                instituteAdmin = true
            }

            //library user uploader
            if(user.authorities.any {
                it.authority == "ROLE_LIBRARY_USER_UPLOADER"
            }) {
                libraryUserUploader = true
            }
            if(user.username.contains("1_cookie_"))guestUser=true
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_INFORMATION_ADMIN"
            }) {
                informationAdmin = true
            }



            //customer support access
            if(user.authorities.any {
                it.authority == "ROLE_CUSTOMER_SUPPORT"
            }) {
                customerSupport = true
            }

            //sales team access
            if(user.authorities.any {
                it.authority == "ROLE_WS_SALES_TEAM"
            }) {
                salesSupport = true
            }
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_EXTERNAL_SALES_VIEWER"
            }) {
                externalSalesAccess = true
            }

            //library admin
            if(user.authorities.any {
                it.authority == "ROLE_LIBRARY_ADMIN"
            }) {
                libraryAdmin = true
            }

            [userInstitutes:userInstitutes,showPublisherControls:showPublisherControls,hasSalesAccess:hasSalesAccess,
             wsAdmin:wsAdmin,instituteAdmin:instituteAdmin, commonTemplate:"true", showPublisherAdminControls:showPublisherAdminControls,informationAdmin:informationAdmin,guestUser:guestUser,
             customerSupport:customerSupport,salesSupport:salesSupport,libraryUserUploader:libraryUserUploader,externalSalesAccess:externalSalesAccess,libraryAdmin:libraryAdmin,
             masterLibraryAdmin:masterLibraryAdmin,iBookGPTSiteAdmin:iBookGPTSiteAdmin,instituteId:instituteId,supportManager:supportManager]
        }else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect([uri: '/logoff'])
        }
    }

    @Transactional
    def wileySignup(){
        [title: 'Sign-up', commonTemplate:"true"]
    }

    @Transactional
    def ibookso(){
        if(session.getAttribute("entryController")==null) {
            userManagementService.setUserSession("ibookso", session, servletContext, response)
        }
        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        def bannerList = redisService.("bannerList_"+session['siteId'])
        [commonTemplate:"true",bannerList:bannerList,title:"Compare Amazon and Flipkart price - iBookso - Never pay more"]
    }

    def contactus(){
        [title: 'Contact us', commonTemplate:"true"]
    }

    def deleteAccount(){
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
        [title: 'Delete Account request']
    }

    def cuetAcademics(){
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
        [title: 'Welcome to CUET Academics - Empowering Your Destiny through Knowledge']
    }

    @Transactional
    def loginPage(){
        if(params.scd!=null) {
            if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                session["scd"] = params.scd
                asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
            }
        }
       //site visitor counter
        def visitorCounter = redisService.("siteVisitor_" + session['siteId'])
        if (visitorCounter == null){
            KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("visitorCounter", session['siteId'])
            if(keyValueMst!=null) {
                visitorCounter = keyValueMst.keyValue
            }else{
                visitorCounter = "0"
            }
            redisService.("siteVisitor_" + session['siteId']) = visitorCounter
        }
        boolean hasLibraryAccess=false
        if("Yes".equals(""+session["ipAddressAccess"])) hasLibraryAccess = utilService.hasLibraryAccess(request,new Integer(""+session["siteId"]))
        boolean loggedIn = false
        if(springSecurityService.currentUser!=null) loggedIn = true
        [title: session["clientName"],showLibrary:hasLibraryAccess,loggedIn:loggedIn,visitorCounter:visitorCounter]
    }

    def gptsir(){
    }

    @Transactional @Secured(['ROLE_USER'])
    def accessCodeBooks(){

    }

    @Transactional @Secured(['ROLE_USER'])
    def getBooksForCampaign(){
        String accessCode = params.accessCode
        String campaignCode = params.campaignCode
        BooksCodeMst booksCodeMst = BooksCodeMst.findByCodeAndCampaignCodeAndSiteId(accessCode, campaignCode,new Integer(""+session["siteId"]))
        List booksList = new ArrayList()
        if(booksCodeMst!=null){
            String bookIds = KeyValueMst.findByKeyNameAndSiteId(campaignCode,new Integer(""+session["siteId"])).keyValue
            if(booksCodeMst!=null){
                //booksIds are the comma separated ids of the BooksMst table. Lets get the list of books
                String[] bookIdArray = bookIds.split(",")
                for(int i=0;i<bookIdArray.length;i++){
                    BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookIdArray[i]))
                    if(booksMst!=null)
                        booksList.add([id:booksMst.id,title:booksMst.title,coverImage:booksMst.coverImage])
                }
            }
        }
        def json = [
                'status': booksList.size()>0 ? "OK" : "Nothing present",
                'books': booksList
        ]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def addBookToUser(){
        String accessCode = params.accessCode
        String campaignCode = params.campaignCode
        String bookId = params.bookId
        BooksCodeMst booksCodeMst = BooksCodeMst.findByCodeAndCampaignCodeAndSiteId(accessCode, campaignCode,new Integer(""+session["siteId"]))
        String status = ""

        if(booksCodeMst!=null){
            String bookIds = KeyValueMst.findByKeyNameAndSiteId(campaignCode,new Integer(""+session["siteId"])).keyValue
            if(bookIds.contains(bookId)){
                Integer validityDays = new Integer(180)
                String username = springSecurityService.currentUser.username
                BooksPermission booksPermission = BooksPermission.findByBookCodeAndBookId(accessCode, new Long(bookId))
                if (booksPermission == null) {
                    BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
                    booksPermission = new BooksPermission()
                    booksPermission.bookId = new Integer(bookId)
                    booksPermission.bookCode = accessCode
                     booksPermission.username = username
                    booksPermission.bookType = booksMst.bookType
                    booksPermission.chatTokensBalance=50
                    Calendar c = Calendar.getInstance()
                    c.add(Calendar.DATE, validityDays)
                    booksPermission.expiryDate = c.getTime()

                    booksPermission.save(failOnError: true, flush: true)
                    wsLibraryService.userShelfBooks(username);
                    status = "allocated"
                    booksCodeMst.delete(flush: true)
                } else {
                    status = "This token is already used."
                }
            }

            }else{
                status = "Book not available for this access code"
            }

        def json = ['status': status]
        render json as JSON
        }

    @Transactional
    def updateVisitorCounter(){
        List sites = KeyValueMst.findAllByKeyName("visitorCounter")
        for(int i=0;i<sites.size();i++){
            //create a random number between 250-400
            int random = (int)(Math.random() * 150 + 250)
            //add this random number to the existing visitor counter
            int counter = Integer.parseInt(sites[i].keyValue)
            counter = counter + random
            sites[i].keyValue = ""+counter
            sites[i].save(flush: true,failOnError: true)
            redisService.("siteVisitor_" +sites[i].siteId) = ""+counter
        }
        def json = ['status': "OK"]
        render json as JSON
    }

    /**
     * Show form to add or edit a SiteMst record.
     * If "id" parameter is provided, we load that record for editing.
     * If no "id" parameter, we prepare to create a new record by:
     *  - Finding max ID from SiteMst and adding 1 to it
     *  - Loading the default values from the SiteMst record with id=1
     */
    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addSite() {
        def siteId = params.long('id')

        SiteMst baseRecord = SiteMst.get(1) // load base record
        if (!baseRecord) {
            // If no base record found, you may want to handle it differently
            flash.message = "Base record with id=1 not found. Please create it first."
            return
        }

        SiteMst siteInstance
        if (siteId) {
            // Editing scenario
            siteInstance = SiteMst.get(siteId)
            if (!siteInstance) {
                flash.message = "Site with id=${siteId} not found."
                return
            }
        } else {
            // Adding scenario
            // Get max id and add 1
            def maxId = SiteMst.executeQuery("select max(id) from SiteMst")[0] ?: 0
            def newId = maxId + 1

            // Create a new instance with defaults from baseRecord
            siteInstance = new SiteMst()
            siteInstance.id = newId   // This won't persist yet, just for display
            siteInstance.siteName = "" // empty for user input
            siteInstance.clientName = "" // empty for user input

            // Copy all other fields from baseRecord
            siteInstance.displayInMainSite = baseRecord.displayInMainSite
            siteInstance.categories = baseRecord.categories
            siteInstance.publisherId = baseRecord.publisherId
            siteInstance.displineCoverImage = baseRecord.displineCoverImage
            siteInstance.teachingNotes = baseRecord.teachingNotes
            siteInstance.teachingSlides = baseRecord.teachingSlides
            siteInstance.sampleChapters = baseRecord.sampleChapters
            siteInstance.mediaLinks = baseRecord.mediaLinks
            siteInstance.exercises = baseRecord.exercises
            siteInstance.otpReg = baseRecord.otpReg
            siteInstance.googleClientId = baseRecord.googleClientId
            siteInstance.fbMessageDatabaseUrl = baseRecord.fbMessageDatabaseUrl
            siteInstance.fbMessageKeyFile = baseRecord.fbMessageKeyFile
            siteInstance.fbFirebaseWebAPI = baseRecord.fbFirebaseWebAPI
            siteInstance.sendNotification = baseRecord.sendNotification
            siteInstance.googleApiKey = baseRecord.googleApiKey
            siteInstance.domainUriPrefix = baseRecord.domainUriPrefix
            siteInstance.iosBundleId = baseRecord.iosBundleId
            siteInstance.androidPackageName = baseRecord.androidPackageName
            siteInstance.channelId = baseRecord.channelId
            siteInstance.smsUsername = baseRecord.smsUsername
            siteInstance.smsPassword = baseRecord.smsPassword
            siteInstance.smsSenderId = baseRecord.smsSenderId
            siteInstance.smsSenderName = baseRecord.smsSenderName
            siteInstance.appOnly = baseRecord.appOnly
            siteInstance.smsUrl = baseRecord.smsUrl
            siteInstance.smsUrl1 = baseRecord.smsUrl1
            siteInstance.securityKey = baseRecord.securityKey
            siteInstance.smsResendUrl = baseRecord.smsResendUrl
            siteInstance.smsResendUrl1 = baseRecord.smsResendUrl1
            siteInstance.jwChannelId = baseRecord.jwChannelId
            siteInstance.jwAuthId = baseRecord.jwAuthId
            siteInstance.sageOnly = baseRecord.sageOnly
            siteInstance.appInApp = baseRecord.appInApp
            siteInstance.awsAccessKeyId = baseRecord.awsAccessKeyId
            siteInstance.awsSecretAccessKey = baseRecord.awsSecretAccessKey
            siteInstance.currentAffairsType = baseRecord.currentAffairsType
            siteInstance.prepjoySite = baseRecord.prepjoySite
            siteInstance.playStoreInstallUrl = baseRecord.playStoreInstallUrl
            siteInstance.allBooksLibrary = baseRecord.allBooksLibrary
            siteInstance.downloadBookChapters = baseRecord.downloadBookChapters
            siteInstance.instituteLibrary = baseRecord.instituteLibrary
            siteInstance.fromEmail = baseRecord.fromEmail
            siteInstance.mainResourcesPage = baseRecord.mainResourcesPage
            siteInstance.fixPayments = baseRecord.fixPayments
            siteInstance.commonWhiteLabel = baseRecord.commonWhiteLabel
            siteInstance.productYoutubeLink = baseRecord.productYoutubeLink
            siteInstance.youtubeLinkTitle = baseRecord.youtubeLinkTitle
            siteInstance.associatedSites = baseRecord.associatedSites
        }

       [siteInstance: siteInstance]
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def saveSite() {
        //print all params
        Long recordId = params.long('recordId')
        SiteMst siteInstance=null

        if (recordId) {
            // Editing existing record
            println("**** first entered here and the record id is "+recordId)
            siteInstance = SiteMst.findById(recordId)

        }
        if(siteInstance==null)
        {
            // Creating new record
            println("**** then entered here "+recordId)
            siteInstance = new SiteMst()
            siteInstance.id = recordId
         }

        // Populate fields from params
        // Only siteName, clientName, and id come from user input for now
        // Rest are from hidden fields

        siteInstance.siteName = params.siteName
        siteInstance.clientName = params.clientName
        siteInstance.displayInMainSite = params.displayInMainSite
        siteInstance.categories = params.categories
        siteInstance.publisherId = params.long('publisherId')
        siteInstance.displineCoverImage = params.displineCoverImage
        siteInstance.teachingNotes = params.teachingNotes
        siteInstance.teachingSlides = params.teachingSlides
        siteInstance.sampleChapters = params.sampleChapters
        siteInstance.mediaLinks = params.mediaLinks
        siteInstance.exercises = null
        siteInstance.otpReg = (params.otpReg == 'true')
        siteInstance.googleClientId = params.googleClientId
        siteInstance.fbMessageDatabaseUrl = params.fbMessageDatabaseUrl
        siteInstance.fbMessageKeyFile = params.fbMessageKeyFile
        siteInstance.fbFirebaseWebAPI = params.fbFirebaseWebAPI
        siteInstance.sendNotification = params.sendNotification
        siteInstance.googleApiKey = params.googleApiKey
        siteInstance.domainUriPrefix = params.domainUriPrefix
        siteInstance.iosBundleId = params.iosBundleId
        siteInstance.androidPackageName = params.androidPackageName
        siteInstance.razorPayKeyId = params.razorPayKeyId
        siteInstance.razorPaySecretKey = params.razorPaySecretKey
        siteInstance.channelId = params.channelId
        siteInstance.siteBaseUrl = params.siteBaseUrl
        siteInstance.smsUsername = params.smsUsername
        siteInstance.smsPassword = params.smsPassword
        siteInstance.smsSenderId = params.smsSenderId
        siteInstance.smsSenderName = params.smsSenderName
        siteInstance.appOnly = params.appOnly
        siteInstance.smsUrl = params.smsUrl
        siteInstance.smsUrl1 = params.smsUrl1
        siteInstance.securityKey = params.securityKey
        siteInstance.smsResendUrl = params.smsResendUrl
        siteInstance.smsResendUrl1 = params.smsResendUrl1
        siteInstance.googleUniversalAnalyticsKey = params.googleUniversalAnalyticsKey
        siteInstance.jwChannelId = params.jwChannelId
        siteInstance.jwAuthId = params.jwAuthId
        siteInstance.sageOnly = params.sageOnly
        siteInstance.appInApp = params.appInApp
        siteInstance.awsAccessKeyId = params.awsAccessKeyId
        siteInstance.awsSecretAccessKey = params.awsSecretAccessKey
        siteInstance.currentAffairsType = params.currentAffairsType
        siteInstance.prepjoySite = params.prepjoySite
        siteInstance.playStoreInstallUrl = params.playStoreInstallUrl
        siteInstance.allBooksLibrary = params.allBooksLibrary
        siteInstance.downloadBookChapters = params.downloadBookChapters
        siteInstance.instituteLibrary = params.instituteLibrary
        siteInstance.siteDomainName = params.siteDomainName
        siteInstance.fromEmail = params.fromEmail
        siteInstance.mainResourcesPage = params.mainResourcesPage
        siteInstance.fixPayments = params.fixPayments
        siteInstance.commonWhiteLabel = params.commonWhiteLabel
        siteInstance.sellPrintOnMainSite = params.sellPrintOnMainSite
        siteInstance.sellPrintOnWhiteLabel = params.sellPrintOnWhiteLabel
        siteInstance.productYoutubeLink = params.productYoutubeLink
        siteInstance.youtubeLinkTitle = params.youtubeLinkTitle
        siteInstance.associatedSites = params.associatedSites
        println("**** site id is "+siteInstance.id)
        siteInstance.save(flush: true,failOnError:true)
        if (!siteInstance.hasErrors()) {
            flash.message = "Record saved successfully (ID = ${siteInstance.id})."
            redirect(action: 'addSite', params: [id: siteInstance.id])
        } else {
            flash.message = "Error saving the record."
            render(view: 'addSite', model: [siteInstance: siteInstance])
        }
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def listSites() {
        String sortField = params.sort ?: 'clientName'
        String sortOrder = params.order ?: 'asc' // can be 'asc' or 'desc'

        // Query all SiteMst records with sorting
        def siteList = SiteMst.createCriteria().list {
            order(sortField, sortOrder)
        }

        [siteList: siteList, sort: sortField, order: sortOrder]
    }
}

