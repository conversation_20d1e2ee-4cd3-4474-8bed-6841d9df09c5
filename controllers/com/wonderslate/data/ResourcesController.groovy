package com.wonderslate.data

import com.ccavenue.security.AesCryptUtil
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.maxmind.geoip2.DatabaseReader
import com.wonderslate.DataNotificationService
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import com.wonderslate.shop.SubscriptionMst
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.apache.commons.io.FileUtils
import org.grails.web.util.WebUtils
import org.jsoup.Jsoup
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.w3c.dom.*

import javax.servlet.http.Cookie
import java.text.SimpleDateFormat

import org.springframework.web.multipart.MultipartFile
import org.apache.commons.io.IOUtils
import org.apache.commons.compress.archivers.zip.*
import javax.xml.parsers.DocumentBuilderFactory
import java.util.zip.CRC32
import org.xml.sax.InputSource

import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.io.FileOutputStream
import groovy.json.JsonBuilder
import groovy.json.JsonOutput

import java.util.zip.ZipOutputStream

class ResourcesController {
    UserManagementService userManagementService
    SpringSecurityService springSecurityService
    Document dom
    Vector<String> v
    def redisService
    DataProviderService dataProviderService
    UtilService utilService
    DataNotificationService dataNotificationService
    AsyncLogsService asyncLogsService
    FolderService folderService
    WsLibraryService wsLibraryService
    PrintBooksService printBooksService
    WsshopService wsshopService
    BookPriceService bookPriceService
    SiteManagerService siteManagerService
    MetainfoService metainfoService
    TestsService testsService
    ResourceCreatorService resourceCreatorService

    def index() {}

    @Transactional
    def resource(resId) {
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))

        if (resourceDtl != null && "Multiple Choice Questions".equals(resourceDtl.resType)) {
            redirect(controller: 'funlearn', action: 'quiz', params: ['resId': resourceDtl.id, 'quizMode': "practice", 'quizId': resourceDtl.resLink])
            return
        } else if ("KeyValues".equals(resourceDtl.resType)) {
            redirect(controller: 'resources', action: 'displayFlashCards', params: ['resId': resourceDtl.id, 'name': resourceDtl.resourceName])
            return
        } else if ("Notes".equals(resourceDtl.resType)) {
            redirect(controller: 'resources', action: 'notesViewer', params: ['resId': resourceDtl.id, 'name': resourceDtl.resourceName])
            return
        } else if ("Reference Videos".equals(resourceDtl.resType)) {
            redirect(uri: '/videos', params: ['resId': resourceDtl.id, 'resLink': resourceDtl.resLink, 'share': true])
            return
        } else {
            //no further logic right now. so redirecting to index
            redirect(controller: 'books', action: 'index')
            return
        }


    }

    @Transactional
    def ebook() {
        Integer siteId = utilService.getSiteId(request, session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if ("true".equals(siteMst.mainResourcesPage) || "books".equals(params.siteName) || "books".equals("" + session['entryController'])) {
            String bookId = params.bookId
            def chapterId = null
            boolean hasBookAccess = false
            def lastReadTopicId = null
            def previewMode = "true".equals(params.preview) ? true : false
            def resId = "-1"
            String publisher = null
            String publisherId = null
            def resType
            def link

            if ("true".equals(siteMst.commonWhiteLabel)) {
                userManagementService.setUserSession(params.siteName, session, servletContext, response)
            }

            if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }
            if (params.resId != null) {
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
                resId = params.resId
                if (resourceDtl != null && "Multiple Choice Questions".equals(resourceDtl.resType)) {
                    redirect(controller: 'funlearn', action: 'quiz', params: ['resId': resourceDtl.id, 'quizMode': "practice", 'quizId': resourceDtl.resLink])
                    return
                } else {

                    if (resourceDtl.chapterId != null) chapterId = "" + resourceDtl.chapterId

                    if (params.bookId == null && chapterId != null) {
                        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(chapterId))
                        bookId = "" + chaptersMst.bookId
                    } else {
                        resource(params.resId)
                        return
                    }
                }

            }

            boolean eBookVersionPurchased = false
            if(springSecurityService.currentUser!=null){
                BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(new Long(bookId),springSecurityService.currentUser.username)
                if(booksPermission!=null&&"eBook".equals(booksPermission.bookType)) eBookVersionPurchased = true
            }


            if (springSecurityService.currentUser == null || siteId.intValue()==80 || userManagementService.isValidSession(springSecurityService.currentUser.username, session.getId())) {

                BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
                if (("bookgpt".equals(booksMst.bookType) || "ebookwithai".equals(booksMst.bookType))&&siteId.intValue()!=80&&!eBookVersionPurchased) {
                    redirect(controller: 'prompt', action: 'bookgpt', params: [bookId: params.bookId,sessionKey:params.sessionKey])
                }else {
                    List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
                    boolean eBookPriceZero = false
                    Double upgradePrice = null
                    Double eBookPrice = null
                    bookPriceDtls.each { bookPrice ->
                        if ("eBook".equals(bookPrice.bookType)) {
                            if (bookPrice.sellPrice != null && bookPrice.sellPrice.doubleValue() == 0) eBookPriceZero = true
                            eBookPrice = bookPrice.sellPrice
                        } else if ("upgrade".equals(bookPrice.bookType)) {
                            upgradePrice = bookPrice.sellPrice
                        }
                    }
                    //now check and initialise category wise flags
                    if (params.chapterId != null) {

                    }
                    //check access

                    hasBookAccess = userManagementService.hasAccessToBook(new Long(bookId), session, request, true, response)
                    if (!hasBookAccess) {
                        hasBookAccess = userManagementService.hasLibraryAccessToBook(new Long(bookId), true)

                    }

                    if (!hasBookAccess) {
                        if (!eBookPriceZero && "published".equals(booksMst.status)) {
                            previewMode = true
                        } else {
                            if ("true".equals(siteMst.commonWhiteLabel)) {
                                redirect(uri: "/sp/${siteMst.siteName}/store")
                                return
                            } else {
                                redirect(controller: siteMst.siteName, action: 'index')
                                return
                            }

                        }
                    }

                    // get publisher information
                    if (booksMst != null && booksMst.publisherId != null) {
                        Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
                        if (publishers != null) {
                            publisher = publishers.name
                            publisherId = "" + publishers.id
                        }
                    }

                    //get and set default / preview chapter details
                    if (redisService.("chapters_" + booksMst.id) == null) {
                        dataProviderService.getChaptersList(booksMst.id)
                    }
                    def chaptersMst = new JsonSlurper().parseText(redisService.("chapters_" + booksMst.id))
                    if (params.chapterId != null && !"topic".equals(params.chapterId)) {
                        chapterId = params.chapterId
                    }
                    String instituteId = null
                    if(params.batchId!=null) {
                        instituteId = ""+dataProviderService.getCourseBatchesDtl(new Integer(params.batchId)).conductedBy
                    }
                    if (previewMode) {
                        chapterId = redisService.("previewchapter_" + booksMst.id)
                        asyncLogsService.updateBookView(params.bookId, "web", "preview", utilService.getSiteId(request, session),
                                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), instituteId)

                    } else {
                        asyncLogsService.updateBookView(params.bookId, "web", "library", utilService.getSiteId(request, session),
                                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), instituteId)
                    }
                    //if the user has full access to book, then make previewMode false, as it is reduntant.
                    if (hasBookAccess) previewMode = false

                    if (chapterId == null) {
                        if (chaptersMst.size() == 0) {
                            redirect(controller: session["entryController"], action: 'index')
                            return
                        } else chapterId = chaptersMst[0].id
                    }
                    if (springSecurityService.currentUser != null) {
                        userManagementService.updateChapterAccess(booksMst.id, new Long("" + chapterId))
                    }

                    //logic for adding the seo friendly title
                    String seoFriendlyTitle = booksMst.title
                    if (params.chapterId != null) {
                        ChaptersMst chaptersMst1 = dataProviderService.getChaptersMst(new Long(params.chapterId))
                        seoFriendlyTitle = booksMst.title + "-" + chaptersMst1.name
                    }
                    if (publisher != null) seoFriendlyTitle += " from " + publisher

                    if (previewMode) seoFriendlyTitle = "Free Pdf - " + seoFriendlyTitle
                    String seoDesc = seoFriendlyTitle
                    if (booksMst.description != null && !"".equals(booksMst.description)) {
                        org.jsoup.nodes.Document document = Jsoup.parse(booksMst.description as String);
                        seoDesc = document.text();
                    }

                    if (springSecurityService.currentUser != null) dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)

                    // Key for epub reader
                    String workingKey = "9E74748742AAB8342432FCF15E225793"
                    String encResp = "epub" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000, pdfResp = "pdf" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000
                    session.setAttribute('epubKey', encResp)
                    AesCryptUtil aesUtil = new AesCryptUtil(workingKey)
                    String encryptStr = aesUtil.encrypt(encResp)
                    String sessionEncKey = session.getAttribute('epubEncKey')
                    if (sessionEncKey != null && !sessionEncKey.isEmpty()) session.removeAttribute('epubEncKey')

                    boolean genericReader = false

                    if((booksMst.genericReader && "Yes".equals(booksMst.genericReader)) || "80".equals("" + session["siteId"])){
                        genericReader = true
                    }

                    session.setAttribute('pdfKey', pdfResp)
                    AesCryptUtil aesUtilPdf = new AesCryptUtil(workingKey)
                    String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)
                    String sessionEncPdfKey = session.getAttribute('pdfEncKey')
                    if (sessionEncPdfKey != null && !sessionEncPdfKey.isEmpty()) session.removeAttribute('pdfEncKey')
                    //new logic for free book. For non logged in user, show only preview thingy
                    if (springSecurityService.currentUser == null && eBookPriceZero) {
                        previewMode = true
                    }
                    if (previewMode && request.getRequestURL().indexOf("libwonder.com") > -1) {
                        String redirectUrl = ("" + request.getRequestURL()).replace("libwonder.com", "wonderslate.com") + "?" + request.queryString
                        redirect(url: redirectUrl)
                    } else {
                        Boolean boughtTestSeries = false
                        [topicId          : chapterId,
                         topicMst         : chaptersMst,
                         title            : seoFriendlyTitle,
                         keywords         : seoFriendlyTitle,
                         id               : params.id,
                         resType          : resType,
                         link             : link,
                         bookId           : params.bookId,
                         bookName         : booksMst.title,
                         lastReadTopicId  : lastReadTopicId == null ? "-1" : lastReadTopicId,
                         previewMode      : previewMode,
                         book             : booksMst,
                         apiKey           : siteMst.googleApiKey,
                         hasBookAccess    : hasBookAccess,
                         isBookPage       : true,
                         resId            : resId,
                         encryptEpubKey   : encryptStr,
                         encryptPdfKey    : encryptPdfStr,
                         publisher        : publisher,
                         publisherId      : publisherId,
                         hasQuiz          : booksMst.hasQuiz,
                         mainResourcesPage: siteMst.mainResourcesPage != null ? siteMst.mainResourcesPage : null,
                         commonTemplate   : "true",
                         freeBook         : eBookPriceZero ? "true" : "false",
                         bookPriceDtls    : bookPriceDtls,
                         upgradePrice     : upgradePrice,
                         eBookPrice       : eBookPrice,
                         genericReader    : genericReader,
                         seoDesc          : seoDesc
                        ]
                    }
                }

            } else {
                Cookie cookie = new Cookie("SimulError", "Fail")
                cookie.path = "/"
                WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
                redirect([uri: '/logoff'])
            }
        } else {
            if (params.instituteId != null && params.batchId != null)
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': params.bookId, 'siteName': params.siteName, 'instituteId': params.instituteId, 'batchId': params.batchId])
            else if (params.instituteId != null)
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': params.bookId, 'siteName': params.siteName, 'instituteId': params.instituteId])
            else if (params.batchId != null)
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': params.bookId, 'siteName': params.siteName, 'batchId': params.batchId])
            else
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': params.bookId, 'siteName': params.siteName])


        }
    }


    @Transactional

    def eBookDtl() {

        String bookId = params.bookId
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        boolean eBook = true
        Double eBookPrice = null
        Double eBookListPrice = null

        if (bookId == null) {
            if (params.pBookId != null) {
                redirect(controller: "printbooks", action: 'index')
                return

            } else if (params.isbn != null) {
                BooksMst booksMst = dataProviderService.getBooksMstByIsbn(params.isbn)
                if (booksMst != null) {
                    bookId = "" + booksMst.id

                } else {
                    redirect(controller: siteMst.siteName, action: 'index')
                    return
                }
            }else if (params.bookCode != null) {
                BooksMst booksMst = dataProviderService.getBooksMstByBookCode(params.bookCode)
                if (booksMst != null) {
                    bookId = "" + booksMst.id

                } else {
                    redirect(uri: "/sp/${siteMst.siteName}/store")
                    return
                }
            } else {
                redirect(controller: siteMst.siteName, action: 'index')
                return
            }
        }
        if (eBook) {
            boolean sellPrintBook = false
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
            if (booksMst.siteId.intValue() == siteMst.id.intValue()) {
                //same site
                if ("Yes".equals(siteMst.sellPrintOnWhiteLabel)) {
                    sellPrintBook = true
                }
            } else {
                //
                SiteMst bookSiteMst = dataProviderService.getSiteMst(booksMst.siteId)
                if ("Yes".equals(bookSiteMst.sellPrintOnMainSite)) {
                    sellPrintBook = true
                }
            }
            if (!"published".equals(booksMst.status)) {
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(booksMst.id)
                if (booksTagDtl != null) {
                    redirect(uri: "/ebooks?level=" + booksTagDtl.level.replaceAll(' ', '-'))
                } else {
                    redirect(uri: "/ebooks")
                }
            } else {
                boolean excludeCheck = false
                if (siteMst != null && (siteMst.id.intValue() == 1 || siteMst.prepjoySite == "true")) {
                    String url = request.getRequestURL()
                    if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1) {
                        excludeCheck = true
                    }
                }

                if (excludeCheck || siteMst.id.intValue() == booksMst.siteId.intValue()|| (siteMst.associatedSites!=null&&siteMst.associatedSites.indexOf(""+booksMst.siteId)>-1)) {
                    if (session['entryController'] == 'privatelabel') {
                        if(!"true".equals(""+session["userSessionSet"])) userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
                    } else if (session['entryController'] == null && params.siteName != null) {
                        if ("true".equals(siteMst.commonWhiteLabel)) {
                            if(!"true".equals(""+session["userSessionSet"])) userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
                        } else {
                            if(!"true".equals(""+session["userSessionSet"])) userManagementService.setEntrySession(siteMst.siteName, session, servletContext)
                        }
                    }

                    if (redisService.("bookResources_" + bookId) == null) {
                        dataProviderService.getBookResources(bookId)
                    }

                    if (redisService.("totalBookMcqCount_" + bookId) == null) {
                        dataProviderService.totalBookMcqCount(bookId)
                    }

                    String chapters = dataProviderService.getChaptersList(new Long(bookId))
                    BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(bookId))

                    boolean inLibrary = false
                    asyncLogsService.updateBookView(bookId, "web", "store", utilService.getSiteId(request, session),
                            (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), null)

                    def publisherName = null
                    boolean canSell = true
                    def publisherNameForTitle=""
                    if (booksMst != null && booksMst.publisherId != null) {

                        Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
                        if (publishers != null) {
                            if(publishers.publisherNameForTitle!=null) {
                                if(booksMst.title.indexOf(publishers.publisherNameForTitle)==-1)
                                publisherNameForTitle = publishers.publisherNameForTitle
                                else publisherNameForTitle=""
                            }
                            else {
                                if(publishers!=null&&publishers.name!=null) {
                                    String tempPubName = publishers.name.split(' ')[0]
                                    if (booksMst.title.indexOf(tempPubName) == -1) publisherNameForTitle = tempPubName
                                }
                            }
                            publisherName = publishers.name
                            if (publishers.sellOnlyInCountry != null && !"".equals(publishers.sellOnlyInCountry)) {
                                canSell = canSellTheBook("" + publishers.sellOnlyInCountry)
                            }
                        }
                    }
                    //logic for adding the seo friendly title
                    String seoFriendlyTitle = booksMst.title

                    String seoDesc = null
                    if (booksMst.description != null) {
                        org.jsoup.nodes.Document document = Jsoup.parse(booksMst.description as String);
                        seoDesc = document.text();

                    };


                    if (publisherNameForTitle != null) seoFriendlyTitle = publisherNameForTitle  + " "+seoFriendlyTitle

                    SubscriptionMst subscriptionMst = null
                    List subsReleasedVersions = null
                    if (booksMst.subscriptionId != null && !"".equals(booksMst.subscriptionId)) {
                        subscriptionMst = SubscriptionMst.findById(new Integer(booksMst.subscriptionId))

                        //logic to get all the old subscriptions
                        subsReleasedVersions = wsshopService.getSubscriptionPublishedVersions(booksMst.subscriptionId)

                    }

                    if (redisService.("bookPriceDetails_" + booksMst.id) == null) bookPriceService.getBookPrices(new Integer("" + booksMst.id))

                    List bookPriceDtls = new JsonSlurper().parseText(redisService.("bookPriceDetails_" + booksMst.id));
                    bookPriceDtls.each { bookPrice ->
                        if ("eBook".equals(bookPrice.bookType)) {
                            eBookPrice = bookPrice.sellPrice
                            eBookListPrice = bookPrice.listPrice
                        }
                    }
                    String bookExpiry = null
                    if (booksMst.bookExpiry != null && !"".equals(booksMst.bookExpiry)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                        bookExpiry = sdf.format(booksMst.bookExpiry);
                    }
                    boolean showPrice = true
                    def wileyCollectionBookId = null
                    def wileyCollectionBookTitle = null
                    HashMap wileyCollectionBooks = null

                    if(session["wileySite"]){
                        if(redisService.("wileyCollectionBook")==null) wsshopService.getWileyCollectionMainBook()
                        wileyCollectionBookId = redisService.("wileyCollectionBook")
                        wileyCollectionBookTitle = redisService.("wileyCollectionBookTitle")
                        if(booksMst.packageBookIds==null) showPrice=false
                        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
                        params.put("wileyCollectionBooks","true")
                        wileyCollectionBooks = wsshopService.getBooksList(params,new Integer(""+session["siteId"]),(new Integer(0)).intValue())
                    }
                    String affiliationUrl=""
                    if(params.scd!=null) {
                        if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                            session["scd"] = params.scd
                            asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
                        }
                    }
                   if(session["userdetails"]!=null&&session["userdetails"].affliationCd!=null) {
                      String tempBookTitle = (""+booksMst.title).replaceAll(' ', '-').replaceAll("--","-").replaceAll("\\.",'').replaceAll('#','').replaceAll('%','').replaceAll('/','-').toLowerCase()
                        affiliationUrl=""+request.getRequestURL()+"?scd="+session["userdetails"].affliationCd+"&bookId="+booksMst.id+"&siteName="+siteMst.siteName
                    }

                     [bookResources       : redisService.("bookResources_" + bookId), totalMcqs: redisService.("totalBookMcqCount_" + bookId),
                     chapters            : chapters, booksMst: booksMst, inLibrary: inLibrary, booksTagDtl: booksTagDtl, "title": booksMst.title, commonTemplate: "true",
                     title               : seoFriendlyTitle, clientName: siteMst.clientName, publisherName: publisherName, validityDays: booksMst.validityDays, seoDesc: seoDesc,
                     mainResourcesPage   : siteMst.mainResourcesPage != null ? siteMst.mainResourcesPage : null, eBook: "true", subscriptionMst: subscriptionMst,
                     subsReleasedVersions: subsReleasedVersions, bookPriceDtls: bookPriceDtls, eBookPrice: eBookPrice,eBookListPrice:eBookListPrice, sellPrintBook: sellPrintBook, canSell: canSell,
                     bookExpiry: bookExpiry,wileyCollectionBookId:wileyCollectionBookId,showPrice:showPrice,wileyCollectionBookTitle:wileyCollectionBookTitle,wileyCollectionBooks:wileyCollectionBooks,
                      publisherNameForTitle:publisherNameForTitle,bookType:booksMst.bookType,affiliationUrl:affiliationUrl,testTypeBook: booksMst.testTypeBook]
                } else {
                    redirect(url: "https://www.wonderslate.com")
                }
            }
        } else {
            redirect(url: "https://www.wonderslate.com")
        }
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def eBookDtlForApp() {

        if (redisService.("bookResources_" + params.bookId) == null) {
            dataProviderService.getBookResources(params.bookId)
        }
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId))
        }

        if (redisService.("totalBookMcqCount_" + params.bookId) == null) {
            dataProviderService.totalBookMcqCount(params.bookId)
        }
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        boolean sellPrintBook = false
        if (booksMst.siteId.intValue() == siteMst.id.intValue()) {
            //same site
            if ("Yes".equals(siteMst.sellPrintOnWhiteLabel)) {
                sellPrintBook = true
            }
        } else {
            //
            SiteMst bookSiteMst = dataProviderService.getSiteMst(booksMst.siteId)
            if ("Yes".equals(bookSiteMst.sellPrintOnMainSite)) {
                sellPrintBook = true
            }
        }
        boolean canSell = true
        def publisherName = null
        if (booksMst != null && booksMst.publisherId != null) {

            Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
            if (publishers != null) {
                publisherName = publishers.name
                if (publishers.sellOnlyInCountry != null && !"".equals(publishers.sellOnlyInCountry)) {
                    canSell = canSellTheBook("" + publishers.sellOnlyInCountry)
                }
            }
        }
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(params.bookId))
        boolean inLibrary = false
        asyncLogsService.updateBookView(params.bookId, "android", "store", utilService.getSiteId(request, session),
                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), null)

        Double upgradePrice = null
        Double eBookPrice = null
        Double eBookListPrice = null
        Double testSeriesPrice = null
        Double testSeriesListPrice = null
        List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
        bookPriceDtls.each { bookPrice ->
            if("bookGPT".equals(bookPrice.bookType)) {
                eBookPrice = bookPrice.sellPrice
                eBookListPrice = bookPrice.listPrice
            }
            else if ("eBook".equals(bookPrice.bookType)) {
                eBookPrice = bookPrice.sellPrice
                eBookListPrice = bookPrice.listPrice
            } else if ("upgrade".equals(bookPrice.bookType)) {
                upgradePrice = bookPrice.sellPrice
            } else if ("testSeries".equals(bookPrice.bookType)) {
                testSeriesPrice = bookPrice.sellPrice
                testSeriesListPrice = bookPrice.listPrice
            }
        }

        List bookPrices = bookPriceDtls.collect { bookPrice ->
            return [id: bookPrice.id, sellPrice: bookPrice, listPrice: bookPrice.listPrice, bookType: bookPrice.bookType]
        }
        String bookExpiry = null
        if (booksMst.bookExpiry != null && !"".equals(booksMst.bookExpiry)) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            bookExpiry = sdf.format(booksMst.bookExpiry);
        }
        println("book Expiry is " + bookExpiry)
        def json = [bookResources: redisService.("bookResources_" + params.bookId), chapters: redisService.("chapters_" + params.bookId), totalMcqs: redisService.("totalBookMcqCount_" + params.bookId),
                    inLibrary    : inLibrary,
                    'bookDesc'   : booksMst != null ? booksMst.description : null, 'price': eBookPrice, 'rating': null,
                    'isbn'       : booksMst != null ? booksMst.isbn : null, 'coverImage': booksMst != null ? booksMst.coverImage : null,
                    'bookId'     : booksMst.id, 'title': booksMst.title, 'listPrice': eBookListPrice, 'testTypeBook': booksMst.testTypeBook, 'bookLangauge': booksMst.language,
                    buylink1     : booksMst.buylink1, buylink2: booksMst.buylink2, rating1: booksMst.reviewLink1, rating2: booksMst.reviewLink2, status: booksMst.status, showDiscount: booksMst.showDiscount, bookType: booksMst.bookType != null ? booksMst.bookType : null, publisherName: publisherName,
                    AuthorName   : booksMst.authors, validityDays: booksMst.validityDays, bookCode: booksMst.bookCode, hasQuiz: booksMst.hasQuiz != null ? booksMst.hasQuiz : "",
                    testsPrice   : testSeriesPrice, testsListprice: testSeriesListPrice, upgradePrice: upgradePrice, bookPriceDtls: bookPrices, sellPrintBook: sellPrintBook,
                    currentStock : booksMst != null ? booksMst.currentStock : null, canSell: canSell, bookExpiry: bookExpiry]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def addKeyValues() {
        def resId
        def keyValueId = -1
        ResourceDtl resourceDtlInstance

        if ("-1".equals(params.studySetResId)) {

            //create the resource dtl first
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "KeyValues"
            if (!"-1".equals(params.chapterId)) resourceDtlInstance.chapterId = new Integer(params.chapterId)
            resourceDtlInstance.resourceName = params.title
            resourceDtlInstance.resLink = "blank"
            if (params.siteId!=null && params.siteId!=""){
                resourceDtlInstance.siteId = new Integer(params.siteId)
            }else{
                resourceDtlInstance.siteId = utilService.getSiteId(request, session)
            }
            resourceDtlInstance.description = params.description
            //do not set anything for sharing if the revision is added by the publisher
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if (user.authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR" })
                resourceDtlInstance.sharing = null
            else
                resourceDtlInstance.sharing = "createdbyuser"
            if (user.authorities.any { it.authority == "ROLE_PUBLISHER" })
                resourceDtlInstance.privacyLevel = 'public'
            resourceDtlInstance.save(flush: true, failOnError: true)
            if (resourceDtlInstance.sharing == null && !"-1".equals(params.studySetResId)) {
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)


            } else {
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
            }
            resId = resourceDtlInstance.id
            if (params.folderId != null && !"".equals(params.folderId)) folderService.addResourceToFolder(params.folderId, "" + resId)
        } else {
            resourceDtlInstance = dataProviderService.getResourceDtl(new Long(params.studySetResId))
            resId = resourceDtlInstance.id

        }

        int noOfItems = Integer.parseInt(params.noOfItems)
        for (int i = 0; i < noOfItems; i++) {
            KeyValues keyValues = new KeyValues(term: params["term" + i], definition: params["definition" + i], resId: resId, status: "active")
            keyValues.save(flush: true, failOnError: true)
            keyValueId = keyValues.id
        }
        if (resourceDtlInstance.sharing == null) {
            dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
            if (resourceDtlInstance.chapterId != null) {
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
            }

        } else {
            dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
            dataProviderService.getUserResources("KeyValues", springSecurityService.currentUser.username)
            dataProviderService.getUserResources("all", springSecurityService.currentUser.username)

        }
        dataProviderService.getLatestFlashCards()
        def json = ["resId": resId, "keyValueId": keyValueId, title: params.title]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def updateFlashCard() {
        String definitionvalue = params.definition
        String definition = definitionvalue.replaceAll("perc", "%")
        KeyValues keyValues = KeyValues.findById(new Long(params.keyValueId))
        keyValues.term = params.term
        keyValues.definition = definition
        keyValues.save(flush: true, failOnError: true)
        ResourceDtl dtl = ResourceDtl.findById(keyValues.resId)
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if (dtl.privacyLevel == 'rejected') {
            dtl.privacyLevel = null
            dtl.save()
        }
        if (dtl.privacyLevel == 'public' && !user.authorities.any { it.authority == "ROLE_PUBLISHER" }) {
            dtl.privacyLevel = 'awaiting_approval'
        }
        def json = ["status": "updated"]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def updateFlashCardTitle() {
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        resourceDtl.resourceName = params.title
        resourceDtl.description = params.description
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if (resourceDtl.privacyLevel == 'rejected') {
            resourceDtl.privacyLevel = null
        }
        if (resourceDtl.privacyLevel == 'public' && !user.authorities.any { it.authority == "ROLE_PUBLISHER" }) {
            resourceDtl.privacyLevel = 'awaiting_approval'
        }
        resourceDtl.save(flush: true, failOnError: true)
        def json = ["status": "updated"]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def deleteFlashCard() {
        KeyValues keyValues = KeyValues.findById(new Long(params.keyValueId))
        keyValues.status = "deleted"
        keyValues.save(flush: true, failOnError: true)
        def json = ["status": "deleted"]
        render json as JSON
    }

    @Transactional

    def getUserFlashCards() {
        // starting as the db read now. Next step is to move them cache with pagination.
        def results
        int pageNo = 0
        String sql
        String resType = "KeyValues" //just in case old version of the api is getting used
        Integer siteId = getSiteId(request)
        if (params.resType != null) resType = params.resType
        if (params.pageNo != null) pageNo = Integer.parseInt(params.pageNo)
        if (springSecurityService.currentUser != null) {
            String username = springSecurityService.currentUser.username
            if (redisService.(username + "_userResources_" + resType.replaceAll("\\s", "")) == null) dataProviderService.getUserResources(resType, username)
            results = redisService.(username + "_userResources_" + resType.replaceAll("\\s", ""))
        }
        if (redisService.("latestResources_" + resType.replaceAll("\\s", "") + "_List_" + pageNo+"_"+siteId) == null) dataProviderService.getLatestResources(pageNo, resType, siteId)
        def json = ['flashCardSets': results, 'latestFlashCards': redisService.("latestResources_" + resType.replaceAll("\\s", "") + "_List_" + pageNo+"_"+siteId)]
        render json as JSON

    }

    @Transactional
    def deleteFlashCards() {
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        String resType = resourceDtl.resType
        if (resourceDtl != null && springSecurityService.currentUser != null && resourceDtl.createdBy.equals(springSecurityService.currentUser.username)) {
            if ("KeyValues".equals(resourceDtl.resType))
                KeyValues.executeUpdate("delete from KeyValues where resId=" + params.resId)

            resourceDtl.delete(flush: true, failOnError: true)
            dataProviderService.getUserResources(resType, springSecurityService.currentUser.username)
            dataProviderService.getUserResources("all", springSecurityService.currentUser.username)
            if (resourceDtl.chapterId) {
                dataProviderService.getChapterDefaultResourcesAsString(resourceDtl.chapterId);
            }
        }
        if (params.folderId != null) {
            dataProviderService.getFolderContents(params.folderId)
        }
        def json = ["status": "deleted"]
        render json as JSON
    }

    @Transactional
    def displayFlashCards() {
        if (params.tokenId != null) {
            if (springSecurityService.currentUser == null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if (authenticationToken != null) {
                    springSecurityService.reauthenticate(authenticationToken.username)
                }
            }
            session.setAttribute("appType", params.appType)
        }
        if (params.name != null) {
            ["title": params.name + " - Flash cards - Wonderslate"]
        }
        ["title": "FlashCards", commonTemplate: "true"]
    }

    @Transactional
    def createFlashCards() {
        if (params.tokenId != null) {
            if (springSecurityService.currentUser == null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if (authenticationToken != null) {
                    springSecurityService.reauthenticate(authenticationToken.username)
                }
            }
            session.setAttribute("appType", params.appType)


        }
    }

    @Transactional
    def flashCardHome() {
        String resIds = null
        def userFolders = null
        if (params.tokenId != null) {
            if (springSecurityService.currentUser == null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if (authenticationToken != null) {
                    springSecurityService.reauthenticate(authenticationToken.username)
                }
            }

            session.setAttribute("appType", params.appType)
        }
        if (springSecurityService.currentUser != null && springSecurityService.currentUser.username != null) {
            if (redisService.("folders_" + springSecurityService.currentUser.username) == null) {
                dataProviderService.getUserFolders(springSecurityService.currentUser.username)
            }
            userFolders = redisService.("folders_" + springSecurityService.currentUser.username)
            if (redisService.(springSecurityService.currentUser.username + "_favResIds") == null) {
                dataProviderService.getUserFavResIds()
            }
            resIds = redisService.(springSecurityService.currentUser.username + "_favResIds")
        }

        String resourceType = "Flash Cards";
        if ("Multiple Choice Questions".equals(params.resType)) resourceType = "MCQs / Tests"
        else if ("Reference Videos".equals(params.resType)) resourceType = "Videos"
        else if ("Reference Web Links".equals(params.resType)) resourceType = "Reference Links"
        else if ("all".equals(params.resType)) resourceType = "My Materials"
        else if ("Notes".equals(params.resType)) resourceType = "Notes"


        [userFolders: userFolders, resIds: resIds, "title": resourceType + " - Wonderslate", resourceType: resourceType, forceLogin: "true", commonTemplate: "true"]
    }

    @Transactional
    def setPrivacyStatus() {
        def resId = params.resId
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))

        //only owner can set the status
        if (resourceDtl != null && springSecurityService.currentUser != null && resourceDtl.createdBy.equals(springSecurityService.currentUser.username)) {
            resourceDtl.privacyLevel = "public"
            resourceDtl.save(flush: true, failOnError: true)
        }
        dataProviderService.getLatestFlashCards()
        def json = [status: "ok"]
        render json as JSON
    }

    @Transactional
    def setPrivacyStatusToAwaitingApproval() {
        def resId = params.resId
        def sm = SiteMst.findById(getSiteId(request))
        Integer siteId = getSiteId(request)
        def clientName = grailsApplication.config.grails.appServer.default == "eutkarsh" ? grailsApplication.config.grails.appServer.siteName : sm.clientName
        def siteName = grailsApplication.config.grails.appServer.default == "eutkarsh" ? grailsApplication.config.grails.appServer.default : sm.siteName
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))
        if (resourceDtl != null && springSecurityService.currentUser != null && resourceDtl.createdBy.equals(springSecurityService.currentUser.username)) {
            resourceDtl.privacyLevel = "awaiting_approval"
            resourceDtl.save(flush: true, failOnError: true)
            User user = User.findByUsername(resourceDtl.createdBy)
            if (user.email) {
                def resType = ""
                def resLink = ""
                if (resourceDtl.resType == 'KeyValues') {
                    resType = "Flash Card"
                    resLink = "/resources/displayFlashCards?resId=" + resourceDtl.id + "&name=" + resourceDtl.resourceName
                } else if (resourceDtl.resType == 'Notes') {
                    resType = "Notes"
                    resLink = "/resources/notesViewer?resId=" + resourceDtl.id + "&name=" + resourceDtl.resourceName
                } else if (resourceDtl.resType == 'Multiple Choice Questions') {
                    resType = "Multiple Choice Questions"
                    resLink = "/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + resourceDtl.id + "&name=" + resourceDtl.resourceName
                } else if (resourceDtl.resType == 'Reference Videos') {
                    resType = "Reference Video"
                    resLink = "/videos?fromEmail=true&resId=" + resourceDtl.id + "&name=" + resourceDtl.resourceName + "&resLink=" + resourceDtl.resLink
                } else if (resourceDtl.resType == 'Reference Web Links') {
                    resType = "Reference Web Link"
                    resLink = resourceDtl.resLink
                }
                userManagementService.sendContentModerationEmail(user.email, user.name, "Your " + resType + " is Awaiting Approval.", "" + resType + " Awaiting Approval", siteName, clientName, resLink, resType)
            }
            dataProviderService.getLatestFlashCards();
            dataProviderService.getLatestResources('all', siteId)
            dataProviderService.getLatestResources(resourceDtl.resType, siteId)
            if (user.username != null) {
                String username = user.username
                dataProviderService.getUserResources('all', username)
                dataProviderService.getUserResources(resourceDtl.resType, username)
            }
        }
        def json = [status: "ok"]
        render json as JSON
    }

    @Transactional
    def contactus() {
        ["title": "Contact Us - Wonderslate", commonTemplate: "true"]
    }

    def Integer getSiteId(request) {
        Integer siteId = new Integer(1);
        if (session["siteId"] != null) {
            siteId = (Integer) session["siteId"]
        } else {
            def jsonObj = request.JSON
            if (jsonObj.siteId != null) siteId = new Integer(jsonObj.siteId);
            else if (params.siteId != null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    @Transactional
    def setUserSession() {
        if (springSecurityService.currentUser != null) {
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }
        }
        if ("true".equals(grailsApplication.config.grails.appServer.main) || "books".equals(grailsApplication.config.grails.appServer.default)) {
            session['siteId'] = new Integer(1);
            session.setAttribute("entryController", "books")
            session.setAttribute("siteName", "Wonderslate")
            session.setAttribute("loginType", "email,mobile")
            session.setAttribute("siteNameUrl", "books")
            Cookie cookie = new Cookie("wlSiteName", "")
            cookie.path = "/"
            cookie.maxAge = 0
            response.addCookie(cookie)
            cookie = new Cookie("siteName", "books")
            cookie.path = "/"
            response.addCookie(cookie)
        }
        return
    }

    @Transactional
    def pdfReader() {
        String serverURL = request.getScheme() + "://" + request.getServerName() +
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId))
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        boolean showPdf = true
        if("false".equals(booksMst.showPdf)){
            showPdf = false
        }
        ['serverURL': serverURL,genericReader :  booksMst.genericReader||"80".equals(""+session["siteId"])?true:false,topicMst:new JsonSlurper().parseText(redisService.("chapters_" + params.bookId)),
        title:booksMst.title,showPdf:showPdf]
    }

    def mobilePdfReader() {
        String serverURL = request.getScheme() + "://" + request.getServerName() +
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        ['serverURL': serverURL]
    }

    def notesViewer() {
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        [resLink: resourceDtl.resLink, resourceName: resourceDtl.resourceName]
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def createPdfBook() {

    }

    @Transactional
    def displayPdfMaterial() {
        if (params.tokenId != null) {
//           if(springSecurityService.currentUser==null) {
            String tokenId = params.tokenId
            AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
            if (authenticationToken != null) {
                springSecurityService.reauthenticate(authenticationToken.username)
            }
//           }
            session.setAttribute("appType", params.appType)
        }
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        String workingKey = "9E74748742AAB8342432FCF15E225793"

        String pdfResp = "pdf" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000

        AesCryptUtil aesUtilPdf = new AesCryptUtil(workingKey)
        String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)

        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))

        [pdfLink: resourceDtl.resLink, allowDownload: resourceDtl.videoPlayer, id: resourceDtl.id, encryptPdfKey: encryptPdfStr, bookLang: booksMst.language, zoomLevel: resourceDtl.zoomLevel]

    }


    @Secured(['ROLE_USER'])
    @Transactional
    def eBookDtlForAdmin() {
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId))
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(params.bookId))
        def json = [chapters  : redisService.("chapters_" + params.bookId),
                    'bookDesc': booksMst != null ? booksMst.description : null, 'price': booksMst != null ? booksMst.price : null, 'rating': null,
                    'isbn'    : booksMst != null ? booksMst.isbn : null, 'coverImage': booksMst != null ? booksMst.coverImage : null,
                    'bookId'  : booksMst.id, 'title': booksMst.title, 'listPrice': booksMst.listprice, 'testTypeBook': booksMst.testTypeBook, 'bookLangauge': booksMst.language,
                    buylink1  : booksMst.buylink1, buylink2: booksMst.buylink2, rating1: booksMst.reviewLink1, rating2: booksMst.reviewLink2, status: booksMst.status, showDiscount: booksMst.showDiscount, bookType: booksMst.bookType != null ? booksMst.bookType : null,
                    AuthorName: booksMst.authors, validityDays: booksMst.validityDays, bookCode: booksMst.bookCode, level: booksTagDtl.level, syllabus: booksTagDtl.syllabus, grade: booksTagDtl.grade, subject: booksTagDtl.subject,
                    testsPrice: booksMst.testsPrice, testsListprice: booksMst.testsListprice, upgradePrice: booksMst.upgradePrice]
        render json as JSON
    }

    boolean canSellTheBook(String countryCode) {
        def userIP = request.getHeader('X-Forwarded-For')
        if (userIP) {
            // The X-Forwarded-For header contains the client's IP address
            // If multiple IP addresses are present (proxies), the first one is considered the client's IP
            userIP = userIP.split(',')[0].trim()
        } else {
            // If X-Forwarded-For header is not available, fallback to the remoteAddr
            userIP = request.remoteAddr
        }

        //check of localhost case
        if ("0:0:0:0:0:0:0:1".equals(userIP)) {
            return true
        } else {
            // Step 3: Fetch the country information based on the IP address
            def database = new File("upload/GeoLite2-Country.mmdb")
            // Provide the path to the MaxMind GeoIP2 database file
            def databaseReader = new DatabaseReader.Builder(database).build()

            def country
            try {
                def response = databaseReader.tryCountry(InetAddress.getByName(userIP))
                if (response.isPresent()) {
                    country = response.get().getCountry()
                }
            } catch (Exception e) {
                log.error("Error retrieving country information: $e.message")
                // Handle the error appropriately
            }

            // Step 4: Compare the country code

            if (countryCode.equals(country?.isoCode)) {
                return true
            } else {
                return false
            }

        }
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def deleteBookChapters() {
        String status = "No chapter deleted. Check the book id or the chapterIds provided"
        Long bookId = null
        if (params.bookId != null && !"".equals(params.bookId)) {
            List chapters = ChaptersMst.findAllByBookId(new Integer(params.bookId))
            chapters.each { chaptersMst ->
                bookId = chaptersMst.bookId
                chaptersMst.bookId = new Long(bookId.intValue() * -1)
                chaptersMst.save(flush: true, failOnError: true)
            }
            status = "Total of ${chapters.size()} deleted from the bookId=" + params.bookId


        } else if (params.chapterIds != null && !"".equals(params.chapterIds)) {
            String[] chapters = params.chapterIds.split(',')
            for (int i = 0; i < chapters.length; i++) {
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Integer(chapters[i]))
                bookId = chaptersMst.bookId
                chaptersMst.bookId = new Long(bookId.intValue() * -1)
                chaptersMst.save(flush: true, failOnError: true)
            }
            status = "Total of ${chapters.length} deleted from the chapterIds=" + params.chapterIds
        }
        if (bookId != null) {
            dataProviderService.getChaptersList(bookId)
            metainfoService.getAllChaptersMetaInfo(bookId)
        }
        def json = [status: status]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    def deleteChapters(){

    }
    @Transactional
    def previewChapter(){
        if (params.bookId != null && !"".equals(params.bookId)) {
            SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
            if ("true".equals(siteMst.commonWhiteLabel)){
                userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
            }
            Long previewChapterId
            BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
            boolean excludeCheck = false
            if (siteMst != null && (siteMst.id.intValue() == 1 || siteMst.prepjoySite == "true")) {
                String url = request.getRequestURL()
                if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1) {
                    excludeCheck = true
                }
            }

            if (excludeCheck || siteMst.id.intValue() == booksMst.siteId.intValue()) {
                if ("bookgpt".equals(booksMst.bookType) || "ebookwithai".equals(booksMst.bookType)) {
                    redirect(action: 'eBookDtl', params: [bookId: params.bookId])
                } else {
                    Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
                    if (redisService.("chapters_" + booksMst.id) == null) {
                        dataProviderService.getChaptersList(booksMst.id)
                    }
                    List chapters = ChaptersMst.findAllByBookId(new Integer(params.bookId))
                    previewChapterId = new Long(redisService.("previewchapter_" + booksMst.id) != null ? redisService.("previewchapter_" + booksMst.id) : "0")
                    List resources = ResourceDtl.findAllByChapterId(previewChapterId)
                    List resourcesList = resources.collect { resource ->
                        return [id          : resource.id, resType: resource.resType, resLink: resource.resLink != null ? resource.resLink.replace("http://", "").replace("https://", "") : "", filename: resource.filename,
                                resourceName: resource.resourceName, examSyllabus: resource.examSyllabus, description: resource.description != null ? resource.description.replace(':', ' ').replace(',', ' ') : "", sharing: resource.sharing]
                    }
                    String workingKey = "9E74748742AAB8342432FCF15E225793"
                    String encResp = "epub" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000, pdfResp = "pdf" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000
                    session.setAttribute('epubKey', encResp)
                    AesCryptUtil aesUtil = new AesCryptUtil(workingKey)
                    String encryptStr = aesUtil.encrypt(encResp)
                    String sessionEncKey = session.getAttribute('epubEncKey')
                    if (sessionEncKey != null && !sessionEncKey.isEmpty()) session.removeAttribute('epubEncKey')

                    session.setAttribute('pdfKey', pdfResp)
                    AesCryptUtil aesUtilPdf = new AesCryptUtil(workingKey)
                    String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)
                    String sessionEncPdfKey = session.getAttribute('pdfEncKey')
                    if (sessionEncPdfKey != null && !sessionEncPdfKey.isEmpty()) session.removeAttribute('pdfEncKey')
                    def resList = resourcesList as JSON
                    String seoDesc
                    if (booksMst.description != null && !"".equals(booksMst.description)) {
                        org.jsoup.nodes.Document document = Jsoup.parse(booksMst.description as String);
                        seoDesc = document.text();
                    } else {
                        seoDesc = booksMst.title + " by " + publishers.name
                    }
                    String keywords = booksMst.title + "," + publishers.name + " books"
                    BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(booksMst.id)
                    if (redisService.("mockTestExamGroup_" + booksTagDtl.syllabus.replaceAll(" ", "-")) == null) testsService.getMockTestExamGroup(booksTagDtl.syllabus)

                    [resourceList   : resList, title: "Free PDF " + booksMst.title + " download",
                     publisherName  : publishers.name, encryptPdfKey: encryptPdfStr, encryptEpubKey: encryptStr, bookLang: booksMst.language,
                     chapters       : dataProviderService.getChaptersList(new Long(params.bookId)), bookCover: booksMst.coverImage, previewChapterId: previewChapterId,
                     bookDescription: booksMst.description, seoDesc: seoDesc, keywords: keywords, examGroup: redisService.("mockTestExamGroup_" + booksTagDtl.syllabus.replaceAll(" ", "-"))]
                }
            }else{
                redirect(url: "https://www.wonderslate.com")
            }
        }
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def editQuiz(){
        session.setAttribute("htmlId",params.resId)
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        ["showHeader":"true","name":resourceDtl.resourceName,resourceDtl:resourceDtl]
    }
    def sampleUploadEpubs(){

    }

    @Transactional
    def uploadSplitEpub() {
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request
        MultipartFile file = multiRequest.getFile("file")

        File uploadDir = new File("supload/epub/"+params.bookId)
        if (!uploadDir.exists()) {
            uploadDir.mkdirs()
        }

        String filename = file.originalFilename.replaceAll("\\s+", "")
        File originalFile = new File(uploadDir.absolutePath + "/" + filename)
        file.transferTo(originalFile)

        try {
            resourceCreatorService.extractEpubContents(originalFile, uploadDir,params)
            dataProviderService.getChaptersList(new Long(params.bookId))
            dataProviderService.refreshCacheForPublishUnpublish("" + params.bookId,getSiteId(request))
        } catch (Exception e) {
            println("Error splitting EPUB: ${e.message}")
        }
        render "Epub uploaded successfully"
    }



    @Transactional
    def epubReader(){
        String serverURL = request.getScheme() + "://" + request.getServerName() +
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId))
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        def jsonChapterDetails = []
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        if(redisService.("allChapterDetails_"+params.bookId)==null) {
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
        }

        ['serverURL': serverURL,genericReader :  booksMst.genericReader||"80".equals(""+session["siteId"])?true:false,chapters:redisService.("allChapterDetails_" +params.bookId),
         title:booksMst.title]
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def createChildBook(){

        BooksMst sourceBooksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        Integer siteId = sourceBooksMst.siteId
        if(params.publisherId!=null) {
            Publishers publishers = dataProviderService.getPublisher(new Integer(params.publisherId))
            siteId = publishers.siteId
        }
                BookIdGenerator bookIdGenerator = new BookIdGenerator()
        bookIdGenerator.save(failOnError: true, flush: true)
        BooksMst destBooksMst = new BooksMst()
        destBooksMst.id=bookIdGenerator.id
        destBooksMst.publisherId = params.publisherId!=null?new Integer(params.publisherId):sourceBooksMst.publisherId
        destBooksMst.description = sourceBooksMst.description
        destBooksMst.title = sourceBooksMst.title
        destBooksMst.createdBy = springSecurityService.currentUser.username
        destBooksMst.siteId = siteId
        destBooksMst.price = sourceBooksMst.price
        destBooksMst.status = null
        destBooksMst.packageBookIds = sourceBooksMst.packageBookIds
        destBooksMst.authors = sourceBooksMst.authors
        destBooksMst.bookType = sourceBooksMst.bookType
        destBooksMst.coverImage = sourceBooksMst.coverImage
        destBooksMst.isbn = sourceBooksMst.isbn
        destBooksMst.language = sourceBooksMst.language
        destBooksMst.listprice = sourceBooksMst.listprice
        destBooksMst.showInLibrary = sourceBooksMst.showInLibrary
        destBooksMst.parentBookId = sourceBooksMst.id
        destBooksMst.validityDays = sourceBooksMst.validityDays
        destBooksMst.testsPrice = sourceBooksMst.testsPrice
        destBooksMst.upgradePrice = sourceBooksMst.upgradePrice
        destBooksMst.testsListprice = sourceBooksMst.testsListprice

        BooksMst booksMst1 = destBooksMst.clone()
        BooksMst booksMst2 = destBooksMst.clone()
        destBooksMst.save(failOnError: true, flush: true)
        booksMst1.wsuser.save(failOnError: true,flush: true)
        booksMst2.wsshop.save(failOnError: true,flush: true)
        BooksPermission booksPermission = new BooksPermission(bookId: destBooksMst.id, username: springSecurityService.currentUser.username)
        booksPermission.save(failOnError: true, flush: true)
        destBooksMst.refresh()

        //add to BooksDtl
        BooksDtl booksDtl = new BooksDtl(bookId: destBooksMst.id, masterBookId: sourceBooksMst.id)
        booksDtl.save(failOnError: true, flush: true)

        dataProviderService.getBooksListForUser()
        dataProviderService.refreshCacheForPublishUnpublish(""+destBooksMst.id,destBooksMst.siteId)
        dataProviderService.getWSUnpublishedMyBooks()
        dataProviderService.getBooksListForPubDeskForPublisher(destBooksMst.publisherId)

        def json = [status: "Success", bookId: destBooksMst.id]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def childBookInput(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        //get publisher list
        List publishers = Publishers.findAllBySiteIdInList([new Integer(100),new Integer(88)])
        [title:"Create Child Book",booksMst:booksMst,publishers:publishers]
    }


}
