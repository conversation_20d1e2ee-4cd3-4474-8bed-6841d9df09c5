package com.wonderslate.data

import com.wonderslate.shop.PdfExporterService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import grails.converters.JSON

class TheoryBooksController {
    TheoryBooksService theoryBooksService
    AutogptService autogptService
    def springSecurityService
    PdfExporterService pdfExporterService

    @Transactional
    def createChaptersFromSyllabus(){
        String bookId = params.bookId
        String syllabusText = params.syllabusText
        String subject = params.subject
        String university = params.university

        BooksDtl booksDtl = BooksDtl.findByBookId(new Long(bookId))
        if(booksDtl==null){
            booksDtl = new BooksDtl(bookId: new Long(bookId))
            booksDtl.save(failOnError: true, flush: true)
        }
        booksDtl.syllabusSubject = subject
        booksDtl.university = university
        booksDtl.save(failOnError: true, flush: true)


        Prompts prompts = Prompts.findByPromptType("metadataFromSyllabus")
        String promptText = prompts.basePrompt
        promptText = promptText.replace("SYLLABUSSUBJECT",subject)
        promptText = promptText.replace("UNIVERSITY",university)
        promptText = promptText.replace("SYLLABUSTEXT",syllabusText)
        def responseString = theoryBooksService.talkToGPT(promptText)
        def jsonResponse = new JsonSlurper().parseText( theoryBooksService.jsonCleaner(responseString.response))
        def chapters = jsonResponse.topics
         chapters.each { chapter ->
            println("chapter: " + chapter)
            def subtopicsData = chapter.subtopics ?: []
            ChaptersMst chaptersMst = new ChaptersMst(name: chapter.topicTitle, bookId: new Long(bookId))
            chaptersMst.save(failOnError: true, flush: true)
            subtopicsData.each { subtopic ->
                ChaptersSubtopicDtl chaptersSubtopicDtl = new ChaptersSubtopicDtl(chapterId: chaptersMst.id, title: subtopic.title,
                        learningObjective: subtopic.learningObjective, keyConcepts: subtopic.keyConcepts, bloomLevel: subtopic.bloomLevel,
                        prerequisites: subtopic.prerequisites, applications: subtopic.applications)
                chaptersSubtopicDtl.save(failOnError: true, flush: true)
            }
        }

        def json = [status: "OK",chapters:chapters]
        render json as JSON
    }

    @Transactional
    def theoryBookInput(){

        [title:"Theory Book Input"]
    }

    @Transactional
    def getExistingChapters(){
        String bookId = params.bookId
        BooksDtl booksDtl = BooksDtl.findByBookId(new Long(bookId))
        List<ChaptersMst> chapters = ChaptersMst.findAllByBookId(new Long(bookId), [sort: "sortOrder", order: "asc"])

        def chaptersData = chapters.collect { chapter ->
            def subtopics = []
            List subtopicsList = ChaptersSubtopicDtl.findAllByChapterId(chapter.id)
            subtopicsList.each { subtopic ->
                subtopics << [
                        title: subtopic.title,
                        learningObjective: subtopic.learningObjective,
                        keyConcepts: subtopic.keyConcepts,
                        bloomLevel: subtopic.bloomLevel,
                        prerequisites: subtopic.prerequisites,
                        applications: subtopic.applications
                ]
            }

            // Check if PYQs are already created for this chapter
            ResourceDtl pyqResource = ResourceDtl.findByChapterIdAndResTypeAndResourceName(chapter.id, "QA", "PYQs")
            def pyqStatus = [:]
            if (pyqResource != null) {
                // Count the number of PYQ questions
                def questionCount = ObjectiveMst.countByQuizId(new Integer(pyqResource.resLink))
                pyqStatus = [
                    created: true,
                    questionCount: questionCount
                ]
            } else {
                pyqStatus = [
                    created: false,
                    questionCount: 0
                ]
            }

            // Check if chapter content (Notes) is already created
            ResourceDtl notesResource = ResourceDtl.findByChapterIdAndResType(chapter.id, "Notes")
            def contentStatus = [:]
            if (notesResource != null) {
                contentStatus = [
                    created: true
                ]
            } else {
                contentStatus = [
                    created: false
                ]
            }

            return [
                id: chapter.id,
                topicTitle: chapter.name,
                subtopics: subtopics,
                pyqStatus: pyqStatus,
                contentStatus: contentStatus
            ]
        }

        def json = [status: "OK", chapters: chaptersData,
                    university: booksDtl?.university ?: "",
                    subject: booksDtl?.syllabusSubject ?: ""]
        render json as JSON
    }

    @Transactional
   def createPDF(){
       getPYQsForChapter(params)
        def json = createChapterContents(params)
        render json as JSON
    }

    @Transactional
    def getPYQsForChapter(params){
        return  theoryBooksService.getPYQsForChapter(params)

    }

    @Transactional
    def createChapterContents(params){
        return  theoryBooksService.createChapterContents(params)


    }
}
