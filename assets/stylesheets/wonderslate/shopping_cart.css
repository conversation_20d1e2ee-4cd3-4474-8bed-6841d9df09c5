@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart {
    padding-bottom: 150px;
  }
}
.shopping_cart h3 {
  margin-bottom: 20px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart h3 {
    font-size: 1.5rem;
  }
}
.shopping_cart h3 small {
  font-weight: 500;
  font-size: 18px;
}
.shopping_cart .cart_items .card {
  border-radius: 10px;
  box-shadow: 0 4px 10px #0000001A;
  min-height: 150px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .card {
    min-height: auto;
  }
}
.shopping_cart .cart_items .card:hover {
  box-shadow: 0 2px 4px #00000040;
}
.shopping_cart .cart_items .card:hover .book_image a img {
  box-shadow: 0 2px 4px #00000040;
}
.shopping_cart .cart_items .card:hover .book_desc h5 a {
  text-decoration: underline;
}
.shopping_cart .cart_items .book_image a {
  display: block;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
.shopping_cart .cart_items .book_image a:hover {
  opacity: 0.8;
}
.shopping_cart .cart_items .book_image img {
  width: 90px;
  height: 120px;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_image img {
    width: 75px;
    height: 100px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc {
    padding-right: 0;
    padding-left: 12px;
  }
}
.shopping_cart .cart_items .book_desc .book_title {
  min-height: 75px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc .book_title {
    min-height: 70px;
  }
}
.shopping_cart .cart_items .book_desc h5 {
  color: #444444;
  font-size: 16px;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc h5 {
    font-size: 15px;
    -webkit-line-clamp: 3;
  }
}
.shopping_cart .cart_items .book_desc h5 a {
  color: #444444;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
}
.shopping_cart .cart_items .book_desc h5 a:hover {
  opacity: 0.8;
}
.shopping_cart .cart_items .book_desc .book_publisher {
  color: #949494;
  margin-bottom: 0.25rem;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc .book_publisher {
    font-size: 13px;
  }
}
.shopping_cart .cart_items .book_desc .book_price {
  display: flex;
  margin: 3px 0 0;
  position: relative;
  z-index: 11;
  flex-wrap: wrap;
}
@media (max-width: 350px) {
  .shopping_cart .cart_items .book_desc .book_price {
    display: grid;
  }
}
.shopping_cart .cart_items .book_desc .book_price.mrp {
  margin: 0;
  line-height: normal;
  width: 100px;
}
.shopping_cart .cart_items .book_desc .book_price .offer_price {
  font-size: 18px;
  font-weight: 500;
  min-width: 55px;
  font-family: 'Rubik', sans-serif !important;
  margin-right: 0.75rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc .book_price .offer_price {
    font-size: 16px;
    min-width: 50px;
    margin-bottom: 5px;
  }
}
.shopping_cart .cart_items .book_desc .book_price .list_price {
  font-size: 15px;
  color: #CE0000;
  font-family: 'Rubik', sans-serif !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .book_desc .book_price .list_price {
    font-size: 14px;
  }
}
.shopping_cart .cart_items .book_desc .book_price .book_discount_btn {
  height: auto;
  border-radius: 3px;
  border: 1px dashed #d4d4d4;
  line-height: normal;
  color: #6C757D;
  box-shadow: 0 2px 4px #0000001A;
  font-weight: 500;
  white-space: normal;
  font-size: 13px;
  padding: 4px 20px;
}
.shopping_cart .cart_items .book_desc .book_price .discount_applied {
  color: #27AE60;
  font-weight: 500;
  min-height: 25px;
  white-space: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .delete_item {
    position: absolute;
    z-index: 1;
    right: 0;
    top: 0;
  }
}
.shopping_cart .cart_items .delete_item a {
  font-size: 11px;
  color: #CE0000;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .delete_item a {
    padding: 5px;
  }
}
.shopping_cart .cart_items .delete_item a:hover {
  color: #CE0000;
  text-decoration: none;
}
.shopping_cart .cart_items .delete_item a i {
  font-size: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_items .delete_item a i {
    font-size: 18px;
  }
}
.shopping_cart .cart_checkout {
  border-radius: 10px;
  box-shadow: 0 4px 10px #0000001A;
  position: sticky;
  top: 0;
  transition: all 0.5s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout {
    position: relative;
    top: 0 !important;
    padding: 0 !important;
    box-shadow: none;
  }
}
.shopping_cart .cart_checkout h5 {
  border-bottom: 1px dashed #e1e1e1;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout h5 {
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .shopping_cart .cart_checkout h5 i {
    font-size: 16px;
  }
}
.shopping_cart .cart_checkout .text-right {
  font-weight: 600;
  font-family: 'Rubik', sans-serif !important;
}
.shopping_cart .cart_checkout .total_discount_price {
  color: #27AE60;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .total_discount_price {
    position: absolute;
    left: 0;
    right: 0;
    margin-bottom: -5px;
  }
}
.shopping_cart .cart_checkout .total_discount_price #totalDiscountPercent {
  width: 45px;
  height: 45px;
  position: relative;
  z-index: -1;
  margin: -40px -5px 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .total_discount_price #totalDiscountPercent {
    width: 40px;
    height: 40px;
  }
}
.shopping_cart .cart_checkout .total_discount_price .saved_price {
  font-family: 'Rubik', sans-serif !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .order_summary {
    position: fixed;
    width: 100%;
    padding: 14px;
    bottom: -100%;
    left: 0;
    right: 0;
    z-index: 9991;
    background: #f9f9f9;
    border-radius: 7px 7px 0 0;
    border-bottom: 1px solid #f5f5f5;
    box-shadow: 0 -0.5rem 1rem #0000001A;
    transition: all 0.3s linear;
  }
  .shopping_cart .cart_checkout .order_summary.showing {
    bottom: 130px;
  }
}
.shopping_cart .cart_checkout .order_summary p {
  margin-bottom: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .order_summary p {
    margin-bottom: 0.5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .cart_buttons {
    position: fixed;
    bottom: 0;
    z-index: 9992;
    background-color: #FFFFFF;
    width: 100%;
    height: 130px;
    left: 0;
    right: 0;
    padding: 0 14px 10px;
    box-shadow: 0 -0.5rem 1rem #0000001A;
    border-top: 1px solid #DDD;
  }
}
.shopping_cart .cart_checkout .cart_buttons a {
  width: 100%;
  border: 1px solid transparent;
  border-radius: 7px;
  box-shadow: 0 2px 4px #0000001A;
  font-weight: 500;
}
.shopping_cart .cart_checkout .cart_buttons a.proceed_btn {
  border: none;
  background-color: #F79420;
  color: #FFFFFF;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  font-size: 1.25rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .cart_buttons a.proceed_btn {
    height: 40px;
    line-height: normal;
  }
}
.shopping_cart .cart_checkout .cart_buttons a.proceed_btn:after {
  content: '';
  position: absolute;
  top: -10px;
  left: -50%;
  z-index: 10;
  display: block;
  width: 30px;
  height: 100px;
  opacity: 0.7;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-40deg);
  transform: skewX(-40deg);
  -webkit-animation: shine 3s infinite;
  animation: shine 3s infinite;
  filter: blur(5px);
}
.shopping_cart .cart_checkout .cart_buttons a.continue_shop_btn {
  color: #6C757D;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .mobile_summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.shopping_cart .cart_checkout .mobile_total,
.shopping_cart .cart_checkout .mobile_view_summary {
  margin-bottom: 0;
  text-align: center;
  padding: 3px 0;
  position: relative;
  z-index: 1;
  font-weight: 500;
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .cart_checkout .mobile_total,
  .shopping_cart .cart_checkout .mobile_view_summary {
    display: block;
  }
}
.shopping_cart .cart_checkout .mobile_total span,
.shopping_cart .cart_checkout .mobile_view_summary span {
  font-weight: normal;
}
.shopping_cart .cart_checkout .mobile_total {
  font-size: 12px;
  font-family: 'Rubik', sans-serif !important;
  line-height: normal;
}
.shopping_cart .cart_checkout .mobile_view_summary a.open_order_summary {
  font-size: 12px;
  display: flex;
  text-decoration: none;
  box-shadow: none;
  font-weight: 400;
  align-items: center;
  line-height: 1;
  color: #949494;
}
.shopping_cart #discountAnimation {
  position: fixed;
  bottom: 0;
  left: 0;
  top: 0;
  right: 0;
  width: 70%;
  height: 70%;
  margin: auto;
  display: none;
  z-index: 12;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart #discountAnimation {
    position: fixed;
    bottom: -100px;
    z-index: 9993;
    width: 100%;
    height: 100%;
  }
}
.shopping_cart .order_information ul {
  padding: 10px 0 0;
  list-style: none;
  margin-bottom: 0;
}
.shopping_cart .order_information ul li {
  line-height: normal;
  padding-bottom: 15px;
  color: #6C757D;
  display: flex;
  align-items: center;
}
.shopping_cart .order_information ul li span {
  display: inline-block;
}
.shopping_cart .order_information ul li span:nth-child(1) {
  width: 180px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .order_information ul li span:nth-child(1) {
    width: 150px;
  }
}
.shopping_cart .order_information ul li span:nth-child(2) {
  width: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .order_information ul li span:nth-child(2) {
    width: 20px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .shopping_cart .order_information .use_ebooks_text {
    text-align: center;
  }
}
.shopping_cart .order_information .use_ebooks_text .tree_emoji {
  font-size: 25px;
}
#showEmptyCart .empty_cart {
  background: #FFFFFF;
  min-height: 400px;
  border-radius: 10px;
}
#showEmptyCart .empty_cart #emptyCartBox {
  width: 250px;
  height: 250px;
  margin: 0 auto;
  position: relative;
  top: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #showEmptyCart .empty_cart #emptyCartBox {
    width: 200px;
    height: 200px;
  }
}
#showEmptyCart .empty_cart h6 {
  line-height: normal;
}
#showEmptyCart .empty_cart .shopnow_btn {
  border: none;
  border-radius: 4px;
  box-shadow: 0 2px 4px #0000001A;
  font-weight: 500;
  background-color: #F79420;
  color: #FFFFFF;
  width: auto;
  height: 35px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  margin-top: 7px;
}
.fixed-navbar .shopping_cart .cart_checkout {
  top: 125px;
  transition: all 0.3s ease;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  footer,
  .footer-menus,
  .prepjoy_cta {
    display: none;
  }
}
.shimmer_animated {
  background: #f6f7f8;
  background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-repeat: no-repeat;
  background-size: 800px 150px;
  display: inline-block;
  position: relative;
  border-radius: 5px;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
}
.shimmer_animated * {
  opacity: 0;
}
@keyframes shine {
  100% {
    left: 125%;
  }
}
@-webkit-keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
.arihant .shopping_cart,
.whitelabel-site .shopping_cart {
  margin-bottom: 50px;
}
.radian_books #showEmptyCart .empty_cart .shopnow_btn,
.oswaal_books #showEmptyCart .empty_cart .shopnow_btn {
  background-color: #000000;
}
.radian_books .cart_checkout .cart_buttons a.proceed_btn,
.oswaal_books .cart_checkout .cart_buttons a.proceed_btn {
  background-color: #000000;
}
.edugorilla #showEmptyCart .empty_cart .shopnow_btn {
  background-color: #C9302C;
}
.edugorilla .cart_checkout .cart_buttons a.proceed_btn {
  background-color: #C9302C;
}
.shippingAdressWrapper {
  background: #fff;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 1rem;
}
.shippingAdressWrapper h3 {
  margin-bottom: 0 !important;
}
.rechargeCartCover {
  width: 90px;
  height: 120px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .printBookMobileCheckout {
    padding-bottom: 0 !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .printBookMobilePB {
    padding-bottom: 150px!important;
  }
}
