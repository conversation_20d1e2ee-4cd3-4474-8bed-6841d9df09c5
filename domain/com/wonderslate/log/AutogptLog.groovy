package com.wonderslate.log

class AutogptLog {

    String createdBy
    Date dateCreated
    Date dateStarted
    Date dateCompleted
    String gptStatus
    Long chapterId
    Integer attempts
    String createSnapshot
    Integer timesAborted
    Integer serverIndex


    static constraints = {
        dateCompleted blank: true, nullable: true
        gptStatus blank: true, nullable: true
        attempts blank: true, nullable: true
        dateStarted blank: true, nullable: true
        createSnapshot blank: true, nullable: true
        timesAborted blank: true, nullable: true
        serverIndex blank: true, nullable: true
    }
    static mapping = {
        datasource 'wslog'
    }
}
